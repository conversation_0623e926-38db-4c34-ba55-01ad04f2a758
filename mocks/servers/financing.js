const { randomIntFromInterval } = require('../helpers/generate-random-number');

const financing = require('./../db/financing/');

const financingServer = (server) => {
	server.get('/financing/:productId/list', (req, res) => {
		return res.json({
			...financing['financing-list']
		});
	});

	server.get('/financing/:productId/list/receivables', (req, res) => {
		return res.json({
			...financing['financing-list-receivables']
		});
	});

	server.post('/onboarding/:productId/financing/find', (req, res) => {
		const status = req.query.status ?? 'completed';

		if (status === 'invalid') {
			res.status(400);
		}

		return res.json({
			...financing[`onboarding-${status}`]
		});
	});

	server.post('/financing/simulate/price', (req, res) => {
		const payload = req.body;

		const simulationsResponse = payload.simulations.map((simulation) => {
			const charges = randomIntFromInterval(simulation.value / 2, simulation.value / 4);
			const tax = parseFloat(`${randomIntFromInterval(0, 9)}.${randomIntFromInterval(0, 9)}${randomIntFromInterval(0, 9)}`);
			const tir = `${randomIntFromInterval(10, 99)}.${randomIntFromInterval(10e10, 99e10)}`;

			return {
				receivableId: simulation.receivableId,
				freeValue: simulation.value,
				netValue: simulation.value + charges,
				charges,
				tax,
				tir
			};
		});

		return res.json({
			simulations: simulationsResponse
		});
	});

	server.post('/financing/:productId/eligibility', (req, res) => {
		const payload = req.body;

		const eligibles = [];

		payload.notas.forEach((note, index) => {
			const isFirstInteraction = index === 0;
			const isLastInteraction = payload.notas.length - 1 === index;
			const defaultCondition = !eligibles.length && isLastInteraction;

			let isEligible = defaultCondition || Boolean(randomIntFromInterval(0, 1));
			isEligible = req.query.forceZero ? false : isEligible;
			isEligible = req.query.forceAll || isEligible;
			if (req.query.forceSome) {
				if (payload.notas.length >= 3) {
					isEligible = (!isFirstInteraction && !isLastInteraction);
				} else {
					isEligible = isFirstInteraction;
				}
			}

			if (!isEligible) {
				return;
			}

			const eligibleNote = {
				numberOrder: note.receivableNumber,
				due: new Intl.DateTimeFormat(['pt-br']).format(new Date()),
				amount: randomIntFromInterval(10000, 150000)
			};

			eligibles.push(eligibleNote);
		});

		const hasError = 0;
		const hasServerError = 0;

		if(hasServerError){
			return res.status(500).json({
				timestamp: +new Date(),
				status: 500,
				error: 'Internal Server Error',
				path: '/financing/80000/eligibility'
			});
		}

		if (hasError) {
			return res.status(400).json({
				queueId: null,
				quantityApproved: 0,
				total: 0,
				eligibles: null,
				error: 'Não foi possível enviar para a fila de execução'
			});
		}

		return res.json({
			queueId: (payload.simulacao) ? null : randomIntFromInterval(10000, 99999).toString(),
			quantityApproved: eligibles.length,
			total: payload.notas.length,
			eligibles,
			error: null
		});
	});

	server.post('/financing/:productId/draft/new', (req, res) => {
		if (parseInt(req.query.step, 10) === 1) {
			return res.json({
				...financing['financing-draft-incomplete']
			});
		}

		if (parseInt(req.query.step, 10) === 2) {
			return res.json({
				...financing['financing-draft-complete']
			});
		}

		return res.json({
			...financing['financing-draft-creation']
		});
	});

	server.post('/financing/:productId/draft/:draftId/step/:stepId', (req, res) => {
		const stepDictionary = {
			1: 'financing-draft-incomplete',
			2: 'financing-draft-complete'
		};

		return res.json({
			...financing[stepDictionary[req.params.stepId]]
		});
	});
};

module.exports = financingServer;
