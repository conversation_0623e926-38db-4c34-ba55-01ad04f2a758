const jsonServer = require('json-server');

const allProducts = require('./db/allProducts.json');
const mockAvailableCash = require('./db/availableCash/');
const mockClientes = require('./db/clientes/');
const financing = require('./db/financing/');
const mockHelloworld = require('./db/helloWorld/');
const mockHolidays = require('./db/holidays/');
const mockOperations = require('./db/operations/');
const receivableBanks = require('./db/receivableBanks/');
// const mockElegibilidade = require('./db/eligibility/');
// const mockCessionSimulation = require('./db/cessionSimulation');
const customRoutes = require('./routes.json');
const servers = require('./servers/');

const server = jsonServer.create();
const router = jsonServer.router({
	...mockClientes,
	...allProducts,
	...mockHolidays,
	...mockHelloworld,
	...mockOperations,
	...mockAvailableCash,
	...receivableBanks,
	// ...mockElegibilidade,
	// ...mockCessionSimulation,
	...receivableBanks,
	...financing
});
const middlewares = jsonServer.defaults();
server.use(jsonServer.bodyParser);


server.use(middlewares);
server.use(jsonServer.bodyParser);
server.post('/clientes/:id/empresa', (req, res) => {
	return res.json({
		data: {},
	});
});
/* server.post('/eligibility', (req, res) => {
	const {
		typeOperation,
		productId,
		limit,
		dateDisbursement,
		valueDisbursement,
		receivablesBank,
	} = req.body;
	const id = 1; //provisório, apenas para usar corretamente o get no método abaixo
	const newEligibility = {
		id,
		typeOperation,
		productId,
		limit,
		dateDisbursement,
		valueDisbursement,
		receivablesBank,
	};
	mockElegibilidade.eligibility.data.push(newEligibility);
	return res.json({ id });
});

server.get('/cessionSimulation/:id', (req, res) => {
	const id = Number(req.params.id);
	const simulation = mockCessionSimulation.cessionSimulation.data.find(sim => sim.id === id);

	setTimeout(() => {
		if (simulation) {
			return res.json({
				message: simulation.message,
			});
		} else {
			return res
				.status(404)
				.json({ message: 'Não foi encontrada simulação para o ID fornecido' });
		}
	}, 2000);
}); */

server.put('/v1/available_cash/products', (req, res) => {
	return res.json({
		data: {},
	});
});

server.post('/v1/holidays', (req, res) => {
	return res.json({
		...mockHolidays['holidays-created'],
	});
});

server.delete('/v1/holidays/:id', (req, res) => {
	return res.json({
		...mockHolidays['holidays-deleted'],
	});
});

server.patch('/v1/holidays/:id', (req, res) => {
	return res.json({
		...mockHolidays['holidays-created'],
	});
});

server.delete('/v1/products/:productId/operation/resale/:operationId', (req, res) => {
	return res.json({
		status: 'ok',
	});
});

server.patch(
	'/v1/products/:productId/operation/resale/:operationId/:currentStep/:nextStep',
	(req, res) => {
		return res.json({
			...mockOperations['operation-current-step-next-step'],
		});
	}
);

server.get(
	'/v1/products/:productId/download/excel/invoices/by/operation/:operationId',
	async (req, res) => {
		const file = `${__dirname}/files/mock-file-export.xlsx`;
		await new Promise(resolve => {
			setTimeout(() => {
				resolve();
			}, 2000);
		});
		res.download(file);
	}
);

servers.financing(server);
servers.financingMyClients(server);

server.use(jsonServer.rewriter(customRoutes));
server.use(router);

server.listen(3000, () => {
	console.log('JSON Server is running! port 3000');
});
