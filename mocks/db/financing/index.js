const financingDetail = require('./financing-detail.json');
const financingDraftComplete = require('./financing-draft-complete.json');
const financingDraftCreation = require('./financing-draft-creation.json');
const financingDraftIncomplete = require('./financing-draft-incomplete.json');
const financingList = require('./financing-list.json');
const financingSummary = require('./financing-summary.json');
const myClientsFinancingDetail = require('./my-clients/financing-detail.json');
const myClientsfinancingList = require('./my-clients/financing-list.json');
const myClientsFinancingSummary = require('./my-clients/financing-summary.json');
const myClientsReceivablesList = require('./my-clients/receivables-list.json');
const myClientsReceivablesSummary = require('./my-clients/receivables-summary.json');
const onboardingAnalysis = require('./onboarding/onboarding-analysis.json');
const onboardingCompleted = require('./onboarding/onboarding-completed.json');
const onboardingIncomplete = require('./onboarding/onboarding-incomplete.json');
const onboardingInvalid = require('./onboarding/onboarding-invalid.json');
const receivablesList = require('./receivable-list.json');
const receivablesSummary = require('./receivables-summary.json');

module.exports = {
	'financing-list-receivables': receivablesList,
	'my-clients-receivables-list': myClientsReceivablesList,
	'financing-list': financingList,
	'my-clients-financing-list': myClientsfinancingList,
	'financing-draft-creation': financingDraftCreation,
	'financing-draft-incomplete': financingDraftIncomplete,
	'financing-draft-complete': financingDraftComplete,
	'financing-summary': financingSummary,
	'financing-detail': financingDetail,
	'onboarding-completed': onboardingCompleted,
	'onboarding-incomplete': onboardingIncomplete,
	'onboarding-analysis': onboardingAnalysis,
	'onboarding-invalid': onboardingInvalid,
	'receivables-summary': receivablesSummary,
	'my-clients-financing-summary': myClientsFinancingSummary,
	'my-clients-receivables-summary': myClientsReceivablesSummary,
	'my-clients-financing-detail': myClientsFinancingDetail
};
