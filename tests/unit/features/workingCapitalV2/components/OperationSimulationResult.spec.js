import { shallowMount, createLocalVue } from '@vue/test-utils';
import Vuex from 'vuex';

import OperationSimulationResult from '@/features/workingCapitalV2/components/OperationSimulationResult';

jest.mock('@tanstack/vue-query', () => ({
	useMutation: () => ({
		isLoading: false,
		isError: false,
		mutate: jest.fn(),
	}),
	useQuery: () => ({
		data: {},
		isLoading: false,
		isFetching: false,
		isError: false,
	}),
}));

jest.mock('@/composible', () => ({
	useModal: () => ({
		isOpenModal: false,
		onOpenModal: jest.fn(),
		onCloseModal: jest.fn(),
	}),
	useRouter: () => ({
		push: jest.fn(),
	}),
	useSelectedProductId: () => ({
		value: 1,
	}),
}));

jest.mock('@/features/workingCapitalV2/composables', () => ({
	useCampaigns: () => ({
		chosenCampaign: {
			value: {
				campaignAndCommercial_product: 'test-campaign',
				tax: 10,
				startCessionExpiration: '2024-01-01',
				endCessionExpiration: '2024-12-31',
			},
		},
	}),
	useWorkingCapitalCreateForm: () => ({
		form: {
			value: {
				supplierId: 1,
				supplierBankAccount: 1,
				supplierTransferDate: '2024-01-01',
				dueDate: '2024-12-31',
			},
		},
		isValidForm: true,
		totalToPay: 1000,
		canAdvance: true,
		hasPreEligibilityApproved: false,
		openCancelModal: jest.fn(),
		simulationId: { value: '123' },
		setSimulationId: jest.fn(),
	}),
}));

jest.mock('@/features/workingCapitalV2/helpers/handleGenericError', () => ({
	handleGenericError: jest.fn(() => ['Generic error message']),
}));

jest.mock('@/features/workingCapitalV2/helpers/handlePreEligibilityError', () => ({
	handlePreEligibilityError: jest.fn(() => ['Pre-eligibility error message']),
}));

const localVue = createLocalVue();
localVue.use(Vuex);

let store = new Vuex.Store({
	modules: {
		dashboards: {
			namespaced: true,
			getters: {
				yourLimits: () => [],
				yourLimitsRequestStatus: () => 'IDLE',
				currentLimit: () => {},
			},
			actions: {
				selectCurrentLimit: jest.fn(),
				cleanCurrentLimit: jest.fn(),
				fetchYourLimits: jest.fn(),
			},
		},
	},
});

describe('OperationSimulationResult component', () => {
	let wrapper;

	beforeEach(() => {
		wrapper = shallowMount(OperationSimulationResult, {
			localVue,
			store,
			propsData: {
				operationTypeId: 2,
			},
		});
	});

	test('OperationSimulationResult created', () => {
		expect(wrapper).toBeDefined();
	});

	describe('handleSimulatePreEligibilityError function', () => {
		test('should use handleGenericError and generic modal for alphanumeric codes', () => {
			const error = {
				response: {
					data: {
						errors: ['Error message (ABC123)', 'Another error (DEF456)']
					}
				}
			};

			const result = wrapper.vm.handleSimulatePreEligibilityError(error);

			expect(result).toEqual({
				messages: ['Generic error message'],
				useGenericModal: true
			});
		});

		test('should use handlePreEligibilityError and legacy modal for numeric codes', () => {
			const error = {
				response: {
					data: {
						errors: ['Error message (123)', 'Another error (456)']
					}
				}
			};

			const result = wrapper.vm.handleSimulatePreEligibilityError(error);

			expect(result).toEqual({
				messages: ['Pre-eligibility error message'],
				useGenericModal: false
			});
		});

		test('should use handlePreEligibilityError and legacy modal for no codes', () => {
			const error = {
				response: {
					data: {
						errors: ['Error message without codes', 'Another error without codes']
					}
				}
			};

			const result = wrapper.vm.handleSimulatePreEligibilityError(error);

			expect(result).toEqual({
				messages: ['Pre-eligibility error message'],
				useGenericModal: false
			});
		});

		test('should use handlePreEligibilityError and legacy modal for empty errors', () => {
			const error = {
				response: {
					data: {
						errors: []
					}
				}
			};

			const result = wrapper.vm.handleSimulatePreEligibilityError(error);

			expect(result).toEqual({
				messages: ['Pre-eligibility error message'],
				useGenericModal: false
			});
		});

		test('should use handlePreEligibilityError and legacy modal when no response data', () => {
			const error = {};

			const result = wrapper.vm.handleSimulatePreEligibilityError(error);

			expect(result).toEqual({
				messages: ['Pre-eligibility error message'],
				useGenericModal: false
			});
		});
	});
});
