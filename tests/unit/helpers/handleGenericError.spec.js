import { ERROR_MESSAGES } from "../../../src/features/workingCapitalV2/constants/errorMessages";
import { handleGenericError } from "../../../src/features/workingCapitalV2/helpers/handleGenericError";

describe('handleGenericError', () => {
    it('should return generic message when no errors are provided', () => {
        const error = { response: { data: { errors: [] } } };
        const result = handleGenericError(error);
        expect(result).toEqual([ERROR_MESSAGES.UNEXPECTED_ERROR]);
    });

    it('should return custom fallback message when provided', () => {
        const error = { response: { data: { errors: [] } } };
        const result = handleGenericError(error, "Erro customizado");
        expect(result).toEqual(["Erro customizado"]);
    });

    it('should return generic message with alphanumeric codes when errors contain codes', () => {
        const error = { response: { data: { errors: ["Erro no fornecedor (F001)", "Documento inválido (DOC123)"] } } };
        const result = handleGenericError(error);
        expect(result).toEqual([`${ERROR_MESSAGES.GENERIC_NOT_FOUND} (F001) (DOC123)`]);
    });

    it('should return generic message with numeric codes when errors contain numeric codes', () => {
        const error = { response: { data: { errors: ["Erro no segmento (27)", "Cliente inválido (38)"] } } };
        const result = handleGenericError(error);
        expect(result).toEqual([`${ERROR_MESSAGES.GENERIC_NOT_FOUND} (27) (38)`]);
    });

    it('should return custom fallback message with codes when provided', () => {
        const error = { response: { data: { errors: ["Erro no fornecedor (F001)"] } } };
        const result = handleGenericError(error, "Erro ao carregar dados");
        expect(result).toEqual([`${ERROR_MESSAGES.GENERIC_NOT_FOUND} (F001)`]);
    });

    it('should return fallback message when no codes are found', () => {
        const error = { response: { data: { errors: ["Erro sem código"] } } };
        const result = handleGenericError(error, "Erro ao carregar dados");
        expect(result).toEqual(["Erro ao carregar dados"]);
    });

    it('should handle mixed errors with and without codes', () => {
        const error = { response: { data: { errors: ["Erro sem código", "Erro com código (ABC123)"] } } };
        const result = handleGenericError(error);
        expect(result).toEqual([`${ERROR_MESSAGES.GENERIC_NOT_FOUND} (ABC123)`]);
    });

    it('should handle error without response structure', () => {
        const error = {};
        const result = handleGenericError(error);
        expect(result).toEqual([ERROR_MESSAGES.UNEXPECTED_ERROR]);
    });

    it('should handle error with custom fallback and no response structure', () => {
        const error = {};
        const result = handleGenericError(error, "Erro customizado");
        expect(result).toEqual(["Erro customizado"]);
    });
});