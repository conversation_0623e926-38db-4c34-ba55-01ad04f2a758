import { addDecorator } from '@storybook/vue';
import vuetify from './vuetify_storybook';

import Vue from 'vue';
import installComponents from '@farm-investimentos/front-mfe-libs-ts/helpers/installComponents';
import '@farm-investimentos/front-mfe-components/dist/front-mfe-components.css';
import '@farm-investimentos/front-mfe-components/src/scss/utils.scss';
import '@farm-investimentos/front-mfe-components/src/scss/cssVariablesGenerator.scss';

import * as farmComponents from '@farm-investimentos/front-mfe-components';
installComponents(Vue, farmComponents);

addDecorator(() => ({
	vuetify,
	template: `
    <v-app>
      <farm-box>
        <story/>
      </farm-box>
    </v-app>
    `,
}));