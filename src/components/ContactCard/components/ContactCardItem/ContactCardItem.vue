<template>
	<p class="contact-card__item">
		<slot name="icon">
			<farm-icon size="13" color="secondary-green" variation="darken" class="mr-1">
				{{ icon }}
			</farm-icon>
		</slot>
		<slot />
	</p>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		icon: {
			type: String,
			default: '',
		},
	},
	setup() {
		return {};
	},
});
</script>

<style lang="scss" scoped>
@import './ContactCardItem.scss';
</style>
