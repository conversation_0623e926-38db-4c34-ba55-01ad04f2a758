<template>
	<farm-card class="contact-card">
		<farm-card-content class="d-flex items-center contact-card__content">
			<slot name="text">
				<farm-subtitle variation="regular" type="2" class="mb-0">
					Em caso de dúvidas entre em contato através dos nossos canais de atendimento:
				</farm-subtitle>
			</slot>
			<div class="d-flex">
				<ContactCardItem icon="email-outline">
					<a href="mailto:<EMAIL>"><EMAIL></a>
				</ContactCardItem>
				<ContactCardItem icon="whatsapp">
					<a href="tel:+551135067800">(11) 3506-7800</a>
				</ContactCardItem>
			</div>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import ContactCardItem from './components/ContactCardItem';

export default defineComponent({
	components: {
		ContactCardItem,
	}
});
</script>

<style lang="scss" scoped>
@import './ContactCard.scss';
</style>
