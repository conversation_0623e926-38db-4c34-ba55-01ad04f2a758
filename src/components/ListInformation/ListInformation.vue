<template>
	<div class="list-information">
		<farm-idcaption
			tooltipColor="gray"
			v-for="(item, index) in data"
			:key="`list-information-${item.id}-${index}`"
			:icon="item.icon"
			:class="{
				'list-information-item': true,
				'with-line': index !== data.length - 1,
			}"
		>
			<template v-slot:title v-if="item.header.title">
				{{ item.header.title || 'Carregando...' }}
			</template>
			<template v-slot:subtitle>
				{{ item.subtitle }}: {{ item.value || 'Carregando...' }}
			</template>
		</farm-idcaption>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { HeaderFormTypes } from './types';

export default defineComponent({
	props: {
		data: {
			type: Array as PropType<Array<HeaderFormTypes>>,
			default: () => [],
		},
	},
});
</script>

<style lang="scss" scoped>
@import './ListInformation';
</style>
