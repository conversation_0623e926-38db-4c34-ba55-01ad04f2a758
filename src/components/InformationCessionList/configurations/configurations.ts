export const informationCessionConfiguration = [
	{
		id: '1',
		title: 'Tipo de Operação',
		value: '',
		copyText: '',
		hasTooltip: false,
		tooltipText: '',
		titleTooltip: '',
		textTooltip: '',
		icon: 'alert-circle-outline',
	},
	{
		id: '2',
		title: 'Produto',
		value: '',
		copyText: '',
		hasTooltip: false,
		tooltipText: '',
		titleTooltip: '',
		textTooltip: '',
	},
	{
		id: '3',
		title: 'Limite Disponível',
		value: '',
		copyText: '',
		hasTooltip: true,
		titleTooltip: 'Limite Disponível',
		textTooltip: 'O limite disponível considera o valor livre para calcular a cessão.',
	},
	{
		id: '4',
		title: 'Data do Desembolso',
		value: '',
		copyText: '',
		hasTooltip: true,
		titleTooltip: 'Data do Desembolso',
		textTooltip: 'Data prevista para o desembolso.',
	},
	{
		id: '5',
		title: 'Desembolso Previsto',
		value: '',
		copyText: '',
		hasTooltip: true,
		titleTooltip: 'Desembolso Previsto',
		textTooltip:`Valor previsto para desembolso considerando a soma
					total do valor líquido dos recebíveis selecionados.
					Este valor pode ser afetado conforme o tipo de garantia
					aplicado na operação de cessão.`,
	},
];
