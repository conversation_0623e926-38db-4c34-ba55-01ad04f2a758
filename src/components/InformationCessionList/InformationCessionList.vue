<template>
	<div class="information-cession-list">
		<farm-idcaption
			tooltipColor="gray"
			v-for="(item, index) in data"
			:key="`information-cession-list-${item.id}-${index}`"
			:copyText="''"
			:icon="item.icon || ''"
			:class="{
				'information-cession-list-item': true,
				'with-line': index !== data.length - 1,
			}"
		>
			<template v-slot:title>
				<div :class="index === 0 ? 'ml-2' : 'ml-1'">
					{{ item.title }}
					<farm-tooltip class="ml-1" v-if="item.hasTooltip">
						<farm-caption variation="semiBold" color="white">
							{{ item.titleTooltip }}
						</farm-caption>
						<farm-caption variation="regular" color="white">
							{{ item.textTooltip }}
						</farm-caption>
						<template v-slot:activator>
							<farm-icon size="sm" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</div>
			</template>
			<template v-if="item.value" v-slot:subtitle>
				<div :class="index === 0 ? 'ml-2' : 'ml-1'">
					{{ item.value }}
				</div>
			</template>
		</farm-idcaption>

		<div class="mx-3 information-cession-line" v-if="edit"></div>
		<div class="information-cession-edit" v-if="edit">
			<farm-btn plain @click="onClickEdit" :disabled="disabledButtonEdit">
				<farm-icon color="primary" size="24px">pencil</farm-icon>
				Data e Valor
			</farm-btn>
		</div>

		<div class="last">
			<div class="d-flex flex-column align-end">
				<farm-caption bold class="mr-2">
					{{ dataRight.title }}
					<farm-tooltip class="ml-1" v-if="dataRight.hasTooltip">
						<farm-caption variation="semiBold" color="white">
							{{ dataRight.titleTooltip }}
						</farm-caption>
						<farm-caption variation="regular" color="white">
							{{ dataRight.textTooltip }}
						</farm-caption>
						<template v-slot:activator>
							<farm-icon size="sm" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</farm-caption>
				<farm-idcaption
					:noHeight="true"
					:link="true"
					copyText=""
					@onLinkClick="onClickDisbursement(dataRight)"
				>
					<template v-slot:title>
						<farm-caption bold color="primary">
							{{ valueDisbursement }}
						</farm-caption>
					</template>
				</farm-idcaption>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue';

import { informationCessionConfiguration } from './configurations';

export default defineComponent({
	name: 'information-cession-list',
	props: {
		typeOperation: {
			type: [String, Number],
			require: true,
		},
		productName: {
			type: [String, Number],
			require: true,
		},
		limit: {
			type: [String, Number],
			require: true,
		},
		dateDisbursement: {
			type: [String, Number],
			require: true,
		},
		valueDisbursement: {
			type: [String, Number],
			require: true,
		},
		edit: {
			type: Boolean,
			default: false,
		},
		disabledButtonEdit: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const data = ref([]);
		const dataRight = ref(null);
		const { dateDisbursement, limit, productName, typeOperation, valueDisbursement } =
			toRefs(props);

		const keys = [
			'typeOperation',
			'productName',
			'limit',
			'dateDisbursement',
			'valueDisbursement',
		];
		const newConfiguration = informationCessionConfiguration.map((item, index) => {
			return {
				...item,
				value: props[keys[index]],
			};
		});

		data.value = newConfiguration.slice(0, 4);
		dataRight.value = {
			...newConfiguration[newConfiguration.length - 1],
		};

		function onClickDisbursement(item): void {
			emit('onClickDisbursement', item);
		}

		function onClickEdit(): void {
			emit('onClickEdit');
		}

		watch(dateDisbursement, newData => {
			data.value[3].value = newData;
		});

		watch(limit, newData => {
			data.value[2].value = newData;
		});

		watch(productName, newData => {
			data.value[1].value = newData;
		});

		watch(typeOperation, newData => {
			data.value[0].value = newData;
		});

		watch(
			() => valueDisbursement.value,
			newData => {
				dataRight.value.value = newData;
			}
		);

		return {
			data,
			dataRight,
			onClickDisbursement,
			onClickEdit,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './InformationCessionList';
</style>
