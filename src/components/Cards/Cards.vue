<template>
	<farm-card>
		<farm-card-content gutter="md">
			<slot name="header"></slot>
		</farm-card-content>
		<farm-line noSpacing />
		<farm-card-content gutter="md" :background="backgroundContent">
			<slot name="body"></slot>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name:"cards",
	props:{
		backgroundContent:{
			type: String,
			default: 'base'
		}
	}
});
</script>

<style lang="scss" scoped>
@import './Cards';
</style>
