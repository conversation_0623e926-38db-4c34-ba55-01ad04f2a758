<template>
	<farm-modal
		v-model="value"
		:offsetTop="48"
		:offsetBottom="76"
		size="md"
		@onClose="handleClose"
		:style="{ overflowY: 'hidden', overflowX: 'hidden' }"

	>
		<template v-slot:header>
			<farm-dialog-header title="Informações Gerais do Recebível" @onClose="handleClose" />
			<farm-line class="mb-2 modal-line" />
		</template>

		<template v-slot:content >
			<farm-row class="mt-3" :style="{ overflowY: 'hidden', overflowX: 'hidden', width: '100%', maxWidth: '100%' }">
				<farm-col cols="12" :style="{ maxWidth: '100%', overflow: 'hidden' }">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Número:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.number || '-' }}
						</farm-caption>
						<quota v-if="data.totalParts > 1" class="ml-3" small />
					</div>
				</farm-col>
			</farm-row>

			<farm-row v-if="data.totalParts > 1">
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Parcelas:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.part }}/{{ data.totalParts }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex">
						<farm-caption variation="regular" color="gray" class="mr-1">
							CFOP:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.cfopCode || data.cfop || '-' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<div class="clipboard-content">
				<div class="d-flex mb-2 position-relative">
					<farm-caption variation="regular" color="gray" class="mr-1">
						Chave de Acesso da Nfe:
					</farm-caption>
					<farm-caption variation="medium" color="gray" color-variation="darken">
						{{ data.danfe || '-' }}
					</farm-caption>
					<farm-copytoclipboard
						v-show="data.danfe"
						:toCopy="data.danfe ? data.danfe : ''"
						tooltipColor="gray"
						successMessage="Chave de Acesso copiada para área de transferência!"
						class="copy-button"
					/>
				</div>
			</div>

			<farm-line class="mb-2 modal-line" />

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption
							variation="regular"
							color="gray"
							class="mr-1 mb-0"
							:class="{ 'mt-1': !data.hasContactInfo }"
						>
							Sacado:
						</farm-caption>
						<farm-caption
							variation="medium"
							color="gray"
							color-variation="darken"
							class="mb-0 align-bottom"
							:class="{ 'mt-1': !data.hasContactInfo }"
							:style="{ position: 'relative' }"
						>
							{{ data.draweeName || data.name || '-' }}
							<template>
								<div :style="{ position: 'absolute', right: '-35px', top: '-3px' }">
									<contact-info-tooltip v-if="!data.hasContactInfo" />
								</div>
							</template>
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Documento do Sacado:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.draweeDocument || data.document || '-' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Celular do Sacado:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.draweeFone || data.draweePhone || '-' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Email do Sacado:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.draweeEmail || '-' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-line class="mb-2 modal-line" />

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Cedente:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.providerName || data.originatorName || '-' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Documento do Cedente:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.providerDocument || data.originatorDocument || '-' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-line class="mb-2 modal-line" />

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Data de Emissão:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ formatDate(data.emissionDate) }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Data de Vencimento:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ formatDate(data.expirationDate) }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-line class="mb-2 modal-line" />

			<farm-row>
				<farm-col cols="12">
					<div class="d-flex mt-1 mb-1">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Valor Nominal:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ formatMoney(data.nominalValue || data.value) }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row v-if="data.valueLiquid">
				<farm-col cols="12">
					<div class="d-flex mt-1 mb-1">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Valor Líquido:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ formatMoney(data.valueLiquid) || 'Sem Precificação' }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row v-if="data.tries">
				<farm-col cols="12">
					<div class="d-flex mt-1 mb-1">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Reprocessamento(s):
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.tries }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row v-if="data.otReason">
				<farm-col cols="12">
					<div class="d-flex mt-1 mb-1">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Motivo OT:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.otReason }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>

			<farm-row v-if="data.reason">
				<farm-col cols="12">
					<div class="d-flex mt-1 mb-1">
						<farm-caption variation="regular" color="gray" class="mr-1">
							Motivo Exibido:
						</farm-caption>
						<farm-caption variation="medium" color="gray" color-variation="darken">
							{{ data.reason }}
						</farm-caption>
					</div>
				</farm-col>
			</farm-row>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer confirmLabel="Fechar" :hasCancel="false" @onConfirm="handleClose" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { defaultDateFormat, brl } from '@farm-investimentos/front-mfe-libs-ts';

import Quota from '@/components/Quota';
import ContactInfoTooltip from '@/features/receivablesBankV2/components/ContactInfoTooltip/ContactInfoTooltip.vue';

import './GeneralInfoModal.scss';

export default defineComponent({
	name: 'GeneralInfoModal',
	components: {
		Quota,
		ContactInfoTooltip,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		data: {
			type: Object,
			required: true,
		},
	},
	emits: ['update:value', 'onClose'],
	setup(props, { emit }) {
		function handleClose(): void {
			emit('update:value', false);
			emit('onClose');
		}

		return {
			formatDate: defaultDateFormat,
			formatMoney: brl,
			handleClose,
		};
	},
});
</script>

<style lang="scss" scoped>
.clipboard-content {
	margin: 0;
}

.d-flex {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.position-relative {
	position: relative;
}

.copy-button {
	position: absolute;
	right: 3.5rem;
	top: 50%;
	transform: translateY(-50%);
}
</style>
