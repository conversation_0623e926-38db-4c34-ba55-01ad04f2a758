<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				hide-default-footer
				class="elevation-0 ml-2 table-receivables-approved"
				id="table-receivables-approved"
				item-key="key"
				:items="data"
				:headers="headerTable"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<farm-emptywrapper subtitle="" />
				</template>
				<template v-slot:header="{ props }" v-if="showCustomHeader">
					<farm-datatable-header
						:headers="props.headers"
						:sortClick="sortClicked"
						:selectedIndex="2"
						@onClickSort="onSort"
					/>
				</template>

				<template v-slot:[`item.emissionDate`]="{ item }">
					{{ formatDate(item.emissionDate) }}
				</template>

				<template v-slot:[`item.expirationDate`]="{ item }">
					{{ formatDate(item.expirationDate) }}
				</template>

				<template v-slot:[`item.netValue`]="{ item }">
					{{ formatMoney(item.netValue) }}
				</template>

				<template v-slot:[`item.value`]="{ item }">
					{{ formatMoney(item.value) }}
				</template>

				<template v-slot:[`item.infos`]="{ item }">
					<farm-btn icon @click="onClickButton(item)">
						<farm-icon color="primary" size="md"> open-in-new</farm-icon>
					</farm-btn>
				</template>
				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="currentPage"
						:totalPages="pagination.totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangeLimitPerPage"
					/>
				</template>
			</v-data-table>
		</farm-box>
		<ModalDetailReceivablesApproved
			v-if="isOpenModal"
			v-model="isOpenModal"
			:data="itemSelected"
			@onClose="onCloseModal"
		/>
	</farm-row>
</template>

<script lang="ts">
import { computed, defineComponent, getCurrentInstance, ref, toRefs, onMounted } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import ModalDetailReceivablesApproved from '@/components/ModalDetailReceivablesApproved';
import { useModal } from '@/composible/useModal';

import { headers } from './configurations';

export default defineComponent({
	name: 'table-receivables-approved',
	components: {
		ModalDetailReceivablesApproved,
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
		pagination: {
			type: Object,
			required: true,
		},
		filter: {
			type: Object,
			required: true,
		},
		headerTable: {
			type: Array,
			default: () => [],
		},
	},
	setup(props, { emit }) {
		const { isOpenModal, onCloseModal, onOpenModal } = useModal();

		const { filter, headerTable } = toRefs(props);

		const currentPage = ref(1);

		const headerTableActive = ref([]);

		const internalInstance: any = getCurrentInstance();

		const breakpoint = computed(() => {
			return internalInstance.proxy.$vuetify.breakpoint.name;
		});

		const showCustomHeader = computed(() => {
			return breakpoint !== 'xs';
		});

		const sortClicked = ref([]);
		const itemSelected = ref(null);

		const headerProps = ref({
			sortByText: 'Ordenar por',
		});

		function onChangePage(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			currentPage.value = pageActive + 1;
			emit('onRequest', { page: pageActive, limit: filter.value.limit });
		}

		function onChangeLimitPerPage(limit: number): void {
			currentPage.value = 1;
			emit('onRequest', { page: 0, limit: limit });
		}

		function onSort(data): void {
			currentPage.value = 1;
			const requestData = {
				order: data.descending,
				orderby: data.field,
				page: 0,
				limit: filter.value.limit,
			};
			emit('onRequest', requestData);
		}

		function onClickButton(item): void {
			itemSelected.value = item;
			onOpenModal();
		}

		onMounted(() => {
			if (headerTable.value.length === 0) {
				headerTableActive.value = [...headers];
				return;
			}
			headerTableActive.value = [...headerTable.value];
		});

		return {
			currentPage,
			headerTableActive,
			showCustomHeader,
			headerProps,
			sortClicked,
			isOpenModal,
			itemSelected,
			headers,
			onCloseModal,
			formatDate: defaultDateFormat,
			formatMoney: brl,
			onChangePage,
			onChangeLimitPerPage,
			onSort,
			onClickButton,
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-receivables-approved', 1, (0));
</style>
<style lang="scss" scoped>
@import './TableReceivablesApproved';
</style>
