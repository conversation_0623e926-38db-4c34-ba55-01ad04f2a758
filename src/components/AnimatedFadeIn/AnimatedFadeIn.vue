<template>
	<!-- TODO: Corrigir o CSS do AnimatedFadeIn. Atualmente a animação altera o fluxo de empilhamento (z-index), 
	fazendo botões de paginação sobreporem modais. Revisar a implementação para não impactar overlays/modais. -->

	<div class="animated-fade-in"> 
		<slot></slot>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name:'animated-fade-in'
});
</script>

<style lang="scss" scoped>
@import './AnimatedFadeIn';
</style>
