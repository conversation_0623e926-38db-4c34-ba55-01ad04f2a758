<template>
	<farm-row extra-decrease>
		<farm-tabs
			class="mb-6 mt-n6"
			ref="tabEl"
			:tabs="tabList"
			:allowUserChange="true"
			:showCounter="false"
			@update="updateTab"
		/>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { TabsFormTypes } from './types';

export default defineComponent({
	name:"tabs-form",
	props: {
		tabList: {
			type: Array,
			required: true,
		},
		valueDefault: {
			type: String,
			required: true,
		},
		isEdit: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			currentTab: '',
			currentTabList: [],
		};
	},
	methods: {
		updateTab(item): void {
			if (!item) {
				return;
			}
			if (item.path === this.$route.query.path) {
				return;
			}
			this.$router.replace({ query: { path: item.path } });
			this.currentTab = item.path;
			this.$emit('onUpdateCurrentTab', item.path);
		},
		configuration(): void {
			let path = this.$route.query.path;
			if (!path) {
				path = this.valueDefault;
				this.$router.replace({
					path: this.$route.path,
					query: { path },
				});
			}
			this.currentTab = path;
			const index = this.currentTabList.findIndex(tab => path === tab.path);
			if (this.$refs.tabEl && this.$refs.tabEl.toIndex) {
				this.$refs.tabEl.toIndex(index);
			}
			this.$emit('onUpdateCurrentTab', path);
		},
	},
	mounted(): void {
		if (this.isEdit) {
			this.currentTabList = [...this.tabList];
		}
	},
	watch: {
		currentTab(newValue): void {
			this.$emit('onUpdateCurrentTab', newValue);
		},
		tabList(newValue: Array<TabsFormTypes>): void {
			this.currentTabList = newValue;
			if (newValue.length > 1) {
				this.configuration();
			}
		},
	},
});
</script>
