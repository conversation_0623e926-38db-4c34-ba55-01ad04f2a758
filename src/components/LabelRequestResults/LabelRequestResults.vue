<template>
	<div class="d-flex">
		<farm-chip v-if="hasItems" color="primary" variation="lighten" :dense="true">
			{{ formatResult(totalItems) }}
		</farm-chip>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

export default defineComponent({
	name: 'label-request-results',
	props: {
		totalItems: {
			required: true,
			type: [Number, String],
			default: '',
		},
		type: {
			type: String,
			default: 'results',
		},
	},
	setup(props) {
		const { totalItems, type } = toRefs(props);
		const hasItems: boolean = computed(() => totalItems.value > 0);

		const formatResult = (value): string =>
			parseInt(value, 10) > 1 ? `${value} resultados` : `${value} resultado`;

		return {
			hasItems,
			formatResult,
			totalItems,
			type,
		};
	},
});
</script>
