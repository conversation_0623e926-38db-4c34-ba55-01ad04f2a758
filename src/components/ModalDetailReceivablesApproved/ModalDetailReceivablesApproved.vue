<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" size="md" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Informações Gerais da Nota" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<div class="modal-detail-receivables-approved">
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2">Número: </farm-caption>
					<farm-caption>{{ data.number }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Nome/Razão Social: </farm-caption>
					<farm-caption>{{ data.draweeName }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> CPF/CNPJ: </farm-caption>
					<farm-caption>{{ data.draweeDocument }}</farm-caption>
				</div>

				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Cedente: </farm-caption>
					<farm-caption>{{ data.providerName }}</farm-caption>
				</div>

				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Documento do cedente: </farm-caption>
					<farm-caption>{{ data.providerDocument }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Data de Emissão: </farm-caption>
					<farm-caption>{{ formatDate(data.emissionDate) }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Data de Vencimento: </farm-caption>
					<farm-caption>{{ formatDate(data.expirationDate) || 'N/A' }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Valor Nominal: </farm-caption>
					<farm-caption>{{ formatMoney(data.nominalValue) || 'N/A' }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Valor Livre: </farm-caption>
					<farm-caption>{{ formatMoney(data.freeValue) || 'N/A' }}</farm-caption>
				</div>
				<div class="d-flex pb-2">
					<farm-caption bold class="mr-2"> Valor Líquido: </farm-caption>
					<farm-caption>{{ formatMoney(data.netValue) || 'N/A' }}</farm-caption>
				</div>
				<div class="d-flex align-center">
					<div>
						<farm-caption bold class="mr-2" tags="span">
							Chave de acesso da Nfe:
						</farm-caption>
					</div>
					<div>
						<farm-caption tags="span">{{
							data.invoiceDanfe || data.danfe
						}}</farm-caption>
					</div>
					<div>
						<farm-copytoclipboard :toCopy="data.invoiceDanfe || data.danfe" />
					</div>
				</div>
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer confirmLabel="Fechar" :hasCancel="false" @onConfirm="onClose" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import { defaultDateFormat, brl } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	name: 'modal-detail-receivables-approved',
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data } = toRefs(props);

		function onClose(): void {
			emit('onClose');
		}

		return {
			data,
			formatDate: defaultDateFormat,
			formatMoney: brl,
			onClose,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './ModalDetailReceivablesApproved';
</style>
