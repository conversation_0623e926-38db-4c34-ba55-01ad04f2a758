<template>
	<div class="d-flex">
		<farm-chip v-if="hasItems" color="primary" variation="lighten" :dense="true">
			{{ formattedValue }}
		</farm-chip>
	</div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
	name:"counted",
	props: {
		value: { required: true, type: Number },
		singularText: { required: true, type: String },
		pluralText: { required: true, type: String },
	},
	setup(props) {
		const hasItems = computed(() => props.value > 0);
		const formattedValue = computed(() => {
			return props.value > 1
				? `${props.value} ${props.pluralText}`
				: `${props.value} ${props.singularText}`;
		});
		return {
			hasItems,
			formattedValue,
		};
	},
});
</script>
