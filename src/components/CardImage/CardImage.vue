<template>
	<div @click="onClick" class="card">
		<v-skeleton-loader
			v-if="isLoading"
			:class="['skeleton-loader', { 'skeleton-card-back': isBack }]"
			type="image"
		/>
		<img v-show="!isLoading" :src="source" :alt="alt" @load="onImageLoad" />
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref, computed } from 'vue';

import svgDefault from '@/assets/FARMPARTS.svg';

export default defineComponent({
	name: 'card-image',
	props: {
		src: {
			type: String,
			required: false,
		},
		alt: {
			type: String,
			required: false,
			default: 'card image',
		},
		isBack: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const { src, alt, isBack } = toRefs(props);
		const isLoading = ref(true);

		const source = computed(() => src.value || svgDefault);

		function onClick() {
			emit('onClick');
		}

		function onImageLoad() {
			isLoading.value = false;
			emit('onImageLoad');
		}

		return {
			src,
			alt,
			onClick,
			onImageLoad,
			isLoading,
			isBack,
			source,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CardImage.scss';
</style>
