<template>
	<div class="d-flex space-between">
		<farm-typography size="md" color="success">
			<b>{{ quantValid }}</b>
			{{ quantValid === 1 ? configuration.valid.singularText : configuration.valid.pluralText }}
		</farm-typography>
		<farm-typography size="md" class="mx-3" color="neutral">
			|
		</farm-typography>
		<farm-typography size="md" color="error">
			<b>{{ quantInvalid }}</b>
			{{ quantInvalid === 1 ? configuration.invalid.singularText : configuration.invalid.pluralText }}
		</farm-typography>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name:"receivables-counter",
	props: {
		configuration: {
			type: Object,
			require: true
		},
		quantValid: {
			type: Number,
			require: true
		},
		quantInvalid: {
			type: Number,
			require: true
		},
	},
});
</script>
