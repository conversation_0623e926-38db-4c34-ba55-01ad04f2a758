<template>
	<div>
		<carousel-card
			ref="carouselCardRef"
			:autoplay="false"
			:initial-index="0"
			indicator-position="none"
			height="198px"
			type="card"
			arrow="hover"
			@change="changeHandle"
			class="farm-carousel-card"
		>
			<carousel-card-item
				v-for="(item, index) in newItems"
				:key="index"
				:name="`cc_${index}`"
			>
				<card-image
					:src="item.sourceImage"
					@onClick="cardClick"
					:isBack="index !== 1"
					alt="alt"
				/>
			</carousel-card-item>
		</carousel-card>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, Ref } from 'vue';

import { CarouselCard, CarouselCardItem, ICarouselCard } from 'vue-carousel-card';

import { getFirstOrSingleWord } from '@/helpers/getFirstOrSingleWord';

import 'vue-carousel-card/styles/index.css';
import CardImage from '../CardImage/CardImage.vue';

export default defineComponent({
	components: {
		'carousel-card': CarouselCard,
		'carousel-card-item': CarouselCardItem,
		CardImage,
	},

	props: {
		items: {
			type: Array,
			default: [],
		},
	},
	setup(props, { emit }) {
		const { items } = props;
		const newItems: any = ref(items);
		const isSingle: Ref<boolean> = ref(false);
		const carouselCardRef = ref<ICarouselCard>();
		const thereWasClick: Ref<boolean> = ref(false);

		const changeHandle = (index: number) => {
			thereWasClick.value = true;

			const dynamicIndex = isSingle.value ? 0 : index;
			if (thereWasClick.value) {
				emit('onChange', dynamicIndex);
			}
		};
		const next = () => {
			carouselCardRef.value?.next();
			thereWasClick.value = true;
		};
		const prev = () => {
			carouselCardRef.value?.prev();
			thereWasClick.value = true;
		};

		const cardClick = () => {
			thereWasClick.value = true;
		};

		const setToFirst = () => {
			carouselCardRef.value?.setActiveItem(1);
			thereWasClick.value = true;
		};

		const hasOnlyItem = (items: any) => {
			newItems.value = [...items];
			isSingle.value = true;
		};

		const isFirstAndLast = (index, isSingle, newItems) => {
			return isSingle && newItems && (index === 0 || index === newItems.length - 1);
		};

		const disableClick = isSingle => {
			return isSingle && 'no-click';
		};

		const showEmptyCard = (index, isSingle, newItems) => {
			return isFirstAndLast(index, isSingle, newItems) && index !== 0 && index !== 1;
		};

		const isEmpty = obj => JSON.stringify(obj) === '{}';

		onMounted(() => {
			items && items.length < 2 && hasOnlyItem(items);
		});

		return {
			carouselCardRef,
			newItems,
			changeHandle,
			next,
			prev,
			isSingle,
			setToFirst,
			cardClick,
			getFirstOrSingleWord,
			disableClick,
			isFirstAndLast,
			showEmptyCard,
			isEmpty,
		};
	},
});
</script>

<style scoped lang="scss">
@import './CarouselCards.scss';
</style>
