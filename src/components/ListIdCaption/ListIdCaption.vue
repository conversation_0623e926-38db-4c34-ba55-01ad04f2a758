<template>
	<div class="list-information">
		<farm-idcaption
			tooltipColor="gray"
			v-for="(item, index) in dataList"
			:key="`list-information-${item.id}-${index}`"
			:icon="item.icon"
			:copyText="item.copyText"
			:class="{
				'list-information-item': true,
				'with-line': index !== dataList.length - 1,
			}"
			:successMessage="messageSucess"
		>
			<template v-slot:title v-if="item.title">
				{{ item.title || 'Carregando...' }}
			</template>
			<template v-slot:subtitle>
				{{ item.subtitle }}
			</template>
		</farm-idcaption>
	</div>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

export default defineComponent({
	name:'list-id-caption',
	props: {
		data: {
			type: Array,
			require: true,
		},
		messageSucess: {
			type: String,
			default: 'Raiz copiada para área de transferência!',
		},
	},
	computed: {
		hasSuccessMessage() {},
	},
	setup(props) {
		const { data } = toRefs(props);

		const dataList = computed(() => {
			return data.value;
		});

		return {
			dataList
		};
	}
});
</script>

<style lang="scss" scoped>
@import './ListIdCaption';
</style>
