<template>
	<lottie :options="options" :height="400" :width="400" v-on:animCreated="handleAnimation" />
</template>

<script lang="ts">
import { defineComponent, watch } from 'vue';

export default defineComponent({
	name:"animated-progress",
	props: {
		jsonAnimation: {
			type: Object,
			required: true,
		},
		progressValue: {
			type: Number,
			required: true,
		},
	},
	data() {
		return {
			anim: null,
		};
	},
	computed: {
		options() {
			return {
				animationData: this.jsonAnimation,
				loop: false,
			};
		},
	},
	mounted() {
		this.watchProgress();
	},
	methods: {
		handleAnimation(animation) {
			this.anim = animation;
		},
		watchProgress() {
			watch(() => this.progressValue, this.adjustFrames);
		},
		adjustFrames() {
			const frames = Math.floor(this.progressValue);
			this.anim.goToAndStop(frames, true);

			if (this.progressValue === 100) {
				this.anim.goToAndPlay(100, true);
			}
		},
	},
});
</script>
