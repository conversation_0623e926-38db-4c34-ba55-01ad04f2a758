<template>
	<farm-collapsible title="" custom-header :open="isOpen">
		<template #header-content>
			<div class="collapsible-header">
				<div class="collapsible-header-left">
					<div class="collapsible-header-content">
						<div>
							<farm-icon size="md" color="primary" class="mr-2">
								clipboard-outline
							</farm-icon>
						</div>
						<farm-subtitle :type="1" variation="medium">
							{{ title }}
						</farm-subtitle>
						<farm-subtitle class="ml-1" variation="medium" :type="1">
							(<farm-subtitle variation="medium" color="success" tag="span" :type="1">
								{{ approved }} </farm-subtitle
							>)
						</farm-subtitle>
					</div>
				</div>
				<div class="collapsible-header-right">
					<div class="collapsible-header-content-right">
						<div>
							<farm-btn
								outlined
								:disabled="disabledButton"
								@click.stop="onHandleClick()"
							>
								<farm-icon size="md">download-outline</farm-icon> Exportar
							</farm-btn>
						</div>
					</div>
				</div>
			</div>
		</template>
		<table-receivables-approved
			:data="data"
			:headerTable="headerTable"
			:pagination="pagination"
			:filter="filter"
			@onRequest="onRequest"
		/>
	</farm-collapsible>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import TableReceivablesApproved from '@/components/TableReceivablesApproved';

export default defineComponent({
	name: 'collapsible-cession-receivables-approved',
	components: {
		TableReceivablesApproved,
	},
	props: {
		isOpen: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: 'Recebíveis Aprovados',
		},
		data: {
			type: Array,
			default: () => [],
		},
		pagination: {
			type: Object,
			default: () => ({}),
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
		headerTable: {
			type: Array,
			default: () => [],
		},
		disabledButton: {
			type: Boolean,
			default: false,
		},
		approved: {
			type: Number,
			required: false,
		},
	},
	setup(props, { emit }) {
		const { data, isOpen, title, disabledButton, approved } = toRefs(props);

		function onHandleClick(): void {
			emit('onClickDownload');
		}

		function onOpen(data): void {
			emit('onStatusOpen', data);
		}

		function onRequest(data): void {
			emit('onRequest', data);
		}

		return {
			isOpen,
			title,
			data,
			approved,
			disabledButton,
			onHandleClick,
			onOpen,
			onRequest,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './CollapsibleCessionReceivablesApproved';
</style>
