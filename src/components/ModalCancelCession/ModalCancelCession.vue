<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" size="sm" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Cancelar Operação" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-caption>
				Deseja realmente cancelar a operação? Ela ficará com o estado de “cancelada” na listagem, podendo ser consultado a qualquer momento.
			</farm-caption>
		
			<farm-caption class="my-3">
				Escreva no campo abaixo
				<b>"CANCELAR"</b> para cancelar a operação.
			</farm-caption>
			<farm-promptusertoconfirm v-model="inputModel" match="CANCELAR" title="" />
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Sim"
				closeLabel="Cancelar"
				:isConfirmDisabled="!inputModel"
				@onConfirm="onConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name:"modal-cancel-cession",
	props: {
		value: {
			type: Boolean,
			required: true,
		}
	},
	setup(props, { emit }) {

		const inputModel = ref(false);

		function onClose(): void {
			emit('onClose');
		}

		function onConfirm(): void {
			emit('onConfirm');
		}

		return {
			inputModel,
			onConfirm,
			onClose
		};
	},
});
</script>
