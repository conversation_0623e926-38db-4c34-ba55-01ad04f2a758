<template>
	<farm-box>
		<farm-row y-grid-gutters v-if="!isDataEmpty">
			<farm-col cols="12" v-for="(item, index) in data" :key="`list-deny-reasons-${index}`">
				<farm-card>
					<farm-card-content gutter="md" class="d-flex align-center">
						<farm-bodytext>
							{{ item.quant }}
						</farm-bodytext>
						<div class="line mx-3"></div>
						<farm-bodytext>
							{{ item.label }}
						</farm-bodytext>
					</farm-card-content>
				</farm-card>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

export default defineComponent({
	name: "list-deny-reasons",
	props:{
		data: Array,
		require: true
	},
	setup(props){
		const { data } = toRefs(props);
		const isDataEmpty = computed(() => data.value.length === 0);
		return {
			data,
			isDataEmpty
		};
	}
});
</script>
<style lang="scss" scoped>
@import './ListDenyReasons';
</style>
