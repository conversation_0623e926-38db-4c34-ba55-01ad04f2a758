<template>
	<farm-box>
		<router-view v-if="userHasAccess && selectedProduct.id" />
		<farm-container v-if="currentUserRoles && !userHasAccess">
			<h3>Sem acesso</h3>
		</farm-container>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';

import { localStorageWrapper, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { Route } from 'vue-router/types/router';
import { mapActions } from 'vuex';

import { useRoute, useStore } from '@/composible';
import store from '@/store';

const SelectedProduct = 'SelectedProduct';

export default defineComponent({
	setup() {
		const selectedProduct = computed(() => store.getters['wallet/selectedProduct']);

		const checkProductToUse = () => {
			const route = useRoute();
			const store = useStore();

			let initProduct = localStorageWrapper.getItem(SelectedProduct) || {};

			const routeUseSubProduct = route.meta?.useSubProduct;
			const hasSubProduct = initProduct?.infoProduct?.subProduct;

			if(routeUseSubProduct && hasSubProduct){
				const subProduct = initProduct?.infoProduct?.subProduct;
				initProduct.id = subProduct?.id;
				initProduct.name = subProduct?.name;
			}

			store.dispatch('wallet/updateBaseData', {
				selectedProduct: { ...initProduct }
			});
		};

		return {
			selectedProduct,
			...mapActions('wallet', {
				updateBaseData: 'updateBaseData',
			}),
			checkProductToUse,
			emitRouteMeta: (route: Route) => notification('ROUTE_META', route.meta),
			currentUserRoles: computed( () => store.getters['userAccess/currentUserRoles']),
			userHasAccess: ref(false),
		};
	},
	created() {
		this.checkProductToUse();

		this.listenToUserRolesChange();
        notification('RETRIEVE_CURRENT_USER_ROLES');

		window.addEventListener('CURRENT_USER_INTERNAL', (data: any) => {
			this.updateInternalUser(data.detail.message);
		});

		notification('RETRIEVE_CURRENT_USER_INTERNAL');

		window.addEventListener('PRODUCT', (data: any) => {
			this.updateBaseData({
				selectedProduct: { ...data.detail.message },
			});
		});
        this.emitRouteMeta(this.$route);
	},
	watch: {
        $route(to) {
            this.checkAccess();
            this.emitRouteMeta(to);
			this.checkProductToUse();
        },
    },

});
</script>

<style src="@farm-investimentos/front-mfe-components/dist/front-mfe-components.css">
