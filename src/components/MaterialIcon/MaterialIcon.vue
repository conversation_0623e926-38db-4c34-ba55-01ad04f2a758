<template>
	<i v-on="$listeners" v-bind="$attrs" :class="classes" :size="$props.size" ref="el">
		{{ icon }}
	</i>
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue';

const breakPoints = ['xs', 'sm', 'md', 'lg', 'xl'];

export default defineComponent({
	name: 'material-icon',
	inheritAttrs: true,

	props: {
		/**
		 * Color
		 */
		color: {
			type: String as PropType<
				| 'primary'
				| 'secondary'
				| 'secondary-green'
				| 'secondary-golden'
				| 'neutral'
				| 'info'
				| 'success'
				| 'error'
				| 'warning'
				| 'extra-1'
				| 'extra-2'
				| 'gray'
				| 'black'
				| 'white'
			>,
			default: 'primary',
		},
		size: {
			type: String as PropType<
				'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'other (examples: 12px, 3rem)'
			>,
			default: 'default',
		},
		variation: {
			type: String as PropType<
				'lighten' | 'base' | 'darken' | '80' | '50' | '40' | '30' | '10' | '5'
			>,
			default: 'base',
		},
		type: {
			type: String as PropType<'filled' | 'outlined'>,
			default: 'filled',
		},
	},

	data() {
		return {
			icon: '',
		};
	},

	computed: {
		classes() {
			const iconClass = this.type === 'outlined' ? 'material-icons-outlined' : 'material-icons';
			return {
				'material-icon': true,
				['material-icon--' + this.color]: true,
				[iconClass]: true,
				'material-icon--lighten': this.variation === 'lighten',
				'material-icon--darken': this.variation === 'darken',
				['material-icon--black-' + this.variation]: this.color === 'black',
			};
		},
		fontSize() {
			return isNaN(Number(this.size)) ? this.size : `${this.size}px`;
		},
	},
	mounted() {
		if (this.size !== 'default' && !breakPoints.includes(this.size)) {
			this.$el.style.fontSize = this.fontSize;
		}
	},
	created() {
		this.checkForSlotContent();
	},
	beforeUpdate() {
		this.checkForSlotContent();
	},
	methods: {
		checkForSlotContent() {
			if (!this.$slots.default) {
				this.icon = '';
				return;
			}
			this.icon = this.$slots.default[0].text!.trim();
		},
	},
});
</script>
<style lang="scss" scoped>
@import './MaterialIcon.scss';
</style> 