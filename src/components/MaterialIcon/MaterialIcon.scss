@import '~@farm-investimentos/front-mfe-components/src/configurations/variables';
@import '~@farm-investimentos/front-mfe-components/src/configurations/theme-colors';

.material-icon {
	align-items: center;
	display: inline-flex;
	font-size: 24px;
	justify-content: center;
	letter-spacing: normal;
	line-height: 1;
	position: relative;
	text-indent: 0;

	// Propriedades específicas para as fontes do Material Icons
	&.material-icons {
		font-family: 'Material Icons';
	}

	&.material-icons-outlined {
		font-family: 'Material Icons Outlined';
	}

	@each $color in $theme-colors-list {
		&#{'--' + $color} {
			color: var(--farm-#{$color}-base);

			&.material-icon--lighten {
				color: var(--farm-#{$color}-lighten);
			}

			&.material-icon--darken {
				color: var(--farm-#{$color}-darken);
			}
		}
	}

	&--white {
		color: white;
	}

	@each $size, $value in $sizes {
		&#{'.material-icon[size=' + $size + ']'} {
			font-size: $value;
		}
	}

	@each $v in $bwVariations {
		&.material-icon--black-#{$v} {
			color: var(--farm-bw-black-#{$v});
		}
	}
} 