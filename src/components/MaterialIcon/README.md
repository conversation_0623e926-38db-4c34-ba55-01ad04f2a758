# MaterialIcon

O componente `MaterialIcon` é uma implementação para usar os ícones da biblioteca [Material Icons](https://fonts.google.com/icons?selected=Material+Icons) da Google no projeto, mantendo a consistência de propriedades com outros componentes da Farm. Ele suporta os estilos **Filled** (padrão) e **Outlined**.

## Como usar

```vue
<!-- <PERSON><PERSON><PERSON>lled (padrão) -->
<material-icon>home</material-icon>

<!-- Ícon<PERSON> Outlined -->
<material-icon type="outlined">search</material-icon>

<!-- Ícone Outlined com cor e tamanho -->
<material-icon type="outlined" size="lg" color="primary">settings</material-icon>
```

## Props

| Prop | Tipo | Padrão | Descrição |
|------|------|--------|-----------|
| type | String | 'filled' | Estilo do ícone: 'filled' ou 'outlined'. |
| color | String | 'primary' | Cor do ícone (primary, secondary, info, success, error, warning, extra-1, extra-2, gray, black, white) |
| size | String | 'default' | Tamanho do ícone (xs, sm, md, lg, xl ou valor específico como '36px'). O padrão da fonte Material Icons é 24px. |
| variation | String | 'base' | Variação da cor (lighten, base, darken, ou variações de preto/cinza: 80, 50, 40, 30, 10, 5) |

## Importante

Este componente utiliza as fontes "Material Icons" e "Material Icons Outlined" via Google Fonts, carregadas globalmente no `main.ts`. O nome do ícone desejado deve ser passado como conteúdo do slot, utilizando a notação _snake_case_ (ex: `arrow_forward`). 