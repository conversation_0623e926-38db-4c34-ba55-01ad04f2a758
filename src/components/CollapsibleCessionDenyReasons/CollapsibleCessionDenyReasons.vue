<template>
	<farm-collapsible title="" custom-header :open="isOpen">
		<template #header-content>
			<div class="collapsible-header">
				<div class="collapsible-header-left">
					<div class="collapsible-header-content">
						<div>
							<farm-icon size="md" color="primary" class="mr-2">
								chart-bar
							</farm-icon>
						</div>
						<farm-subtitle :type="1" variation="medium">
							{{ title }}
						</farm-subtitle>
						<farm-subtitle class="ml-1" variation="medium" :type="1">
							(<farm-subtitle variation="medium" color="error" tag="span" :type="1">
								{{ notApproved }} </farm-subtitle
							>)
						</farm-subtitle>
					</div>
				</div>
				<div class="collapsible-header-right">
					<div class="collapsible-header-content-right">
						<div>
							<farm-btn
								outlined
								:disabled="disabledButton"
								@click.stop="onHandleClick()"
							>
								<farm-icon size="md">download-outline</farm-icon> Exportar
							</farm-btn>
						</div>
					</div>
				</div>
			</div>
		</template>
		<table-deny-reasons
			:data="data"
			:pagination="pagination"
			:filter="filter"
			@onRequest="onRequest"
		/>
	</farm-collapsible>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref } from 'vue';

import TableDenyReasons from '@/components/TableDenyReasons';

export default defineComponent({
	name: 'collapsible-cession-deny-reasons',
	props: {
		isOpen: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: 'Motivos de Recusa',
		},
		data: {
			type: Array,
			required: true,
		},
		disabledButton: {
			type: Boolean,
			default: false,
		},
		notApproved: {
			type: Number,
			required: true,
		},
		pagination:{
			type: Object,
			required: true,
		}
	},
	components: {
		TableDenyReasons,
	},
	setup(props, { emit }) {
		const { data, isOpen, title, disabledButton, notApproved, pagination } = toRefs(props);

		const filter = ref({
			page:0,
			limit:10
		});

		function onHandleClick(): void {
			emit('onClickDownload');
		}

		function onOpen(data): void {
			emit('onStatusOpen', data);
		}

		function onRequest(data): void {
			filter.value = {
				...data,
				page: data.page || 0,
				limit: data.limit || 10,
			};
			emit('onRequest', filter.value);
		}

		return {
			isOpen,
			title,
			data,
			notApproved,
			disabledButton,
			filter,
			pagination,
			onHandleClick,
			onOpen,
			onRequest
		};
	},
});
</script>
<style lang="scss" scoped>
@import './CollapsibleCessionDenyReasons';
</style>
