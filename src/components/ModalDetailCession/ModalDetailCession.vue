<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="76" size="md" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Detal<PERSON> da Cessão" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<div class="modal-detail-cession-main">
				<div class="modal-detail-cession-disbursement">
					<div class="mr-8">
						<div class="d-flex">
							<farm-icon color="primary" size="16px" class="mr-2" variation="darken">
								wallet
							</farm-icon>
							<farm-caption variation="regular" color="primary" color-variation="darken">
								Desembolso Previsto
							</farm-caption>
						</div>
						<farm-bodytext :type="2" variation="bold" color="primary" color-variation="darken">
							{{ formatMoney(data.valueDisbursement) || 'N/A'}}
						</farm-bodytext>
					</div>
					<div>
						<div class="d-flex">
							<farm-icon color="primary" size="16px" class="mr-2" variation="darken">
								wallet
							</farm-icon>
							<farm-caption variation="regular" color="primary" color-variation="darken">
								Data do Desembolso
							</farm-caption>
							<farm-tooltip>
								<farm-caption variation="semiBold" color="white">
									Data do Desembolso
								</farm-caption>
								<farm-caption variation="regular" color="white">
									Data prevista para o desembolso.
								</farm-caption>
								<template v-slot:activator>
									<farm-icon size="sm" color="gray" class="mx-1">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</div>
						<farm-bodytext :type="2" variation="bold" color="primary" color-variation="darken">
							{{ formatDate(data.dateDisbursement) }}
						</farm-bodytext>
					</div>
				</div>
				<farm-line :noSpacing="true" class="my-4" />
				<div class="modal-detail-cession-operation">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" class="mr-1">
							Tipo de Operação:
						</farm-caption>
						<farm-caption variation="medium">
							{{ data.typeOperation }}
						</farm-caption>
					</div>
					<div class="d-flex">
						<farm-caption variation="regular" class="mr-1">
							Produto
						</farm-caption>
						<farm-caption variation="medium">
							{{ data.productName }}
						</farm-caption>
					</div>
				</div>
				<farm-line :noSpacing="true" class="my-4" v-if="receivables" />
				<div class="modal-detail-cession-receivables" v-if="receivables">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" class="mr-1">
							Recebíveis Selecionados:
						</farm-caption>
						<farm-caption variation="medium">
							{{ data.total }}
						</farm-caption>
					</div>
					<div class="d-flex mb-2">
						<farm-idcaption :link="true" no-height @onLinkClick="openModalReceivablesApproved(data)">
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									Aprovados: <b>{{ data.approved }}</b>
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
					<div class="d-flex">
						<farm-idcaption :link="true" no-height @onLinkClick="openModalDenyReasons(data)">
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									Recusados: <b>{{ data.refused }}</b>
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
				</div>
				<farm-line :noSpacing="true" class="my-4" />
				<div class="modal-detail-cession-limit">
					<div class="d-flex mb-2">
						<farm-caption variation="regular" >
							Limite Disponível:
						</farm-caption>
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white">
								Limite Disponível
							</farm-caption>
							<farm-caption variation="regular" color="white">
								O limite disponível considera o valor livre para calcular a cessão.
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray" class="mx-1">help-circle</farm-icon>
							</template>
						</farm-tooltip>
						<farm-caption variation="medium">
							{{ formatMoney(data.limit) }}
						</farm-caption>
					</div>
					<div class="d-flex mb-2">
						<farm-caption variation="regular" class="mr-1">
							Valor Nominal:
						</farm-caption>
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white">
								Valor Nominal
							</farm-caption>
							<farm-caption variation="regular" color="white">
								Valor nominal dos recebíveis da operação.
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray" class="mx-1">help-circle</farm-icon>
							</template>
						</farm-tooltip>
						<farm-caption variation="medium">
							{{ formatMoney(data.nominalValue) }}
						</farm-caption>
					</div>
					<div class="d-flex">
						<farm-caption variation="regular" class="mr-1">
							Valor Livre:
						</farm-caption>
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white">
								Valor Livre
							</farm-caption>
							<farm-caption variation="regular" color="white">
								Valor nominal menos o valor do excedente.
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray" class="mx-1">help-circle</farm-icon>
							</template>
						</farm-tooltip>
						<farm-caption variation="medium">
							{{ formatMoney(data.freeValue) }}
						</farm-caption>
					</div>
				</div>
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer confirmLabel="Fechar" :hasCancel="false" @onConfirm="onClose" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import {
	defaultDateFormat,
	brl
} from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	name:"modal-detail-cession",
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		data: {
			type: Object,
			default: false,
		},
		receivables:{
			type: Boolean,
			required: true,
		}
	},
	setup(props, { emit }) {
		const { data } = toRefs(props);

		function onClose(): void {
			emit('onClose');
		}

		function openModalReceivablesApproved(data): void {
			emit('onOpenModalReceivablesApproved', data);
		}

		function openModalDenyReasons(data): void {
			emit('onOpenModalDenyReasons', data);
		}

		return {
			data,
			formatDate: defaultDateFormat,
			formatMoney: brl,
			onClose,
			openModalReceivablesApproved,
			openModalDenyReasons
		};
	},
});
</script>
<style lang="scss" scoped>
@import './ModalDetailCession';
</style>
