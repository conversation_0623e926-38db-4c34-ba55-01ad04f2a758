<template>
	<farm-card class="mb-4">
		<farm-card-content>
			<div class="formalization-card-flex">
				<div class="formalization-card-information">
					<div>
						<farm-bodytext variation="bold" :type="1">
							{{ data.title }}
						</farm-bodytext>
					</div>
					<div>
						<card-list-text-header noSpacing :data="listDates" />
					</div>
				</div>
				<div class="formalization-card-count">
					<farm-bodytext variation="bold" :type="2">
						<b>{{ data.numberOfSignedSignatories || 0 }}</b> de
						<b> {{ data.numberOfSignatories || 0 }}</b> Assinado(s)
					</farm-bodytext>
				</div>
			</div>
		</farm-card-content>
		<farm-line no-spacing />
		<farm-card-content>
			<farm-box>
				<div v-for="(item, index) in data.documents" :key="item.id">
					<formalization-documents-card :data="item" />
					<farm-line noSpacing class="mt-3 mb-4" v-if="!((data.documents.length - 1) === index)" />
				</div>
			</farm-box>
		</farm-card-content>
	</farm-card>
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import CardListTextHeader from '@/components/CardListTextHeader';

import FormalizationDocumentsCard from '../FormalizationDocumentsCard';

export default defineComponent({
	name: 'formalization-card',
	components: {
		CardListTextHeader,
		FormalizationDocumentsCard,
	},
	props: {
		data: {
			type: Object,
			required: true
		}
	},
	setup(props) {
		const { data } = toRefs(props);

		const listDates = [
			{
				id: null,
				label: 'Data de Emissão',
				value: defaultDateFormat(data.value.emissionDate) || null,
				copyText: '',
			},
			{
				id: null,
				label: 'Data de Vencimento',
				value: defaultDateFormat(data.value.dueDate) || null,
				copyText: '',
			},
		];

		return {
			listDates,
			data,
		};
	},
});
</script>
<style lang="scss" scoped>
@import 'FormalizationCard';
</style>
