<template>
	<farm-chip :color="data.color" :dense="dense" v-if="data">
		<farm-icon color="white" size="16px" class="mr-1">{{ data.icon }}</farm-icon
		>{{ data.text || 'N/A' }}
	</farm-chip>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, toRefs } from 'vue';

export default defineComponent({
	name: 'formalization-status',
	props: {
		status: {
			type: String,
			require: true,
		},
		dense: {
			type: Boolean,
			default: false,
		},
	},
	setup(props) {
		const { status } = toRefs(props);
		const data = ref(null);

		onMounted(() => {
			if (status.value.toUpperCase() === 'PENDENTE') {
				data.value = {
					color: 'warning',
					text: 'Pendente',
					icon: 'timer-sand',
				};
				return;
			}
			data.value = {
				color: 'primary',
				text: 'Assinado',
				icon: 'check',
			};
		});

		return {
			data,
		};
	},
});
</script>
