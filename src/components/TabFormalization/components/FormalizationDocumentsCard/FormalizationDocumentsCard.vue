<template>
	<div>
		<div class="formalization-card-flex mb-2">
			<div>
				<farm-bodytext variation="bold" :type="2">
					{{ data.documentName }}
				</farm-bodytext>
			</div>
			<div>
				<farm-btn outlined :disabled="false" @click.stop="onDownload(data)">
					<farm-icon size="md">download-outline</farm-icon> Baixar Contrato
				</farm-btn>
			</div>
		</div>
		<div
			class="formalization-card-flex"
			v-for="(item, index) in data.signatories"
			:key="`teste${index}`"
		>
			<div class="formalization-card-information">
				<div class="formalization-card-list">
					<farm-bodytext :type="2" tag="p" color="black" colorVariation="80">
						{{ item.name }}
					</farm-bodytext>
					<div class="formalization-card-line mx-2"></div>
					<farm-typography
						size="14px"
						tag="p"
						color="black"
						:weight="400"
						colorVariation="80"
					>
						<farm-icon color="primary" size="16px" class="mr-1">email</farm-icon
						>{{ item.email }}
					</farm-typography>
					<div class="formalization-card-line mx-2" v-if="item.phoneNumber"></div>
					<farm-typography
						size="14px"
						tag="p"
						color="black"
						:weight="400"
						colorVariation="80"
						v-if="item.phoneNumber"
					>
						<farm-icon color="primary" size="16px" class="mr-1">phone</farm-icon
						>{{ item.phoneNumber }}
					</farm-typography>
				</div>
			</div>
			<div class="formalization-card-count">
				<formalization-status :status="item.farmStatusCode" dense />
				<div v-if="!isHiddenOptions">
					<farm-context-menu
						:items="contextMenu"
						@onCopy="onCopy(item)"
						@onSend="onSend(data, item)"
					/>
				</div>
			</div>
		</div>
		<farm-loader mode="overlay" v-if="isLoadingSendEmail" />
	</div>
</template>
<script lang="ts">
import { defineComponent, inject } from 'vue';

import CardListTextHeader from '@/components/CardListTextHeader';
import { useSponsorCockpitFormalizationDownload } from '@/features/SponsorCockpit/composables/useSponsorCockpitFormalizationDownload';

import { useCopyLinkFormalization } from '../../composables/useCopyLinkFormalization';
import { useDownloadContract } from '../../composables/useDownloadContract';
import { useSendEmailFormalization } from '../../composables/useSendEmailFormalization';
import { contextMenu } from '../../configurations/contextMenu';
import FormalizationStatus from '../FormalizationStatus';

export default defineComponent({
	name: 'formalization-documents-card',
	components: {
		CardListTextHeader,
		FormalizationStatus,
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	setup() {
		const { isLoadingSendEmail, sendEmail } = useSendEmailFormalization();
		const { copyLink } = useCopyLinkFormalization();
		const { downloadContract } = useDownloadContract();
		const { downloadFormalizationDocument } = useSponsorCockpitFormalizationDownload();

		// Inject download context from parent
		const downloadContext = inject('downloadContext', 'default');
		const operationId = inject('operationId', null);

		async function onCopy(item): Promise<void> {
			copyLink(item.signatureLink);
		}

		function onSend(params, item): void {
			const payload = {
				name: item.name,
				email: item.email,
				signatureLink: item.signatureLink,
				contractName: params.documentName,
			};
			sendEmail(payload);
		}

		function onDownload(itemSelected) {
			if (downloadContext === 'sponsor' && operationId && itemSelected.documentId) {
				downloadFormalizationDocument(operationId, itemSelected.documentId, itemSelected.documentName);
			} else {
				downloadContract(itemSelected.link);
			}
		}

		return {
			contextMenu,
			isLoadingSendEmail,
			onCopy,
			onSend,
			onDownload,
		};
	},
});
</script>
<style lang="scss" scoped>
@import 'FormalizationDocumentsCard';
</style>
