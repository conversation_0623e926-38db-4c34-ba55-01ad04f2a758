import { RequestStatusEnum, notification, toClipboard } from '@farm-investimentos/front-mfe-libs-ts';

type UseCopyLinkFormalization = {
	copyLink: Function;
};

export function useCopyLinkFormalization(): UseCopyLinkFormalization {
	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `O link de assinatura foi copiado para área de transferência. !`,
			title: 'Copiado com sucesso',
		});
	}

	function createNotificationError(): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `Não foi possivel copiar o link, Por favor tente mais tarde.`,
			title: 'Erro copiar o link',
		});
	}

	async function copyLink(link): Promise<void> {
		try {
			await toClipboard(link);
			createNotification();
		} catch (error) {
			createNotificationError();
		}
	}

	return {
		copyLink,
	};
}
