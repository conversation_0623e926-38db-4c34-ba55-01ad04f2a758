import { ref, computed } from "vue";

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { sendEmailFormalization } from '@/features/detailCession/services';

type UseSendEmailFormalization = {
	isLoadingSendEmail: {
		value: boolean
	};
	isErrorSendEmail: {
		value: boolean
	};
	sendEmail: Function;
};

export function useSendEmailFormalization(): UseSendEmailFormalization {
	const email = ref('');
	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => sendEmailFormalization(params),
		onSuccess: () => {
			setTimeout(() => {
				createNotification(email);
			}, 1000);
		},
		onError: () => {
			setTimeout(() => {
				createNotificationError();
			}, 1000);
		}
	});

	function createNotification(email): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `O link de assinatura foi reenviado para o e-mail <b>${email.value}</b>!`,
			title: 'Sucesso',
		});
	}

	function createNotificationError(): void {
		notification(RequestStatusEnum.SUCCESS, {
			message: `Não foi possivel enviar o e-mail, Por favor tente mais tarde.`,
			title: 'Erro',
		});
	}

	const isLoadingSendEmail= computed(() => {
		return isLoading.value;
	});

	const isErrorSendEmail = computed(() => {
		return isError.value;
	});

	function sendEmail(payload): void {
		mutate({ payload });
		email.value = payload.email;
	}

	return {
		isLoadingSendEmail,
		isErrorSendEmail,
		sendEmail,
	};
}

