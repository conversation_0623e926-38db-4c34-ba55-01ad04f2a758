<template>
	<farm-box>
		<formalization-list-skeleton :numberItems="2" v-if="isLoading" />
		<animated-fade-in v-if="!isLoading && (isError || isEmpty)">
			<formalization-status-completed-empty />
		</animated-fade-in>
		<animated-fade-in v-if="!isLoading && !isEmpty">
			<farm-box>
				<formalization-card :data="item" v-for="item in formalization" :key="item.id" />
			</farm-box>
		</animated-fade-in>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, provide } from 'vue';

import AnimatedFadeIn from '@/components/AnimatedFadeIn';

import FormalizationCard from './components/FormalizationCard';
import FormalizationListSkeleton from './components/FormalizationListSkeleton';
import FormalizationStatusCompletedEmpty from './components/FormalizationStatusCompletedEmpty';

export default defineComponent({
	name: 'tab-formalization',
	components: {
		FormalizationCard,
		FormalizationListSkeleton,
		FormalizationStatusCompletedEmpty,
		AnimatedFadeIn,
	},
	props: {
		formalization: {
			type: Array,
			required: true,
		},
		isLoading: {
			type: Boolean,
			required: true,
		},
		isError: {
			type: Boolean,
			default: false,
		},
		downloadContext: {
			type: String,
			default: 'default',
		},
		operationId: {
			type: Number,
			required: false,
		},
	},
	setup(props) {
		const isEmpty = computed(
			() => !Array.isArray(props.formalization) || props.formalization.length === 0
		);

		// Provide download context to child components
		provide('downloadContext', props.downloadContext);
		provide('operationId', props.operationId);

		return {
			isEmpty,
		};
	},
	emits: ['reload'],
});
</script>
