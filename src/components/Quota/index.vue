<template>
	<div class="quota-content">
		<farm-tooltip>
			<template #activator>
				<div class="quota-icon">P</div>
			</template>
			<span><PERSON><PERSON><PERSON><PERSON></span>
		</farm-tooltip>
	</div>
</template>

<style lang="scss" scoped>
.quota-content {
	display: flex;
	align-items: center;
	justify-content: center;
}

.quota-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 20px;
	min-height: 20px;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background-color: #FFB300;
	color: #FFFFFF;
	font-size: 12px;
	font-weight: bold;
}
</style> 