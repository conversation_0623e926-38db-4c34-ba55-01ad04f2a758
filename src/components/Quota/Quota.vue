<template>
	<div
		:class="{
			'quota-content': true,
			small: small,
		}"
	>
		<farm-tooltip fluid position="top-right">
			<template #title>
				<farm-caption variation="semiBold" color="white"> Parcelado </farm-caption>
			</template>
			<farm-caption variation="regular" color="white">
				Recebível com múltiplos vencimentos.
			</farm-caption>
			<template #activator>
				<span class="text cursor-pointer">P</span>
			</template>
		</farm-tooltip>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name: 'quota',
	props: {
		small: {
			type: Boolean,
			default: false,
		},
	},
});
</script>

<style lang="scss" scoped>
@import './Quota';
</style>
