<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" size="sm" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="<PERSON><PERSON><PERSON>" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-caption>
				Deseja realmente fechar a Cessão? Nenhuma seleção feita nesse passo será
				salvo.
			</farm-caption>
			<farm-caption>
				Escreva no campo abaixo
				<b>"FECHAR"</b> para fechar a cessão.
			</farm-caption>
			<farm-promptusertoconfirm v-model="inputModel" match="FECHAR" title="" />
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Sim"
				closeLabel="Cancelar"
				:isConfirmDisabled="!inputModel"
				@onConfirm="onConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">

import { defineComponent, ref } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';

export default defineComponent({
	name:"modal-close-cession",
	props: {
		value: {
			type: Boolean,
			required: true,
		}
	},
	setup(props, { emit }) {
		const { trigger } = useAnalytics();

		const inputModel = ref(false);

		function onClose(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					step: 2,
					description:'clicou no botão de cancelar modal fechar cessão',
				}
			};
			trigger(dataTrigger);
			emit('onClose');
		}

		function onConfirm(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					step: 2,
					description:'clicou no botão de sim modal fechar cessão',
				}
			};
			trigger(dataTrigger);
			emit('onConfirm');
		}

		return {
			inputModel,
			onConfirm,
			onClose
		};
	},
});
</script>
