<template>
	<farm-collapsible
		icon="chart-bar"
		colorIcon="primary"
		:title="title"
		:open="isOpen"
		:hasButton="true"
		:disabledButton="disabledButton"
		@onClick="onHandleClick"
		@open="onOpen"
	>
		<list-deny-reasons :data="data" />
	</farm-collapsible>
</template>

<script lang="ts">
import { defineComponent,  toRefs } from 'vue';

import ListDenyReasons from '@/components/ListDenyReasons';

export default defineComponent({
	name: 'collapsible-deny-reasons',
	props: {
		isOpen: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: 'Motivos de Recusa',
		},
		data: {
			type: Array,
			default: () => [],
		},
		id: {
			type: Number,
			required: false,
		},
		disabledButton: {
			type: Boolean,
			default: false,
		}
	},
	components: {
		ListDenyReasons,
	},
	setup(props, { emit }) {

		const { data, isOpen, title, disabledButton } = toRefs(props);

		function onHandleClick(): void {
			emit('onClick');
		}

		function onOpen(data): void {
			emit('onStatusOpen', data);
		}

		return {
			isOpen,
			title,
			data,
			disabledButton,
			onHandleClick,
			onOpen
		};
	},
});
</script>
