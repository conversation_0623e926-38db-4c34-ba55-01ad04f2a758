<template>
	<farm-alertbox v-model="value" dismissable :color="color" :icon="icon" class="my-4">
		<slot />
	</farm-alertbox>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name:"feedback-alert",
	props: {
		color: {
			type: String,
			default: 'warning',
		},
		icon:{
			type: String,
			default: 'alert-circle-outline',
		},
		value: {
			type: Boolean,
			default: false,
		}
	}
});
</script>
