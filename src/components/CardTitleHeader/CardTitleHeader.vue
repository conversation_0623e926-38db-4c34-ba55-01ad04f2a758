<template>
	<farm-bodytext variation="bold" :type="2" :ellipsis="ellipsis" :title="hasTitle">
		{{ value }}
	</farm-bodytext>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name:"card-title-header",
	props: {
		ellipsis: {
			type: Boolean,
			default: false,
		},
		value: {
			type: String,
			required: true,
		},
	},
	computed: {
		hasTitle(): string {
			return this.ellipsis ? this.value : '';
		},
	},
});
</script>
