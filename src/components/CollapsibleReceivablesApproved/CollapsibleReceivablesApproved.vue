<template>
	<farm-collapsible
		icon="clipboard-outline"
		colorIcon="primary"
		:title="title"
		:open="isOpen"
		:hasButton="true"
		:disabledButton="disabledButton"
		@onClick="onHandleClick"
		@open="onOpen"
	>
		<farm-caption v-if="id" class="d-flex justify-end py-4"
			><b>Valor Nominal Total</b>: {{ sumTotalNominal }}</farm-caption
		>
		<farm-loader v-if="isLoading" mode="overlay" />
		<table-receivables-approved
			v-if="data"
			:data="data"
			:headerTable="headerTable"
			:pagination="pagination"
			:filter="filter"
			@onRequest="onRequest"
		/>
	</farm-collapsible>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import TableReceivablesApproved from '@/components/TableReceivablesApproved';
import useSelectedProductId from '@/composible/useSelectedProductId';

import { getNominalTotalSum as getNominalTotalSumService } from '../../features/operationCession/services/services';

export default defineComponent({
	name: 'collapsible-receivables-approved',
	components: {
		TableReceivablesApproved,
	},
	props: {
		isOpen: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: 'Recebíveis Aprovados',
		},
		data: {
			type: Array,
			required: false,
		},
		id: {
			type: Number,
			required: false,
		},
		pagination: {
			type: Object,
			default: () => ({}),
		},
		filter: {
			type: Object,
			default: () => ({}),
		},
		headerTable: {
			type: Array,
			default: () => [],
		},
		disabledButton: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const productId = useSelectedProductId().value;

		function onSort(data): void {
			emit('onSort', data);
		}

		function onHandleClick(): void {
			emit('onClick');
		}

		function onRequest(data): void {
			emit('onRequest', data);
		}

		function onOpen(data): void {
			emit('onStatusOpen', data);
		}

		const sumTotalNominal = ref();

		const { isLoading, mutate } = useMutation({
			mutationFn: params => getNominalTotalSumService(params),
			onSuccess: data => {
				sumTotalNominal.value = brl(data.data.content.total || 0);
			},
		});

		onMounted(() => {
			if (props.id) {
				mutate({ productId, id: props.id });
			}
		});

		return {
			onHandleClick,
			onSort,
			onRequest,
			onOpen,
			sumTotalNominal,
			isLoading,
		};
	},
});
</script>
