<template>
	<farm-box>
		<farm-row justify="center" class="mb-4">
			<img
				v-if="receivables"
				src="@/assets/empty-receivables.svg"
				alt="imagem referente a filtro vazio"
			/>
			<img v-else src="@/assets/empty-filter.svg" alt="imagem referente a filtro vazio" />
		</farm-row>
		<farm-row justify="center">
			<farm-bodytext variation="bold" color="gray" :type="2">
				{{ title }}
			</farm-bodytext>
		</farm-row>
		<farm-row justify="center" class="mt-2">
			<farm-caption variation="regular" color="gray">
				{{ subtitle }}
			</farm-caption>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name: 'filter-empty-state',
	props: {
		receivables: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: 'Nenhuma informação para exibir aqui',
		},
		subtitle: {
			type: String,
			default: 'Tente filtrar novamente sua pesquisa.',
		},
	},
});
</script>
