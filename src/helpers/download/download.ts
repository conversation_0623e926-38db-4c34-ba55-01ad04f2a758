/**
 * Cria um link de download para um blob e o executa.
 * @param blobData - Os dados do arquivo em formato blob.
 * @param fileName - O nome que o arquivo terá ao ser baixado.
 */
export function downloadBlob(blobData: any, fileName: string): void {
	const blob = new Blob([blobData], { type: 'application/octet-stream' });
	const downloadUrl = window.URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.href = downloadUrl;
	link.setAttribute('download', fileName);
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
	window.URL.revokeObjectURL(downloadUrl);
}
