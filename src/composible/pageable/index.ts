import currentPage from './computed/currentPage';
import onInputChange from './methods/onInputChange';
import redoSearchFirstPage from './methods/redoSearchFirstPage';

type Pageable = {
	currentPage: Function;
	onInputChange: Function;
};

export default (filters, doSearchFn): Pageable => {
	const redoSearchFirstPageFn = redoSearchFirstPage(filters, doSearchFn);

	return {
		currentPage: currentPage(filters),
		onInputChange: onInputChange(filters, redoSearchFirstPageFn),
	};
};
