import { ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';
import { Ref } from 'vue/types/v3-generated';

import builderRoles from '@/helpers/builderRoles';
import { getRoles } from '@/services';

interface useRoles {
	currentUserRoles: Ref<Object> | null;
	currentUserRoleName: Ref<String> | null;
	internalUser: Ref<Boolean> | null;
	getUserRoles: Function,
	isLoading: Boolean,
	isError: Boolean
}

export function useRoles(): useRoles {
	const currentUserRoles = ref(null);
	const currentUserRoleName = ref(null);
	const internalUser = ref(null);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getRoles(),
		onSuccess: data => {
			const { internal, roleName, roles } = builderRoles(data.data);

			currentUserRoleName.value = roleName;
			currentUserRoles.value = roles;
			internalUser.value = internal;
		},
	});

	function getUserRoles(): void {
		mutate({});
	}

	return {
		currentUserRoles,
		currentUserRoleName,
		internalUser,
		isLoading,
		isError,
		getUserRoles
	};
}
