/**
 * Retorna uma função com debounce.
 *
 * @param fn Função a ser debounciada
 * @param delay Tempo de espera em ms (default: 300)
 * @returns Função com debounce
 */
export function useDebounce<T extends (...args: any[]) => void>(fn: T, delay = 300) {
	let timeout: ReturnType<typeof setTimeout>;

	return (...args: Parameters<T>) => {
		clearTimeout(timeout);
		timeout = setTimeout(() => fn(...args), delay);
	};
}
