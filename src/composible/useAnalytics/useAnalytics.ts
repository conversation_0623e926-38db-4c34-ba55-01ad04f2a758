import { notification } from '@farm-investimentos/front-mfe-libs-ts';

import store from '@/store';

import { AnalyticsNotificationParams, UseAnalytics, UseAnalyticsProps } from './types';

const INITIAL_STATE = { DEBUG_MODE: false };

export function useAnalytics({ DEBUG_MODE }: UseAnalyticsProps = INITIAL_STATE): UseAnalytics {
	function trigger(data: AnalyticsNotificationParams): void {
		const dataAnalytics = {
			event: data.event,
			payload: {
				action: data?.action || 'click',
				product: store.getters['wallet/selectedProduct'].register?.name,
				...(data?.payload ?? {}),
			},
		};

		if (DEBUG_MODE) {
			console.table(dataAnalytics);
			return;
		}

		notification('ANALYTICS_EVENT', dataAnalytics);
	}
	return {
		trigger,
	};
}
