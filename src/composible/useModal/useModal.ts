import { ref } from 'vue';

import { Ref } from 'vue/types/v3-generated';

interface UseModal {
	isOpenModal: boolean | Ref<boolean>;
	onCloseModal: Function;
	onOpenModal: Function;
}

export function useModal(open = false): UseModal {
	const isOpenModal = ref(open);

	function onCloseModal(): void {
		isOpenModal.value = false;
	}

	function onOpenModal(): void {
		isOpenModal.value = true;
	}

	return {
		isOpenModal,
		onCloseModal,
		onOpenModal,
	};
}
