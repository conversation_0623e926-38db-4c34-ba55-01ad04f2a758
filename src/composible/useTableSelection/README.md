# useTableSelection

Este composable foi criado para centralizar a lógica de seleção de linhas em tabelas que utilizam o `v-data-table` do Vuetify em conjunto com o componente `farm-datatable-header`.

## Problema Resolvido

Ao trabalhar com tabelas que possuem seleção múltipla (checkbox), precisamos gerenciar:
- Seleção individual de linhas
- Seleção/desseleção de todas as linhas
- Estado do checkbox no cabeçalho (selecionado, parcialmente selecionado, não selecionado)
- Sincronização entre o estado do checkbox do cabeçalho e os checkboxes das linhas

## Como Usar

```typescript
import useTableSelection from '@/composible/useTableSelection';

// No setup do seu componente
const items = ref([/* seus dados */]);

const {
  selectedItems,    // Ref<T[]> - Items selecionados
  allSelected,      // ComputedRef<boolean> - Se todos estão selecionados
  someItems,        // ComputedRef<boolean> - Se alguns estão selecionados
  toggleSelection,  // (item: T, value: boolean) => void
  onToggleSelectAll // (value: boolean) => void
} = useTableSelection<T>(items, 'id'); // Onde 'id' é a chave única do item
```

### Exemplo com v-data-table e farm-datatable-header

```vue
<template>
  <v-data-table
    v-model="selectedItems"
    :items="items"
    show-select
  >
    <template v-slot:header="{ props }">
      <farm-datatable-header
        v-model="allSelected"
        :headerProps="{
          everyItem: allSelected,
          someItems: someItems
        }"
        @toggleSelectAll="onToggleSelectAll"
      />
    </template>

    <template #item.data-table-select="{ item, isSelected }">
      <farm-checkbox
        :modelValue="isSelected"
        @input="(value) => toggleSelection(item, value)"
      />
    </template>
  </v-data-table>
</template>
```

## API

### Parâmetros
- `items`: `Ref<T[]>` - Array reativo com os itens da tabela
- `itemKey`: `keyof T` - Nome da propriedade que serve como chave única para cada item

### Retorno
- `selectedItems`: `Ref<T[]>` - Array com os itens selecionados
- `allSelected`: `ComputedRef<boolean>` - Se todos os itens estão selecionados
- `someItems`: `ComputedRef<boolean>` - Se alguns itens estão selecionados (seleção parcial)
- `toggleSelection`: `(item: T, value: boolean) => void` - Função para alternar seleção de um item
- `onToggleSelectAll`: `(value: boolean) => void` - Função para selecionar/desselecionar todos
- `selectedCount`: `ComputedRef<number>` - Quantidade de itens selecionados
- `totalItems`: `ComputedRef<number>` - Total de itens na tabela

## Benefícios
- Reduz duplicação de código
- Mantém consistência no comportamento de seleção entre diferentes tabelas
- Simplifica a integração entre v-data-table e farm-datatable-header
- Facilita a manutenção da lógica de seleção 