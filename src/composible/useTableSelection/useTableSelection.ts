import { ref, computed, Ref, ComputedRef } from 'vue';

import type { TableSelectionComposable } from './types';

export function useTableSelection<T>(
	items: Ref<T[]>,
	itemKey: keyof T
): TableSelectionComposable<T> {
	const selectedItems = ref<T[]>([]);

	const totalItems = computed(() => items.value.length);
	const selectedCount = computed(() => selectedItems.value.length);

	const allSelected = computed<boolean>({
		get() {
			return totalItems.value > 0 && selectedCount.value === totalItems.value;
		},
		set(value: boolean) {
			if (value) {
				selectedItems.value = [...items.value];
			} else {
				selectedItems.value = [];
			}
		},
	});

	const someItems = computed(
		() => selectedCount.value > 0 && selectedCount.value < totalItems.value
	);

	function toggleSelection(item: T, value: boolean) {
		const key = item[itemKey];
		if (value) {
			if (!selectedItems.value.some(i => i[itemKey] === key)) {
				selectedItems.value.push(item);
			}
		} else {
			const idx = selectedItems.value.findIndex(i => i[itemKey] === key);
			if (idx !== -1) selectedItems.value.splice(idx, 1);
		}
	}

	function onToggleSelectAll(value: boolean) {
		allSelected.value = value;
	}

	return {
		selectedItems,
		allSelected,
		someItems,
		toggleSelection,
		onToggleSelectAll,
		selectedCount,
		totalItems,
	};
}

export default useTableSelection;
