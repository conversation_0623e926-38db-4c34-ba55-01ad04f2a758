import { ref, computed } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { useStore } from '@/composible';
import { builderFormalization } from '@/helpers/builderFormalization';
import { getFormalization as getFormalizationService } from '@/services';

type UseFormalization = {
	isLoadingFormalization: {
		value: boolean;
	};
	isErrorFormalization: {
		value: boolean;
	};
	formalization: Array<any>;
	getFormalization: Function;
};

type Context = 'wallet' | 'sponsor';

export function useFormalization(context: Context = 'wallet'): UseFormalization {
	const store = useStore();
	const formalization = ref([]);
	const callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getFormalizationService(params),
		onSuccess: response => {
			formalization.value = builderFormalization(response);
			
			const totalSignatories = formalization.value.reduce(
				(acc, item) => acc + item.numberOfSignatories,
				0
			);
			const totalSignedSignatories = formalization.value.reduce(
				(acc, item) => acc + item.numberOfSignedSignatories,
				0
			);
			store.dispatch(`${context}/saveNumberOfTotalSignatories`, {
				total: totalSignatories,
			});

			store.dispatch(`${context}/saveNumberOfTotalSignedSignatories`, {
				total: totalSignedSignatories,
			});

			if (callFunc) callFunc(formalization.value, false);
		},
		onError: () => {
			console.log('error');
			if (callFunc) callFunc([], true);
		},
	});

	const isLoadingFormalization = computed(() => {
		return isLoading.value;
	});

	const isErrorFormalization = computed(() => {
		return isError.value;
	});

	function getFormalization(workingCapitalId: number): void {
		mutate(workingCapitalId);
	}

	return {
		isLoadingFormalization,
		isErrorFormalization,
		formalization,
		getFormalization,
	};
}
