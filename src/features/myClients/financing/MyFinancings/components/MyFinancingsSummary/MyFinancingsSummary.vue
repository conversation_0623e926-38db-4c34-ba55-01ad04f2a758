<template>
	<farm-card>
		<farm-card-content class="header-card-content d-flex flex-md-row md:flex-row pa-6">
			<div class="d-flex flex-row me-auto caption-container">
				<farm-idcaption copyText="" icon-box-color="success" icon="currency-usd">
					<template v-slot:title> Total em Títulos Emitidos </template>
					<template v-slot:subtitle>
						{{ financingSummary ? brl(financingSummary.sentTotal) : '-' }}
					</template>
				</farm-idcaption>

				<farm-idcaption copyText="" icon-box-color="success" icon="currency-usd">
					<template v-slot:title> Total a Emitir </template>
					<template v-slot:subtitle>
						{{ financingSummary ? brl(financingSummary.totalToSend) : '-' }}
					</template>
				</farm-idcaption>
			</div>

			<farm-idcaption
				class="header-card-content__item"
				icon="dots-horizontal-circle-outline"
				copyText=""
			>
				<template v-slot:title> Em Análise </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.inProgress : '-' }}
				</template>
			</farm-idcaption>
			<farm-idcaption
				class="header-card-content__item"
				icon="check-circle-outline"
				copyText=""
			>
				<template v-slot:title> Concluídos </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.completed : '-' }}
				</template>
			</farm-idcaption>
			<farm-idcaption
				class="header-card-content__item"
				icon="close-circle-outline"
				copyText=""
			>
				<template v-slot:title> Cancelados </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.canceled : '-' }}
				</template>
			</farm-idcaption>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';
import type { PropType } from 'vue/types';

import type { FinancingSummary } from '@/features/financing/types/Financing';

export default defineComponent({
	props: {
		financingSummary: {
			type: Object as PropType<FinancingSummary>,
			default: null,
		},
	},
	setup() {
		return {
			brl,
		};
	},
});
</script>

<style lang="scss" scoped>
@import '@/features/financing/components/MyFinancings/MyFinancingsSummary/MyFinancingsSummary.scss';
@import './MyFinancingsSummary.scss';
</style>
