export const FINANCING_STATUSES = {
	IN_PROGRESS: 1,
	SUCCESS: 2,
	FAILED: 3,
} as const;

export const DRAFT_STATUSES = {
	SELECT: 1,
	REVIEW: 2,
	FINISH: 3,
} as const;

export const ONBOARDING_STATUSES = {
	INVITE: 10,
	INVITED: 11,
	INCOMPLETE: 12,
	COMPLETED: 13,
	FAILED: 14,
	IN_ANALYSIS: 15,
	IN_PROGRESS: 16,
	IN_WAITING: 17,
	REJECTED: 18,
} as const;

export const ELIGIBILITY_DIALOG = {
	SIMULATION(internalInstance, retrySubmitMessage) {
		const defaultErrorMessage =
			'Não foi possível simular todas as solicitações. Tente novamente em alguns instantes.';

		internalInstance.$dialog.alert(
			{
				title: 'Atenção!',
				body: `
					<p>${defaultErrorMessage}</p>
					<br />
					${retrySubmitMessage}
					<br />
					<p>Se o erro persistir, entre em contato conosco:</p>
					<p><EMAIL></p>
					<p>(11) 3506-7800</p>
				`,
			},
			{
				html: true,
				okText: 'Voltar',
			}
		);
	},
	ERROR(internalInstance, retrySubmitMessage) {
		const defaultErrorMessage =
			'Não foi possível enviar todas as solicitações para a fila de execução. Tente novamente em alguns instantes.';

		internalInstance.$dialog.alert(
			{
				title: 'Atenção!',
				body: `
					<p>${defaultErrorMessage}</p>
					<br />
					${retrySubmitMessage}
					<br />
					<p>Se o erro persistir, entre em contato conosco:</p>
					<p><EMAIL></p>
					<p>(11) 3506-7800</p>`,
			},
			{
				html: true,
				okText: 'Voltar',
			}
		);
	},
};
