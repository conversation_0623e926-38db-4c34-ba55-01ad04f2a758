export const headers = [
	{
		text: 'NÚMERO DO PEDIDO',
		sortable: true,
		value: 'receivableNumber',
		align: 'left',
	},
	{
		text: 'VENCIMENTO DO PEDIDO',
		sortable: true,
		value: 'dueDate',
		align: 'center',
	},
	{
		text: 'VALOR DO PEDIDO',
		sortable: true,
		value: 'value',
		align: 'right',
	},
];

export const headerTableHome = [
	{
		text: 'STATUS',
		sortable: true,
		value: 'statusId',
		align: 'center',
		width: 160,
	},
	{
		text: 'NÚMERO',
		sortable: false,
		value: 'number',
		align: 'center',
	},
	{
		text: 'NOME/RAZÃO SOCIAL',
		sortable: true,
		value: 'draweeName',
		align: 'center',
	},
	{
		text: 'CPF/CNPJ',
		sortable: false,
		value: 'draweeDocument',
		align: 'center',
	},
	{
		text: 'NFe',
		sortable: true,
		value: 'typeId',
		align: 'center',
	},
	{
		text: 'DATA DE VENCIMENTO',
		sortable: true,
		value: 'expirationDate',
		align: 'center',
	},
	{
		text: 'VALOR NOMINAL',
		sortable: true,
		value: 'value',
		align: 'right',
	},
	{
		text: '',
		sortable: false,
		value: '',
		align: 'center',
	},
];

export const myFinancingsHeaders = [
	{
		text: 'QTD. DE PEDIDOS',
		sortable: true,
		value: 'receivablesQuantity',
		align: 'left',
		width: '195',
	},
	{
		text: 'ÚLTIMA ATUALIZAÇÃO',
		sortable: true,
		value: 'updatedAt',
		align: 'center',
	},
	{
		text: 'STATUS',
		sortable: false,
		value: 'status',
		align: 'center',
		width: 150,
	},
	{
		text: 'VENC. DO TÍTULO',
		sortable: true,
		value: 'dueDate',
		align: 'center',
	},
	{
		text: 'TOTAL EM PEDIDOS',
		sortable: true,
		value: 'total',
		align: 'center',
	},
	{
		text: 'TOTAL A PAGAR',
		sortable: true,
		value: 'totalPayment',
		align: 'center',
	},
	{
		text: '',
		value: 'openInNewTab',
		align: 'center',
		width: 70,
	},
];

export const myReceivablesHeaders = [
	{
		text: 'NÚMERO DO PEDIDO',
		sortable: true,
		value: 'receivableNumber',
		align: 'left',
	},
	{
		text: 'STATUS',
		sortable: true,
		value: 'status',
		align: 'center',
	},
	{
		text: 'VENCIMENTO DO PEDIDO',
		sortable: true,
		value: 'dueDate',
		align: 'center',
	},
	{
		text: 'VALOR DO PEDIDO',
		sortable: true,
		value: 'value',
		align: 'center',
	},
];

export const groupedReceivablesHeaders = [
	{
		text: '',
		value: 'status',
		sortable: false,
		align: 'left',
	},
	{
		text: 'Número do Pedido',
		sortable: true,
		value: 'receivableNumber',
		align: 'left',
	},
	{
		text: 'Vencimento',
		sortable: true,
		value: 'dueDate',
		align: 'center',
	},
	{
		text: 'Valor',
		sortable: true,
		value: 'value',
		align: 'center',
	},
	{
		text: 'Taxa',
		sortable: true,
		value: 'tax',
		align: 'center',
	},
	{
		text: 'Encargos',
		sortable: true,
		value: 'charges',
		align: 'center',
	},

	{
		text: 'Total',
		sortable: true,
		value: 'total',
		align: 'center',
	},
];

export const myClientHeaders = {
	myFinancingsHeaders: [
		{
			text: 'NOME DO SACADO',
			sortable: true,
			value: 'draweeName',
			align: 'left',
			width: '335',
		},
		{
			text: 'DOCUMENTO',
			sortable: true,
			value: 'draweeDocument',
			align: 'center',
			width: '191',
		},
		...myFinancingsHeaders,
	],
	myReceivablesHeaders: [
		{
			text: 'NOME DO SACADO',
			sortable: true,
			value: 'draweeName',
			align: 'left',
			width: '335',
		},
		{
			text: 'DOCUMENTO',
			sortable: true,
			value: 'draweeDocument',
			align: 'center',
			width: '191',
		},
		...myReceivablesHeaders,
	],
};
