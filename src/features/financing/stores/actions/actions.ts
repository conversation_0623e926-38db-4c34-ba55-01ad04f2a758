import {
	RequestStatusEnum,
	errorBuilder,
	fetchDefaultParser,
	fetchDefaultParserPagination,
} from '@farm-investimentos/front-mfe-libs-ts';

import {
	getReceivablesListByProduct as getReceivablesListByProductService,
	getReceivablesSummary as getReceivablesSummaryService,
	getFinancingList as getFinancingListService,
	getFinancingSummary as getFinancingSummaryService,
	getFinancingSummaryAll as getFinancingSummaryAllService,
	getAllowedDates as getAllowedDatesService,
	createDraftFinancing as createDraftFinancingService,
	editDraftStep as editDraftStepService,
	simulateFinancingPrice as simulateFinancingPriceService,
	deleteDraftFinancing as deleteDraftFinancingService,
	checkOnboardingStatus as checkOnboardingStatusService,
	checkPreEligibility as checkPreEligibilityService,
	getFinancingDetail as getFinancingDetailService,
	deleteReceivableFromFinancing as deleteReceivableFromFinancingService,
} from '@/features/financing/services';
import { receivablesSummaryFromAPI, financingSummaryFromAPI } from '@/helpers/dtos';
import financingPreEligibilityFromAPI from '@/helpers/dtos/financingPreEligibilityFromAPI';

import {
	CheckOnboardingStatusRequest,
	CreateDraftFinancingRequest,
	DeleteDraftFinancingRequest,
	EditDraftStepRequest,
	GetAllowedDatesRequest,
	GetFinancingListRequest,
	GetFinancingSummaryRequest,
	GetReceivablesByProductListRequest,
	SimulateFinancingPriceRequest,
	GetPreEligibilityCheckerRequest,
	CheckPreEligibilityRequest,
	FinancingDetailRequest,
	DeleteReceivableFromFinancing,
	GetReceivablesSummaryRequest,
} from '../../services/types';

export default {
	async getReceivablesList({ commit }, request: GetReceivablesByProductListRequest) {
		commit('setReceivablesListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getReceivablesListByProductService(request);

			fetchDefaultParserPagination(commit, data, null, 'ReceivablesList');
			commit('setReceivablesListPageable', data.data.pageable);
		} catch (err) {
			commit('setReceivablesListRequestStatus', errorBuilder(err));
		}
	},

	async getReceivablesSummary({ commit }, request: GetReceivablesSummaryRequest) {
		commit('setReceivablesSummaryRequestStatus', RequestStatusEnum.START);
		try {
			const response = await getReceivablesSummaryService(request);

			fetchDefaultParser(commit, response, receivablesSummaryFromAPI, 'ReceivablesSummary');
		} catch (err) {
			commit('setReceivablesSummaryRequestStatus', errorBuilder(err));
		}
	},

	async getReceivablesByProductList({ commit }, request: GetReceivablesByProductListRequest) {
		commit('setReceivablesByProductListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getReceivablesListByProductService(request);

			fetchDefaultParserPagination(commit, data, null, 'ReceivablesByProductList');
			commit('setReceivablesByProductListPageable', data.data.pageable);
		} catch (err) {
			commit('setReceivablesByProductListRequestStatus', errorBuilder(err));
		}
	},

	async getFinancingList({ commit }, request: GetFinancingListRequest) {
		commit('setFinancingListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getFinancingListService(request);

			fetchDefaultParserPagination(commit, data, null, 'FinancingList');
			commit('setFinancingListPageable', data.data.pageable);
		} catch (err) {
			commit('setFinancingListRequestStatus', errorBuilder(err));
		}
	},

	async getFinancingSummary({ commit }, request: GetFinancingSummaryRequest) {
		commit('setFinancingSummaryRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getFinancingSummaryService(request);

			fetchDefaultParser(commit, data, financingSummaryFromAPI, 'FinancingSummary');
		} catch (err) {
			commit('setFinancingSummaryRequestStatus', errorBuilder(err));
		}
	},

	async getAllowedDates({ commit, state }, request: GetAllowedDatesRequest) {
		commit('setRequestAllowedDatesRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getAllowedDatesService(request);

			if (!state.requestAllowedDates) {
				commit('setRequestAllowedDates', {});
			}

			commit('setRequestAllowedDates', {
				...state.requestAllowedDates,
				[(request.query as Record<string, string>).data]: data,
			});

			commit('setRequestAllowedDatesRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (err) {
			commit('setRequestAllowedDatesRequestStatus', errorBuilder(err));
		}
	},

	async simulateFinancingPrice({ commit }, request: SimulateFinancingPriceRequest) {
		commit('setFinancingPriceRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await simulateFinancingPriceService(request);

			fetchDefaultParser(commit, data.simulations, null, 'FinancingPrice');
		} catch (err) {
			commit('setFinancingPriceRequestStatus', errorBuilder(err));
		}
	},

	async createDraftFinancing({ commit }, request: CreateDraftFinancingRequest) {
		commit('setDraftFinancingRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await createDraftFinancingService(request);

			fetchDefaultParser(commit, data, null, 'DraftFinancing');
		} catch (err) {
			commit('setDraftFinancingRequestStatus', errorBuilder(err));
		}
	},

	async editDraftStep({ commit }, request: EditDraftStepRequest) {
		commit('setEditDraftStepRequestStatus', RequestStatusEnum.START);
		commit('setDraftFinancingRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await editDraftStepService(request);

			fetchDefaultParser(commit, data, null, 'DraftFinancing');
			commit('setEditDraftStepRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (err) {
			commit('setEditDraftStepRequestStatus', errorBuilder(err));
			commit('setDraftFinancingRequestStatus', errorBuilder(err));
		}
	},

	async deleteDraftFinancing({ commit }, request: DeleteDraftFinancingRequest) {
		commit('setDeleteDraftFinancingRequestStatus', RequestStatusEnum.START);
		try {
			await deleteDraftFinancingService(request);

			commit('setDeleteDraftFinancingRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (err) {
			commit('setDeleteDraftFinancingRequestStatus', errorBuilder(err));
		}
	},

	async checkOnboardingStatus({ commit }, request: CheckOnboardingStatusRequest) {
		commit('setOnboardingStatusRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await checkOnboardingStatusService(request);

			fetchDefaultParser(commit, data, null, 'OnboardingStatus');
		} catch (err) {
			commit('setOnboardingStatus', err?.response?.data);
			commit('setOnboardingStatusRequestStatus', errorBuilder(err));
		}
	},

	getPreEligibilityChecker({ commit }, request: GetPreEligibilityCheckerRequest) {
		commit('setPreEligibilityRequestStatus', RequestStatusEnum.START);
		try {
			return checkPreEligibilityService(request);
		} catch (err) {
			commit('setPreEligibilityRequestStatus', errorBuilder(err));
		}
	},

	async checkPreEligibility({ commit }, request: CheckPreEligibilityRequest) {
		commit('setPreEligibilityRequestStatus', RequestStatusEnum.START);

		try {
			const response = await Promise.all(request.promises);

			fetchDefaultParser(commit, response, financingPreEligibilityFromAPI, 'PreEligibility');
		} catch (err) {
			commit('setPreEligibilityRequestStatus', errorBuilder(err));
		}
	},

	async checkEligibility({ commit }, request: CheckPreEligibilityRequest) {
		commit('setPreEligibilityRequestStatus', RequestStatusEnum.START);

		try {
			const responses = await Promise.allSettled(request.promises);

			fetchDefaultParser(commit, responses, financingPreEligibilityFromAPI, 'PreEligibility');
		} catch (err) {
			commit('setPreEligibilityRequestStatus', errorBuilder(err));
		}
	},

	async getFinancingDetail({ commit }, request: FinancingDetailRequest) {
		commit('setFinancingDetailRequestStatus', RequestStatusEnum.START);

		try {
			const { data } = await getFinancingDetailService(request);

			fetchDefaultParser(commit, data.data.content, null, 'FinancingDetail');
		} catch (err) {
			commit('setFinancingDetailRequestStatus', errorBuilder(err));
		}
	},

	async deleteReceivableFromFinancing({ commit }, request: DeleteReceivableFromFinancing) {
		commit('setDeleteReceivableFromFinancingRequestStatus', RequestStatusEnum.START);
		try {
			await deleteReceivableFromFinancingService(request);

			commit('setDeleteReceivableFromFinancingRequestStatus', RequestStatusEnum.SUCCESS);
		} catch (err) {
			commit('setDeleteReceivableFromFinancingRequestStatus', errorBuilder(err));
		}
	},
};
