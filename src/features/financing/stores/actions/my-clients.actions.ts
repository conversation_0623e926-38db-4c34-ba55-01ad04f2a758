import {
	errorBuilder,
	fetchDefaultParser,
	fetchDefaultParserPagination,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';

import {
	getFinancingSummaryAll as getFinancingSummaryAllService,
	getFinancingListAll as getFinancingListAllService,
	getReceivablesListAll as getReceivablesListAllService,
	getReceivablesSummaryAll as getReceivablesSummaryAllService,
	getFinancingDetailAll as getFinancingDetailAllService,
} from '@/features/financing/services';
import { financingSummaryFromAPI, receivablesSummaryFromAPI } from '@/helpers/dtos';

import type {
	FinancingDetailAllRequest,
	GetFinancingListAllRequest,
	GetFinancingSummaryAllRequest,
	GetReceivablesByProductListAllRequest,
	GetReceivablesSummaryAllRequest,
} from '../../services/types';

export default {
	async getFinancingSummaryAll({ commit }, request: GetFinancingSummaryAllRequest) {
		commit('setFinancingSummaryRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getFinancingSummaryAllService(request);

			fetchDefaultParser(commit, data, financingSummaryFromAPI, 'FinancingSummary');
		} catch (err) {
			commit('setFinancingSummaryRequestStatus', errorBuilder(err));
		}
	},

	async getFinancingListAll({ commit }, request: GetFinancingListAllRequest) {
		commit('setFinancingListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getFinancingListAllService(request);

			fetchDefaultParserPagination(commit, data, null, 'FinancingList');
			commit('setFinancingListPageable', data.data.pageable);
		} catch (err) {
			commit('setFinancingListRequestStatus', errorBuilder(err));
		}
	},

	async getReceivablesListAll({ commit }, request: GetReceivablesByProductListAllRequest) {
		commit('setReceivablesListRequestStatus', RequestStatusEnum.START);
		try {
			const { data } = await getReceivablesListAllService(request);

			fetchDefaultParserPagination(commit, data, null, 'ReceivablesList');
			commit('setReceivablesListPageable', data.data.pageable);
		} catch (err) {
			commit('setReceivablesListRequestStatus', errorBuilder(err));
		}
	},

	async getReceivablesSummaryAll({ commit }, request: GetReceivablesSummaryAllRequest) {
		commit('setReceivablesSummaryRequestStatus', RequestStatusEnum.START);
		try {
			const response = await getReceivablesSummaryAllService(request);

			fetchDefaultParser(commit, response, receivablesSummaryFromAPI, 'ReceivablesSummary');
		} catch (err) {
			commit('setReceivablesSummaryRequestStatus', errorBuilder(err));
		}
	},

	async getFinancingDetailAll({ commit }, request: FinancingDetailAllRequest) {
		commit('setFinancingDetailRequestStatus', RequestStatusEnum.START);

		try {
			const { data } = await getFinancingDetailAllService(request);

			fetchDefaultParser(commit, data.data.content, null, 'FinancingDetail');
		} catch (err) {
			commit('setFinancingDetailRequestStatus', errorBuilder(err));
		}
	},
};
