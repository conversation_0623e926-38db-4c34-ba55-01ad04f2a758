import { mutationsBuilder } from '@farm-investimentos/front-mfe-libs-ts';

import { basicKeys } from '@/features/financing/stores/basicKeys';
import { requestStatusKeys } from '@/features/financing/stores/requestStatusKeys';

export default {
	...mutationsBuilder(basicKeys),
	...mutationsBuilder(requestStatusKeys),
	setHasFinishedDraftFinancing(state, hasFinished) {
		state.hasFinishedDraftFinancing = hasFinished;
	},
};
