import type { Receivable } from './Receivable';

export type Financing = {
	id: number;
	charges: number;
	total: number;
	totalPayment: number;
	dueDate: string;
	receivablesQuantity: number;
	receivables: Receivable[];
	status: number;
	createdAt: string;
	updatedAt: string;
	closedAt: string | null;
};

export type FinancingSummary = {
	productName: string;
	inProgress: number;
	completed: number;
	canceled: number;
};

export type DraftFinancing = {
	id: number;
	idProduct: number;
	peopleId: number;
	availableLimit: number;
	cognitoUserId: number;
	financings: Financing[] | null;
	receivables: Receivable[] | null;
	step: 1 | 2 | 3;
	usedLimit: number;
};
