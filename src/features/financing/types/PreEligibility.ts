type EligibleReceivable = {
	numberOrder: string;
	due: string[];
	amount: number;
};

export type PreEligibility = {
	eligibles: EligibleReceivable[];
	quantityApproved: number;
	total: number;
	queueId?: string | null;
	error: string | null;
};

export type GroupedPreEligibility = {
	eligibles: EligibleReceivable[];
	quantityApproved: number;
	total: number;
	queueId: PreEligibility['queueId'][];
	error: PreEligibility['error'][];
};
