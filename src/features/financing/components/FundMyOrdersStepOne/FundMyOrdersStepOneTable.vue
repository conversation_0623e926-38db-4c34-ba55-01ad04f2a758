<template>
	<v-data-table
		v-model="tableSelectedRows"
		id="table-receivables-list"
		ref="tableReceivablesList"
		hide-default-header
		hide-default-footer
		item-value="id"
		show-select
		item-selectable="selectable"
		:headers="headers"
		:items="items"
		:server-items-length="pagination ? pagination.totalElements : 0"
	>
		<template slot="no-data">
			<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" />
		</template>
		<template v-slot:header="{ props }">
			<farm-datatable-header
				v-model="props.everyItem"
				item-selectable="selectable"
				show-checkbox
				first-selected
				:headers="props.headers"
				:header-props="props"
				:selected-index="2"
				:sort-click="sortClicked"
				@onClickSort="onSortTable"
				@toggleSelectAll="toggleSelectAll"
			/>
		</template>
		<template v-slot:[`item.data-table-select`]="{ item, isSelected, select }">
			<v-simple-checkbox
				:value="isSelected"
				:readonly="item.selectable"
				:disabled="item.selectable"
				@input="select($event)"
			/>
		</template>
		<template v-slot:[`item.dueDate`]="{ item }">
			{{ defaultDateFormat(item.dueDate) }}
		</template>
		<template v-slot:[`item.value`]="{ item }">{{ brl(item.value) }} </template>
		<template v-slot:footer>
			<farm-datatable-paginator
				v-if="items.length > 0"
				class="mt-6 mb-n6"
				:page="page"
				:totalPages="pagination.totalPages"
				:initialLimitPerPage="pagination.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>
	</v-data-table>
</template>

<script lang="ts">
import { onBeforeMount, defineComponent, ref, watch } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';
import type { Ref, PropType } from 'vue/types';

import { useAnalytics } from '@/composible';

import { headers } from '../../configurations/headers';
import type { Receivable } from '../../types';

import { useFundMyOrdersStepOne } from './composables/useFundMyOrdersStepOne';
import { useFundMyOrdersStepOneModal } from './composables/useFundMyOrdersStepOneModal';

export default defineComponent({
	props: {
		items: {
			type: Array as PropType<Receivable[]>,
			required: true,
		},
	},
	setup(props) {
		const { trigger } = useAnalytics();
		const sortClicked = ref([]);
		const tableReceivablesList: Ref<Receivable[]> = ref();

		const { sort, pagination, page, onSortTable, onChangePage, onChangePageLimit } =
			useFundMyOrdersStepOneModal();
		const { selectedRequests, tableSelectedRows } = useFundMyOrdersStepOne();

		const toggleSelectAll = (value: boolean | null) => {
			if (!value) {
				tableSelectedRows.value = [];

				return;
			}

			setTimeout(() => {
				for (let i = 0; i < props.items.length; i++) {
					const item = props.items[i];

					// @ts-expect-error
					tableReceivablesList.value.select(item);
				}
			}, 1);
		};

		onBeforeMount(() => {
			tableSelectedRows.value = selectedRequests.value;
		});

		watch(
			() => pagination.value?.pageSize,
			(newValue, oldValue) => {
				if (!newValue || !oldValue) {
					return;
				}
				if (oldValue !== newValue) {
					trigger({
						event: 'update_per_page',
						payload: {
							event_category: 'title_emission',
							event_label: 'Tabela de seleção de recebíveis',
							description: 'Alterou quantidade de itens por página na tabela',
							perPage: newValue,
						},
					});
				}
			}
		);
		watch(page, (newValue, oldValue) => {
			if (!newValue) {
				return;
			}
			if (oldValue !== newValue) {
				trigger({
					event: 'update_page_number',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tabela de seleção de recebíveis',
						description: `Paginou a tabela para ${newValue}`,
						page: newValue,
					},
				});
			}
		});
		watch(
			sort,
			newValue => {
				if (!newValue) {
					return;
				}
				trigger({
					event: 'update_sort',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tabela de seleção de recebíveis',
						description: `Ordenou a tabela por ${newValue.orderby} em ordenação ${
							newValue.order === 'ASC' ? 'ascendente' : 'descendente'
						}`,
						column: newValue.orderby,
						order: newValue.order,
					},
				});
			},
			{
				deep: true,
			}
		);

		return {
			pagination,
			headers,
			page,
			tableSelectedRows,
			tableReceivablesList,
			sortClicked,
			brl,
			defaultDateFormat,
			toggleSelectAll,
			onSortTable,
			onChangePage,
			onChangePageLimit,
		};
	},
});
</script>
