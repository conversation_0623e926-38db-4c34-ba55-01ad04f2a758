<template>
	<farm-card class="request-card" :class="{ 'request-card--errored': errored }">
		<farm-card-content class="d-flex flex-row justify-space-between px-6">
			<div class="d-flex flex-row">
				<farm-idcaption class="request-card__item" copy-text="">
					<template #title>Número do Pedido</template>
					<template #subtitle>
						<span class="font-weight-medium">{{ request.receivableNumber }}</span>
					</template>
				</farm-idcaption>

				<farm-idcaption class="request-card__item" copy-text="">
					<template #title>Vencimento</template>
					<template #subtitle>
						<span class="font-weight-medium">
							{{ defaultDateFormat(request.dueDate) }}
						</span>
					</template>
				</farm-idcaption>

				<farm-idcaption class="request-card__item" copy-text="">
					<template #title>Valor</template>
					<template #subtitle>
						<span class="font-weight-medium">{{ brl(request.value) }}</span>
					</template>
				</farm-idcaption>

				<farm-idcaption class="request-card__item" copy-text="">
					<template #title>Taxa</template>
					<template #subtitle>
						<span class="font-weight-medium">{{ taxText }}</span>
					</template>
				</farm-idcaption>

				<farm-idcaption class="request-card__item" copy-text="">
					<template #title>Encargos</template>
					<template #subtitle>
						<span class="font-weight-medium">{{ chargesText }}</span>
					</template>
				</farm-idcaption>

				<farm-idcaption class="request-card__item" copy-text="">
					<template #title>Total</template>
					<template #subtitle>
						<span class="font-weight-medium">
							{{ totalText }}
						</span>
					</template>
				</farm-idcaption>
			</div>

			<div class="d-flex flex-column items-center request-card__form ml-auto">
				<label class="d-flex flex-row mb-2" :for="`request-date-id-${request.id}`">
					<farm-caption color="neutral-darken" weight="600" class="mr-2">
						Vencimento do título
						<farm-caption tag="span" color="error">*</farm-caption>
					</farm-caption>
					<farm-tooltip
						@mouseover.native="trackTooltipHover"
						@mouseout.native="trackTooltipHover"
					>
						Seus pedidos serão agrupados pelo vencimento do pedido e pelo vencimento
						escolhido por você.
						<template #activator>
							<farm-icon size="16" color="gray">help-circle</farm-icon>
						</template>
					</farm-tooltip>
				</label>

				<farm-input-datepicker
					position="top"
					required
					readonly
					:key="datepickerKey"
					:inputId="`request-date-id-${request.id}`"
					:allowed-dates="checkForAllowedDates"
					:picker-date.sync="firstMonthToShow"
					v-model="date"
					@input="updateDate"
				/>
			</div>

			<button class="d-flex ml-6" @click="onRemoveRequest(request.id)">
				<farm-icon class="align-self-center">delete</farm-icon>
			</button>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, ref, computed, watch, toRefs } from 'vue';

import { defaultDateFormat, unFormatDate, brl } from '@farm-investimentos/front-mfe-libs-ts';
import type { Ref, PropType } from 'vue/types';

import { useGetter, useSelectedProductId, useStore, useAnalytics } from '@/composible';
import { debounce } from '@/helpers/debounce';

import { GetAllowedDatesRequest } from '../../services/types';
import type { Receivable, ReceivableSimulation } from '../../types';
import { useFundMyOrdersStepOne } from '../FundMyOrdersStepOne/composables/useFundMyOrdersStepOne';

export default defineComponent({
	props: {
		request: {
			type: Object as PropType<Receivable>,
			required: true,
		},
		errored: {
			type: Boolean,
			required: true,
		},
	},
	setup(props) {
		const store = useStore();
		const productId = useSelectedProductId().value;
		const { trigger } = useAnalytics();
		const { selectedRequests, financingPrice, draftFinancing } = useFundMyOrdersStepOne();
		const { cancel, debouncedFunction } = debounce(
			() =>
				trigger({
					event: 'see_tooltip',
					action: 'mouseover',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tooltip do vencimento do título',
						description: 'Abertura de tooltip',
					},
				}),
			750
		);

		const { request: selectedRequest }: { request: Ref<Receivable> } = toRefs(props);

		const datepickerKey = ref(0);
		const formattedDate = unFormatDate(defaultDateFormat(selectedRequest.value.dueDate));
		const date = ref('');
		const firstMonthToShow = ref('');

		const requestAllowedDates = computed(useGetter('financing', 'requestAllowedDates'));
		const availableDates = computed(() =>
			requestAllowedDates.value && formattedDate in requestAllowedDates.value
				? requestAllowedDates.value[formattedDate]
				: []
		);
		const taxText = computed(() =>
			selectedRequest.value.tax ? `${selectedRequest.value.tax}% a.m.` : '-'
		);
		const chargesText = computed(() => {
			return selectedRequest.value.charges ? brl(selectedRequest.value.charges) : '-';
		});
		const totalText = computed(() => {
			const total = selectedRequest.value.value + selectedRequest.value.charges;
			return typeof total === 'number' && selectedRequest.value.charges ? brl(total) : '-';
		});

		const updateDate = async (date: string) => {
			trigger({
				event: 'updated_due_date',
				payload: {
					event_category: 'title_emission',
					event_label: 'Datepicker do recebível',
					description: 'Escolheu uma data para o recebível',
					receivable_id: selectedRequest.value.id,
					receivable_number: selectedRequest.value.receivableNumber,
					due_date: defaultDateFormat(date),
				},
			});

			selectedRequest.value.financingDate = date || null;

			const payload = [
				{
					receivableId: selectedRequest.value.id,
					value: selectedRequest.value.value,
					dueDate: date,
				} as ReceivableSimulation,
			];

			await store.dispatch('financing/simulateFinancingPrice', {
				payload,
			});

			if (!financingPrice.value?.length) {
				return;
			}

			const response = financingPrice.value[0];

			selectedRequest.value.charges = response.charges;
			selectedRequest.value.tax = response.tax;
			selectedRequest.value.tir = response.tir;
		};
		const fetchAllowedDates = (date: typeof formattedDate) => {
			const request: GetAllowedDatesRequest = {
				params: {
					productId,
				},
				query: {
					data: date,
				},
			};

			store.dispatch('financing/getAllowedDates', request);
		};
		const onRemoveRequest = async (id: Receivable['id']) => {
			const request = {
				params: {
					productId,
					draftId: draftFinancing.value.id,
					receivableId: id,
				},
			};

			store.dispatch('financing/deleteReceivableFromFinancing', request);

			selectedRequests.value = selectedRequests.value.filter(request => request.id !== id);
		};
		const checkForAllowedDates = value => availableDates.value.includes(value);
		const setFirstMonthToShow = dates => {
			const separator = '-';
			const splittedDate = dates[0].split(separator);

			firstMonthToShow.value = `${splittedDate[0]}${separator}${splittedDate[1]}`;
			datepickerKey.value++;
		};
		const trackTooltipHover = (event: MouseEvent) => {
			if (event.type === 'mouseout') {
				cancel();
				return;
			}

			debouncedFunction();
		};

		watch(availableDates, newValue => {
			if (!newValue?.length) {
				return;
			}

			setFirstMonthToShow(newValue);
		});

		onBeforeMount(async () => {
			date.value = !Array.isArray(selectedRequest.value.financingDate)
				? selectedRequest.value.financingDate
				: '';

			if (availableDates.value.length) {
				setFirstMonthToShow(availableDates.value);
			} else {
				fetchAllowedDates(formattedDate);
			}
		});

		return {
			date,
			datepickerKey,
			firstMonthToShow,
			taxText,
			chargesText,
			totalText,
			trackTooltipHover,
			checkForAllowedDates,
			onRemoveRequest,
			updateDate,
			defaultDateFormat,
			brl,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './styles/FundMyOrdersStepOneRequestCard.scss';
</style>
