<template>
	<farm-box>
		<farm-row class="mb-6">
			<farm-col cols="12">
				<farm-heading type="6">
					Escolha os pedidos e selecione uma data de vencimento para cada um:
				</farm-heading>
				<farm-subtitle type="2" variation="regular">
					Os pedidos selecionados serão agrupados pelo vencimento do pedido e pelo
					vencimento escolhido por você.
				</farm-subtitle>
			</farm-col>
		</farm-row>
		<FinancingLimitContainer
			:receivables="selectedRequests"
			:can-advance-to-next-step="canAdvanceToNextStep"
		/>

		<farm-btn
			v-if="!canPickReceivables"
			full
			outlined
			class="requests-button my-6 py-6 px-4"
			@click="onOpenModal"
		>
			<farm-icon size="md">clipboard-text</farm-icon> Adicionar ou alterar pedidos
		</farm-btn>

		<farm-alert-reload
			v-else
			class="alert-reload my-6 mx-auto"
			label="Ocorreu um erro ao buscar seu limite disponível"
			@onClick="createOrRetrieveDraft"
		/>

		<farm-alertbox
			v-if="!isAllReceivablesApproved"
			icon="alert-circle-outline"
			color="error"
			class="mb-6"
		>
			<farm-subtitle color="error" color-variation="darken">
				Os pedidos em destaque não estão elegíveis para continuar com a criação da sua
				operação.
			</farm-subtitle>
		</farm-alertbox>

		<ContactCard v-if="!isAllReceivablesApproved" class="mb-3" />

		<FundMyOrdersStepOneRequestList />

		<farm-row extra-decrease>
			<farm-container-footer class="d-flex align-center justify-end">
				<farm-btn outlined class="mr-2" @click="handleDeleteDraft">Cancelar</farm-btn>

				<farm-tooltip v-if="continueButtonHasTooltip">
					{{ continueButtonTooltipMessage }}
					<template #activator>
						<farm-btn
							:disabled="!canAdvanceToNextStep || hasExceededLimit"
							@click="handlePreEligibility"
						>
							Continuar
						</farm-btn>
					</template>
				</farm-tooltip>
				<farm-btn
					v-else
					:disabled="!canAdvanceToNextStep || triggeredPreEligibility"
					@click="handlePreEligibility"
				>
					Continuar
				</farm-btn>
			</farm-container-footer>
		</farm-row>

		<FundMyOrdersStepOneModal v-if="isOpened" :value="isOpened" :onClose="onCloseModal" />
		<FundMyOrdersStepOneEligibilityModal
			v-if="isEligibilityModalOpen"
			:value="isEligibilityModalOpen"
			:onClose="onCloseEligibilityModal"
		/>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-box>
</template>
<script lang="ts">
import { defineAsyncComponent, defineComponent, onBeforeMount } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import ContactCard from '@/components/ContactCard';

import FinancingLimitContainer from './../FinancingLimitContainer';
import FundMyOrdersLimit from './../FundMyOrdersLimit';
import FundMyOrdersStepOneRequestList from './FundMyOrdersStepOneRequestList.vue';
import FundMyOrdersStepOneTable from './FundMyOrdersStepOneTable.vue';
import { useFundMyOrdersStepOne } from './composables/useFundMyOrdersStepOne';
import { useFundMyOrdersStepOneModal } from './composables/useFundMyOrdersStepOneModal';

const FundMyOrdersStepOneModal = defineAsyncComponent(
	() => import('./FundMyOrdersStepOneModal.vue')
);
const FundMyOrdersStepOneEligibilityModal = defineAsyncComponent(
	() => import('./FundMyOrdersStepOneEligibilityModal.vue')
);

export default defineComponent({
	components: {
		FundMyOrdersStepOneModal,
		FundMyOrdersStepOneEligibilityModal,
		FundMyOrdersStepOneTable,
		FundMyOrdersStepOneRequestList,
		FinancingLimitContainer,
		FundMyOrdersLimit,
		ContactCard,
	},
	setup() {
		const {
			allRequestsAreFilledWithDate,
			canPickReceivables,
			canAdvanceToNextStep,
			continueButtonHasTooltip,
			continueButtonTooltipMessage,
			draftFinancing,
			financingPriceHasError,
			hasExceededLimit,
			isEligibilityModalOpen,
			isLoading,
			selectedRequests,
			triggeredPreEligibility,
			handlePreEligibility,
			createOrRetrieveDraft,
			handleDeleteDraft,
			handleNextDraftStep,
			onCloseEligibilityModal,
			isAllReceivablesApproved,
			resetStepOne,
		} = useFundMyOrdersStepOne();

		const { isOpened, onOpenModal, onCloseModal } = useFundMyOrdersStepOneModal();

		onBeforeMount(() => {
			selectedRequests.value = draftFinancing.value?.receivables || [];
			resetStepOne();
		});

		return {
			financingPriceHasError,
			triggeredPreEligibility,
			isEligibilityModalOpen,
			hasExceededLimit,
			canPickReceivables,
			continueButtonHasTooltip,
			continueButtonTooltipMessage,
			allRequestsAreFilledWithDate,
			isAllReceivablesApproved,
			isLoading,
			selectedRequests,
			isOpened,
			canAdvanceToNextStep,
			handlePreEligibility,
			createOrRetrieveDraft,
			handleDeleteDraft,
			handleNextDraftStep,
			onOpenModal,
			onCloseModal,
			onCloseEligibilityModal,
			brl,
		};
	},
});
</script>
<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('#table-receivables-list', 1, (0));
</style>
<style lang="scss" scoped>
@import './styles/FundMyOrdersStepOne.scss';
</style>
