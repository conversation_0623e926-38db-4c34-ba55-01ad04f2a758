<template>
	<farm-modal :value="value" :offset-bottom="68" :offset-top="48" @input="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Seleção de pedidos" @onClose="onClose" />
		</template>

		<template v-slot:content>
			<farm-row class="mt-4">
				<farm-col cols="12" md="6">
					<farm-form-mainfilter
						label="Buscar Pedido"
						:has-extra-filters="false"
						@onInputChange="onInputChangeMainFilter"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-box>
					<FundMyOrdersStepOneTable :items="receivablesByProductList" />
				</farm-box>
			</farm-row>
			<farm-loader v-if="isLoading" mode="overlay" />
		</template>

		<template v-slot:footer>
			<div class="d-flex justify-end align-center pa-4">
				<farm-btn plain @click="onClose">Voltar</farm-btn>
				<farm-btn @click="sendSelection">Confirmar seleção</farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount } from 'vue';

import FundMyOrdersStepOneTable from './FundMyOrdersStepOneTable.vue';
import { useFundMyOrdersStepOneModal } from './composables/useFundMyOrdersStepOneModal';

export default defineComponent({
	components: { FundMyOrdersStepOneTable },
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		onClose: {
			type: Function,
			default: () => {},
		},
	},
	setup({ onClose }) {
		const {
			isLoading,
			receivablesByProductList,
			receivablesByProductListRequest,
			receivablesByProductListPageable,
			onInputChangeMainFilter,
			fetchReceivablesByProductList,
			sendSelection,
		} = useFundMyOrdersStepOneModal();

		onBeforeMount(() => {
			const defaultQuery = receivablesByProductListRequest.query as Record<string, string>;

			const query = {
				...defaultQuery,
				limit: receivablesByProductListPageable.value?.pageSize || defaultQuery.limit,
			};

			fetchReceivablesByProductList({
				...receivablesByProductListRequest,
				query,
			});
		});

		return {
			receivablesByProductList,
			isLoading,
			onInputChangeMainFilter,
			sendSelection: () => sendSelection(onClose),
		};
	},
});
</script>
