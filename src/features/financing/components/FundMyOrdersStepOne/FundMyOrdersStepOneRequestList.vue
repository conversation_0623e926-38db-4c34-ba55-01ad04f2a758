<template>
	<farm-row yGridGutters>
		<farm-col v-for="request in orderedSelectedReceivables" :key="request.id" cols="12">
			<FundMyOrdersStepOneRequestCard
				:request="request"
				:errored="isNotApproved(request.id)"
			/>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, computed, watch } from 'vue';

import type { ComputedRef } from 'vue/types';

import type { Receivable } from '../../types';
import { useFundMyOrdersStepOne } from '../FundMyOrdersStepOne/composables/useFundMyOrdersStepOne';

import FundMyOrdersStepOneRequestCard from './FundMyOrdersStepOneRequestCard.vue';

type ReceivablePart = Pick<Receivable, 'id' | 'financingDate' | 'value'>;

export default defineComponent({
	components: {
		FundMyOrdersStepOneRequestCard,
	},
	setup() {
		const { selectedRequests, approvedReceivables, triggeredPreEligibility } =
			useFundMyOrdersStepOne();

		const orderedSelectedReceivables: ComputedRef<Receivable[]> = computed(() => {
			return [...selectedRequests.value].sort(
				(a: any, b: any) => +new Date(a.dueDate) - +new Date(b.dueDate)
			);
		});
		const dates: ComputedRef<ReceivablePart[]> = computed(() =>
			selectedRequests.value.map(({ id, financingDate, value }) => ({
				id,
				financingDate,
				value,
			}))
		);
		const approvedReceivablesIds: ComputedRef<Receivable['id'][]> = computed(() =>
			approvedReceivables.value.map(({ id }) => id)
		);

		const isNotApproved = (receivableId: Receivable['id']) =>
			!approvedReceivablesIds.value.includes(receivableId) && triggeredPreEligibility.value;

		// reset approved receivables (and error states) when change dates or receivables
		watch([selectedRequests, dates], () => {
			triggeredPreEligibility.value = false;
			approvedReceivables.value = [];
		});

		return {
			orderedSelectedReceivables,
			isNotApproved,
		};
	},
});
</script>
