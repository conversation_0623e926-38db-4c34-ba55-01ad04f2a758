import { ref, computed } from 'vue';

import type { ComputedRef } from 'vue/types';

import {
	useGetter,
	useIsLoading,
	useSelectedProductId,
	useStore,
	usePageable,
	useAnalytics,
} from '@/composible';
import type { GetFinancingListRequest } from '@/features/financing/services/types';
import type { Receivable } from '@/features/financing/types';

import { useFundMyOrdersStepOne } from '../useFundMyOrdersStepOne/useFundMyOrdersStepOne';

export function useFundMyOrdersStepOneModal({ initialModalValue = false } = {}) {
	const productId = useSelectedProductId().value;
	const store = useStore();
	const { trigger } = useAnalytics();
	const { sort, selectedRequests, tableSelectedRows } = useFundMyOrdersStepOne();

	const receivablesByProductListRequest: GetFinancingListRequest = {
		params: {
			productId,
		},
		query: {
			page: '0',
			limit: '10',
			status: 'DISPONIVEL',
		},
	};

	const isOpened = ref(initialModalValue);

	const receivablesByProductList: ComputedRef<Receivable[]> = computed(
		useGetter('financing', 'receivablesByProductList')
	);
	const receivablesByProductListRequestStatus = computed(
		useGetter('financing', 'receivablesByProductListRequestStatus')
	);
	const receivablesByProductListPageable = computed(
		useGetter('financing', 'receivablesByProductListPageable')
	);
	const isLoading = useIsLoading([receivablesByProductListRequestStatus]);

	const onOpenModal = () => (isOpened.value = true);
	const onCloseModal = () => (isOpened.value = false);
	const fetchReceivablesByProductList = async (
		request: GetFinancingListRequest = receivablesByProductListRequest
	) => {
		await store.dispatch('financing/getReceivablesByProductList', request);
	};
	const sendSelection = (onClose: () => {}) => {
		selectedRequests.value = tableSelectedRows.value;

		trigger({
			event: 'select_content',
			payload: {
				event_category: 'title_emission',
				event_label: 'Tabela de recebíveis',
				description: 'Selecionou itens na tabela de recebíveis',
				selected_count: selectedRequests.value.length,
			},
		});

		onClose();
	};

	const {
		pagination,
		page,
		onSortTable,
		onChangePage,
		onChangePageLimit,
		onInputChangeMainFilter,
	} = usePageable(
		{
			sort,
			lowercaseSort: true,
			keyInputSearch: 'receivableNumber',
			filters: {
				status: (receivablesByProductListRequest.query as Record<string, string>).status,
			},
			calbackFn: async query => {
				await fetchReceivablesByProductList({
					...receivablesByProductListRequest,
					query,
				});

				if (query.receivableNumber) {
					trigger({
						event: 'search',
						payload: {
							event_category: 'title_emission',
							event_label: 'Seleção de recebíveis',
							description: 'Fez uma busca de recebíveis',
							search_term: query.receivableNumber,
							results_count: receivablesByProductListPageable.value.totalElements,
						},
					});
				}
			},
		},
		receivablesByProductListPageable
	);

	return {
		pagination,
		page,
		sort,
		onSortTable,
		onChangePage,
		onChangePageLimit,
		isOpened,
		isLoading,
		receivablesByProductList,
		receivablesByProductListRequest,
		receivablesByProductListPageable,
		onInputChangeMainFilter,
		onOpenModal,
		onCloseModal,
		sendSelection,
		fetchReceivablesByProductList,
	};
}
