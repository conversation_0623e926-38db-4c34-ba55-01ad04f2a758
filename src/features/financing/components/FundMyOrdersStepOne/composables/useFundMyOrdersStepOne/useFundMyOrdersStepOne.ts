import { ref, computed, watch } from 'vue';

import { RequestStatusEnum } from '@farm-investimentos/front-mfe-libs-ts';
import type { Ref, ComputedRef } from 'vue/types';

import {
	useGetter,
	useIsError,
	useIsLoading,
	useRouter,
	useSelectedProductId,
	useStore,
	useAnalytics,
} from '@/composible';
import { useReceivablesCounters } from '@/features/financing/composables';
import { DRAFT_STATUSES } from '@/features/financing/constants';
import {
	CreateDraftFinancingRequest,
	DeleteDraftFinancingRequest,
	EditDraftStepRequest,
} from '@/features/financing/services/types';
import type { DraftFinancing, Receivable } from '@/features/financing/types';

import { useFundMyOrdersStepOneEligibility } from '../useFundMyOrdersStepOneEligibility';

const tableSelectedRows: Ref<Receivable[]> = ref([]);
const selectedRequests: Ref<Receivable[]> = ref([]);
const approvedReceivables: Ref<Receivable[]> = ref([]);
const triggeredPreEligibility: Ref<boolean> = ref(false);

export function useFundMyOrdersStepOne() {
	const store = useStore();
	const router = useRouter();
	const productId = useSelectedProductId().value;
	const { trigger } = useAnalytics();
	const {
		eligibleReceivablesSummary,
		eligibilityRequestStatus: preEligibilityRequestStatus,
		hasAllReceivableApproved,
		isEligibilityModalOpen,
		onCloseEligibilityModal,
		handleEligibility,
	} = useFundMyOrdersStepOneEligibility(selectedRequests);

	const sort = ref({
		orderby: 'dueDate',
		order: 'ASC',
	});

	const draftFinancing: ComputedRef<DraftFinancing | null> = computed(
		useGetter('financing', 'draftFinancing')
	);
	const draftFinancingRequestStatus = computed(
		useGetter('financing', 'draftFinancingRequestStatus')
	);
	const financingPrice = computed(useGetter('financing', 'financingPrice'));
	const financingPriceRequestStatus = computed(
		useGetter('financing', 'financingPriceRequestStatus')
	);

	const financingPriceHasError = computed(
		() => financingPriceRequestStatus.value?.type === RequestStatusEnum.ERROR
	);
	const hasRequests = computed(() => Boolean(selectedRequests.value.length));
	const allRequestsAreFilledWithDate = computed(() => {
		return Object.values(selectedRequests.value).every(({ financingDate }) => {
			if (!financingDate || Array.isArray(financingDate)) {
				return false;
			}

			return new Date(financingDate) instanceof Date;
		});
	});
	const allRequestHaveValidDates = computed(() => {
		if (financingPrice.value) {
			return Object.values(financingPrice.value).every(({ erro }) => !erro);
		}

		return false;
	});
	const invalidSimulateFinancingPrice = computed(() => {
		if (financingPriceRequestStatus.value === RequestStatusEnum.START) return false;
		if (!financingPrice.value) return false;
		return Object.values(financingPrice.value)?.every(({ erro }) => !!erro);
	});
	const canAdvanceToNextStep = computed(
		() =>
			hasRequests.value &&
			allRequestsAreFilledWithDate.value &&
			allRequestHaveValidDates.value &&
			!financingPriceHasError.value
	);
	const continueButtonHasTooltip = computed(
		() =>
			hasExceededLimit.value ||
			financingPriceHasError.value ||
			invalidSimulateFinancingPrice.value
	);
	const continueButtonTooltipMessage = computed(() => {
		let message = '';

		if (hasExceededLimit.value) {
			message = 'O total a pagar é maior que o seu limite disponível';
		}

		if (financingPriceHasError.value) {
			message = 'A precificação dos pedidos não ocorreu corretamente. Tente novamente.';
		}

		if (invalidSimulateFinancingPrice.value) {
			message = 'A precificação dos pedidos não ocorreu corretamente. Tente novamente.';
		}

		return message;
	});
	const isLoading = useIsLoading([draftFinancingRequestStatus, preEligibilityRequestStatus]);
	const canPickReceivables = useIsError([draftFinancingRequestStatus]);
	const totalToPayForApprovedReceivables = computed(() => {
		return approvedReceivables.value.reduce((accumulator, { value, charges }) => {
			return (accumulator += value + charges);
		}, 0);
	});
	const isAllReceivablesApproved = computed(() => {
		if (triggeredPreEligibility.value) {
			return approvedReceivables.value.length === selectedRequests.value.length;
		}

		return true;
	});

	const { hasExceededLimit } = useReceivablesCounters(selectedRequests, canAdvanceToNextStep);

	const createOrRetrieveDraft = () => {
		const request: CreateDraftFinancingRequest = {
			params: {
				productId,
			},
		};

		store.dispatch('financing/createDraftFinancing', request);
	};
	const handleDeleteDraft = async () => {
		const request: DeleteDraftFinancingRequest = {
			params: {
				productId,
				draftId: draftFinancing.value.id,
			},
		};

		trigger({
			event: 'cancel_step_one',
			payload: {
				event_category: 'title_emission',
				event_label: 'Passo 1 do rascunho (seleção)',
				description: 'Cancelou o rascunho',
				selected_count: selectedRequests.value.length,
			},
		});

		await store.dispatch('financing/deleteDraftFinancing', request);
		router.push({ name: 'MyFinancingsHome' });
	};
	const handleNextDraftStep = async () => {
		trigger({
			event: 'eligibility',
			payload: {
				event_category: 'title_emission',
				event_label: 'Elegibilidade',
				description: `Avançou com ${approvedReceivables.value.length} recebívei(s) para o passo 2`,
			},
		});

		const request: EditDraftStepRequest = {
			params: {
				productId,
				draftId: draftFinancing.value.id,
				step: DRAFT_STATUSES.REVIEW,
			},
			payload: approvedReceivables.value,
		};

		triggeredPreEligibility.value = false;

		store.dispatch('financing/editDraftStep', request);
	};
	const resetEligibles = () => {
		approvedReceivables.value = [];
	};
	const resetStepOne = () => {
		resetEligibles();
		triggeredPreEligibility.value = false;
	};
	const handlePreEligibility = async () => {
		resetEligibles();

		const isSuccessEligibility = await handleEligibility();

		if (!isSuccessEligibility) {
			return;
		}

		const eligibles = eligibleReceivablesSummary.value.eligibles ?? [];

		const ids = eligibles.map(({ numberOrder }) => numberOrder);

		approvedReceivables.value = selectedRequests.value.filter(receivable =>
			ids.includes(receivable.receivableNumber)
		);

		triggeredPreEligibility.value = true;

		if (hasAllReceivableApproved.value && approvedReceivables.value.length) {
			await handleNextDraftStep();
		}
	};

	watch(draftFinancing, newValue => {
		if (!newValue) {
			return;
		}

		selectedRequests.value = newValue.receivables || [];
	});

	return {
		continueButtonHasTooltip,
		continueButtonTooltipMessage,
		triggeredPreEligibility,
		approvedReceivables,
		isAllReceivablesApproved,
		totalToPayForApprovedReceivables,
		isEligibilityModalOpen,
		canPickReceivables,
		allRequestsAreFilledWithDate,
		isLoading,
		sort,
		tableSelectedRows,
		selectedRequests,
		canAdvanceToNextStep,
		hasExceededLimit,
		hasRequests,
		financingPrice,
		financingPriceRequestStatus,
		financingPriceHasError,
		draftFinancing,
		onCloseEligibilityModal,
		handlePreEligibility,
		createOrRetrieveDraft,
		handleDeleteDraft,
		handleNextDraftStep,
		resetStepOne,
	};
}
