import { ref, computed, watch } from 'vue';

import type { Ref } from 'vue/types';

import { useAnalytics } from '@/composible';
import type { Receivable } from '@/features/financing/types';

import { useEligibility } from './../../../../composables';

export function useFundMyOrdersStepOneEligibility(selectedRequests: Ref<Receivable[]> = ref([])) {
	const { trigger } = useAnalytics();
	const {
		eligibleReceivablesSummary,
		eligibility: preEligibility,
		eligibilityRequestStatus,
		hasNoReceivableApproved,
		hasAllReceivableApproved,
		hasSomeReceivableApproved,
		handleEligibility,
	} = useEligibility(selectedRequests);

	const isEligibilityModalOpen = ref(false);

	const hasEligibilityValidation = computed(
		() => preEligibility.value?.quantityApproved < preEligibility.value?.total
	);

	const onCloseEligibilityModal = () => {
		trigger({
			event: 'eligibility_back',
			payload: {
				event_category: 'title_emission',
				event_label: 'Elegibilidade',
				description: 'Fechou o modal clicando no botão',
			},
		});

		isEligibilityModalOpen.value = false;
	};

	watch(preEligibility, () => {
		isEligibilityModalOpen.value =
			hasSomeReceivableApproved.value || hasNoReceivableApproved.value;
	});

	return {
		eligibleReceivablesSummary,
		preEligibility,
		eligibilityRequestStatus,
		isEligibilityModalOpen,
		hasEligibilityValidation,
		hasNoReceivableApproved,
		hasSomeReceivableApproved,
		hasAllReceivableApproved,
		handleEligibility,
		onCloseEligibilityModal,
	};
}
