.request-card__form {
	pointer-events: none;

	:deep([id^='farm-textfield-request-date-id']) {
		.farm-textfield--input {
			background-color: var(--farm-bw-black-5);

			input {
				color: var(--farm-gray-base);
			}

			i {
				color: var(--farm-stroke-disabled);
			}
		}
	}
}

.contact-message-card {
	&__info {
		padding: 0 16px;
		margin: 0;
		text-decoration: none;

		a {
			font-size: 14px;
			color: var(--farm-primary-base);
			text-decoration: none;
		}

		&:first-of-type {
			padding-left: 0;
		}

		&:last-of-type {
			padding-right: 0;
		}

		&:not(:last-of-type) {
			border-right: 1px solid var(--farm-stroke-base);
		}
	}
}
