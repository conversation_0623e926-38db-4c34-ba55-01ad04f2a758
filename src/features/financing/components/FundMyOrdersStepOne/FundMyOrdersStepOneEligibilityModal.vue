<template>
	<farm-modal :value="value" :offset-bottom="69" :offset-top="48" :size="modalSize" persistent>
		<template #header>
			<farm-dialog-header title="Análise dos pedidos selecionados" :has-close-icon="false" />
		</template>

		<template #content>
			<farm-row class="mb-4" tag="section">
				<farm-col cols="12" class="mb-4">
					<farm-bodytext type="2" variation="regular">
						Os seus pedidos passaram previamente por uma análise e identificamos que
						<strong>
							{{ eligibleReceivablesSummary.quantityApproved }} de
							{{ eligibleReceivablesSummary.total }}
						</strong>
						foram aprovados para serem utilizados{{
							eligibleReceivablesSummary.quantityApproved > 0
								? ' e caso queira, é possível continuar somente com os pedidos que estão elegíveis para continuar:'
								: '.'
						}}
					</farm-bodytext>
				</farm-col>

				<farm-col cols="12" v-if="hasSomeReceivableApproved">
					<ContactCard />
				</farm-col>

				<farm-col v-if="eligibleReceivablesSummary.quantityApproved === 0" cols="12">
					<farm-bodytext type="2" variation="regular" class="mt-4">
						Em caso de dúvidas ou problemas, entre em contato através do chat ou canais
						de atendimento:
					</farm-bodytext>

					<farm-card class="mb-2 mt-4">
						<farm-card-content class="d-flex">
							<a
								class="d-flex text-decoration-none"
								href="mailto:<EMAIL>"
								@click="analysisTracking('email')"
							>
								<farm-icon
									size="13"
									color="secondary-green"
									variation="darken"
									class="mr-1"
								>
									email-outline
								</farm-icon>
								<farm-subtitle type="2" color="primary"
									><EMAIL></farm-subtitle
								>
							</a>
						</farm-card-content>
					</farm-card>

					<farm-card>
						<farm-card-content>
							<a
								class="d-flex text-decoration-none"
								href="tel:+551135067800"
								@click="analysisTracking('whatsapp')"
							>
								<farm-icon
									size="13"
									color="secondary-green"
									variation="darken"
									class="mr-1"
								>
									whatsapp
								</farm-icon>

								<farm-subtitle type="2" color="primary"
									>(11) 3506-7800</farm-subtitle
								>
							</a>
						</farm-card-content>
					</farm-card>
				</farm-col>
			</farm-row>

			<template v-if="hasSomeReceivableApproved">
				<farm-line class="mb-4" />

				<farm-row class="mb-4">
					<farm-col cols="12" md="9">
						<farm-caption variation="semiBold">
							Pedido(s) aprovado(s): {{ eligibleReceivablesSummary.quantityApproved }}
						</farm-caption>
					</farm-col>
					<farm-col cols="12" md="3">
						<farm-caption class="text-md-right">
							Total a Pagar: {{ brl(totalToPayForApprovedReceivables) }}
						</farm-caption>
					</farm-col>
				</farm-row>

				<farm-row class="mb-4">
					<farm-col
						v-for="(eligibleReceivable, index) in approvedReceivables"
						:key="eligibleReceivable.receivableNumber"
						cols="12"
						:class="{ 'mb-4': index < approvedReceivables.length - 1 }"
					>
						<farm-card class="request-card">
							<farm-card-content class="d-flex flex-row justify-space-between">
								<div class="d-flex flex-row">
									<farm-idcaption class="request-card__item" copy-text="">
										<template #title>Número do Pedido</template>
										<template #subtitle>
											<span class="font-weight-medium">
												{{ eligibleReceivable.receivableNumber }}
											</span>
										</template>
									</farm-idcaption>

									<farm-idcaption class="request-card__item" copy-text="">
										<template #title>Vencimento</template>
										<template #subtitle>
											<span class="font-weight-medium">
												{{ defaultDateFormat(eligibleReceivable.dueDate) }}
											</span>
										</template>
									</farm-idcaption>

									<farm-idcaption class="request-card__item" copy-text="">
										<template #title>Valor</template>
										<template #subtitle>
											<span class="font-weight-medium">
												{{ brl(eligibleReceivable.value) }}
											</span>
										</template>
									</farm-idcaption>

									<farm-idcaption class="request-card__item" copy-text="">
										<template #title>Taxa</template>
										<template #subtitle>
											<span class="font-weight-medium">
												{{ eligibleReceivable.tax }}% a.m.
											</span>
										</template>
									</farm-idcaption>

									<farm-idcaption class="request-card__item" copy-text="">
										<template #title>Encargos</template>
										<template #subtitle>
											<span class="font-weight-medium">
												{{ brl(eligibleReceivable.charges) }}
											</span>
										</template>
									</farm-idcaption>

									<farm-idcaption class="request-card__item" copy-text="">
										<template #title>Total</template>
										<template #subtitle>
											<span class="font-weight-medium">
												{{
													brl(
														eligibleReceivable.value +
															(eligibleReceivable.charges || 0)
													)
												}}
											</span>
										</template>
									</farm-idcaption>
								</div>

								<div class="d-flex flex-column items-center request-card__form">
									<label
										class="d-flex flex-row"
										:for="`request-date-id-${eligibleReceivable.receivableNumber}`"
									>
										<farm-caption
											color="neutral-darken"
											weight="600"
											class="mr-2 mb-2"
										>
											Vencimento do título
											<farm-caption tag="span" color="error">*</farm-caption>
										</farm-caption>
										<farm-tooltip>
											Seus pedidos serão agrupados por data de vencimento.
											<template #activator>
												<farm-icon size="16" color="gray">
													help-circle
												</farm-icon>
											</template>
										</farm-tooltip>
									</label>

									<farm-input-datepicker
										:inputId="`request-date-id-${eligibleReceivable.receivableNumber}`"
										readonly
										:value="eligibleReceivable.financingDate.toString()"
									/>
								</div>
							</farm-card-content>
						</farm-card>
					</farm-col>
				</farm-row>
			</template>
		</template>

		<template #footer>
			<farm-line no-spacing />

			<footer class="d-flex justify-end align-center pa-4">
				<template v-if="hasNoReceivableApproved">
					<farm-btn @click="onClose">Alterar pedidos</farm-btn>
				</template>
				<template v-if="hasSomeReceivableApproved">
					<farm-btn plain @click="onClose">Alterar</farm-btn>
					<farm-btn @click="handleNextDraftStep">Continuar</farm-btn>
				</template>
			</footer>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import ContactCard from '@/components/ContactCard';

import { useFundMyOrdersStepOne } from './composables/useFundMyOrdersStepOne';
import { useFundMyOrdersStepOneEligibility } from './composables/useFundMyOrdersStepOneEligibility';

export default defineComponent({
	components: {
		ContactCard,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		onClose: {
			type: Function,
			required: true,
		},
	},
	setup() {
		const { eligibleReceivablesSummary, hasNoReceivableApproved, hasSomeReceivableApproved } =
			useFundMyOrdersStepOneEligibility();

		const { approvedReceivables, totalToPayForApprovedReceivables, handleNextDraftStep } =
			useFundMyOrdersStepOne();

		const modalSize = computed(() => (hasNoReceivableApproved.value ? 'xs' : 'default'));

		return {
			approvedReceivables,
			modalSize,
			eligibleReceivablesSummary,
			hasNoReceivableApproved,
			hasSomeReceivableApproved,
			totalToPayForApprovedReceivables,
			brl,
			defaultDateFormat,
			handleNextDraftStep,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './styles/FundMyOrdersStepOneRequestCard.scss';
@import './styles/FundMyOrdersStepOneEligibilityModal.scss';
</style>
