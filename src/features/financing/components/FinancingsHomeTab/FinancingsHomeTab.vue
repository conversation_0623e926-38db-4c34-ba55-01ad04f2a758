<template>
	<farm-container>
		<farm-row extra-decrease>
			<farm-tabs
				class="mt-n6"
				:tabs="tabs"
				:show-counter="false"
				:initial-select="actualRouteIndex"
				@update="handleTabChange"
			/>
		</farm-row>

		<slot />
	</farm-container>
</template>
<script lang="ts">
import { computed, defineComponent } from 'vue';

import { useRouter, useRoute } from '@/composible';

import { DEFAULT_TABS, MY_CLIENTS_TABS } from './constants';

export default defineComponent({
	setup() {
		const router = useRouter();
		const route = useRoute();

		const isExternalView = computed(() => !!route.meta.externalView);
		const tabs = computed(() => (isExternalView.value ? MY_CLIENTS_TABS : DEFAULT_TABS));
		const actualRouteIndex = computed(() =>
			tabs.value.findIndex(({ pathName }) => route.name === pathName)
		);

		const handleTabChange = ({ pathName }) => {
			router.replace({ name: pathName });
		};

		return {
			tabs,
			actualRouteIndex,
			handleTabChange,
		};
	},
});
</script>
