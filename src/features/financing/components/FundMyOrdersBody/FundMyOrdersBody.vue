<template>
	<farm-container>
		<template v-if="!isLoading">
			<FundMyOrdersStepOne v-if="step === 1" />
			<FundMyOrdersStepTwo v-else-if="step === 2" />

			<OnboardingModal
				v-if="!onboardingIsFinished"
				:value="true"
				:title="modalOptions.TITLE"
				:button-text="modalOptions.BUTTON_TEXT"
				:offset-bottom="modalOptions.MARGIN ? modalOptions.MARGIN.bottom : undefined"
				:offset-top="modalOptions.MARGIN ? modalOptions.MARGIN.top : undefined"
				@on-close="modalOptions.CALLBACK(onboardingStatus)"
			>
				<template v-if="onboardingIsIncompleted">
					<ContentIncompleted />
				</template>

				<template v-if="onboardingIsInAnalysis">
					<ContentAnalysis />
				</template>

				<template v-if="onboardingIsInvalid">
					<ContentInvalid />
				</template>
			</OnboardingModal>
		</template>

		<farm-loader v-else mode="overlay" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, defineAsyncComponent, onBeforeMount, computed } from 'vue';
const FundMyOrdersStepOne = defineAsyncComponent(
	() => import('../FundMyOrdersStepOne/FundMyOrdersStepOne')
);
const FundMyOrdersStepTwo = defineAsyncComponent(
	() => import('../FundMyOrdersStepTwo/FundMyOrdersStepTwo')
);
const OnboardingModal = defineAsyncComponent(() => import('../OnboardingModal'));
const ContentIncompleted = defineAsyncComponent(
	() => import('../OnboardingModal/Content/Incompleted.vue')
);
const ContentAnalysis = defineAsyncComponent(
	() => import('../OnboardingModal/Content/Analysis.vue')
);
const ContentInvalid = defineAsyncComponent(() => import('../OnboardingModal/Content/Invalid.vue'));

import type { ComputedRef } from 'vue/types';

import {
	useGetter,
	useIsLoading,
	useSelectedProductId,
	useStore,
	useAnalytics,
} from '@/composible';
import type { ValueOf } from '@/types';

import { DRAFT_STATUSES, ONBOARDING_STATUSES } from '../../constants';
import { CheckOnboardingStatusRequest, CreateDraftFinancingRequest } from '../../services/types';
import type { DraftFinancing, OnboardingStatus } from '../../types';

import { ONBOARDING_DIALOG } from './constants';

export default defineComponent({
	components: {
		FundMyOrdersStepOne,
		FundMyOrdersStepTwo,
		OnboardingModal,
		ContentIncompleted,
		ContentAnalysis,
		ContentInvalid,
	},
	setup() {
		const store = useStore();
		const productId = useSelectedProductId().value;
		const { trigger } = useAnalytics();

		const DEFAULT_STEP = DRAFT_STATUSES.SELECT;
		const createDraftRequest: CreateDraftFinancingRequest = {
			params: {
				productId,
			},
		};
		const requestOnboarding: CheckOnboardingStatusRequest = {
			params: {
				productId,
			},
		};

		const draftFinancing: ComputedRef<DraftFinancing | null> = computed(
			useGetter('financing', 'draftFinancing')
		);
		const onboardingStatus: ComputedRef<OnboardingStatus | null> = computed(
			useGetter('financing', 'onboardingStatus')
		);
		const step: ComputedRef<DraftFinancing['step'] | typeof DEFAULT_STEP> = computed(
			() => draftFinancing.value?.step ?? DEFAULT_STEP
		);
		const editDraftStepRequestStatus = computed(
			useGetter('financing', 'editDraftStepRequestStatus')
		);
		const draftFinancingRequestStatus = computed(
			useGetter('financing', 'draftFinancingRequestStatus')
		);
		const onboardingStatusRequestStatus = computed(
			useGetter('financing', 'onboardingStatusRequestStatus')
		);
		const onboardingIsFinished = computed(
			() => onboardingStatus.value?.id === ONBOARDING_STATUSES.COMPLETED
		);
		const onboardingIsIncompleted = computed(() =>
			(
				[
					ONBOARDING_STATUSES.INVITE,
					ONBOARDING_STATUSES.INVITED,
					ONBOARDING_STATUSES.INCOMPLETE,
					ONBOARDING_STATUSES.FAILED,
				] as ReadonlyArray<ValueOf<typeof ONBOARDING_STATUSES>>
			).includes(onboardingStatus.value.id)
		);
		const onboardingIsInAnalysis = computed(() =>
			(
				[
					ONBOARDING_STATUSES.IN_ANALYSIS,
					ONBOARDING_STATUSES.IN_PROGRESS,
					ONBOARDING_STATUSES.IN_WAITING,
				] as ReadonlyArray<ValueOf<typeof ONBOARDING_STATUSES>>
			).includes(onboardingStatus.value.id)
		);
		const onboardingIsInvalid = computed(
			() =>
				onboardingStatusRequestStatus.value?.type === 'ERROR' &&
				onboardingStatusRequestStatus.value?.httpStatus === 400
		);
		const modalOptions = computed(() => {
			if (onboardingIsIncompleted.value) {
				return ONBOARDING_DIALOG.INCOMPLETED;
			}
			if (onboardingIsInAnalysis.value) {
				return ONBOARDING_DIALOG.ANALYSIS;
			}

			return ONBOARDING_DIALOG.INVALID;
		});

		const isLoading = useIsLoading([
			onboardingStatusRequestStatus,
			draftFinancingRequestStatus,
			editDraftStepRequestStatus,
		]);

		onBeforeMount(async () => {
			await store.dispatch('financing/checkOnboardingStatus', requestOnboarding);

			const analytics = {
				event_category: 'title_emission',
				event_label: 'Checagem de onboarding',
				description: 'Usuário não tem onboarding concluído',
				onboarding_status: onboardingStatus.value.id,
			};

			if (onboardingIsInAnalysis.value) {
				analytics.description = 'Usuário está em análise';
			}

			if (onboardingIsInvalid.value) {
				analytics.description = 'Usuário não possui onboarding e/ou está inválido';
			}

			if (onboardingIsFinished.value) {
				store.dispatch('financing/createDraftFinancing', createDraftRequest);

				analytics.description = 'Usuário tem onboarding concluído';
			}

			trigger({
				event: 'onboarding_check',
				payload: analytics,
			});
		});

		return {
			step,
			isLoading,
			modalOptions,
			onboardingStatus,
			onboardingIsIncompleted,
			onboardingIsInAnalysis,
			onboardingIsFinished,
			onboardingIsInvalid,
		};
	},
});
</script>
<style scoped lang="scss">
@import './FundMyOrderBody.scss';
</style>
