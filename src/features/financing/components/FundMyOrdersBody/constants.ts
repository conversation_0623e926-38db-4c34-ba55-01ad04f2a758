import { useRouter, useAnalytics } from '@/composible';

export const ONBOARDING_DIALOG = {
	INCOMPLETED: {
		TITLE: 'Identificamos que estão faltando algumas informações no seu cadastro.',
		MARGIN: {
			top: 66,
			bottom: 84,
		},
		BUTTON_TEXT: 'Completar Cadastro',
		CALLBACK(onboardingStatus) {
			const router = useRouter();
			const { trigger } = useAnalytics();

			trigger({
				event: 'onboarding_back',
				payload: {
					event_category: 'title_emission',
					event_label: 'Checagem de onboarding',
					description: 'Clicou no botão "Completar Cadastro"',
					people_type: onboardingStatus.tpPeople,
					token: onboardingStatus.token,
				},
			});

			const baseLink = '/onboarding';
			const route = router.resolve(
				`${baseLink}/${onboardingStatus.tpPeople}/inicial?token=${onboardingStatus.token}`
			);

			window.open(route.href, '_blank');

			router.push({ name: 'MyFinancingsHome' });
		},
	},
	INVALID: {
		TITLE: 'Contato',
		MARGIN: {
			top: 48,
			bottom: 86,
		},
		CALLBACK() {
			const router = useRouter();
			const { trigger } = useAnalytics();

			trigger({
				event: 'onboarding_back',
				payload: {
					event_category: 'title_emission',
					event_label: 'Checagem de onboarding',
					description: 'Clicou no botão "Voltar"',
				},
			});

			router.push({ name: 'MyFinancingsHome' });
		},
	},
	ANALYSIS: {
		TITLE: 'Suas informações estão em validação',
		MARGIN: {
			top: 48,
			bottom: 84,
		},
		CALLBACK() {
			const router = useRouter();
			const { trigger } = useAnalytics();

			trigger({
				event: 'onboarding_back',
				payload: {
					event_category: 'title_emission',
					event_label: 'Checagem de onboarding',
					description: 'Clicou no botão "Voltar"',
				},
			});

			router.push({ name: 'MyFinancingsHome' });
		},
	},
};
