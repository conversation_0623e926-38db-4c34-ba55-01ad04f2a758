<template>
	<farm-col
		cols="12"
		md="4"
		class="requests-information d-flex flex-column justify-end flex-md-row align-md-center"
	>
		<div class="requests-information__item pr-6 mr-6 text-right">
			<farm-caption color="black">Pedidos</farm-caption>
			<farm-bodytext>{{ requests.length }}</farm-bodytext>
		</div>
		<div class="requests-information__item pr-6 mr-6 text-right">
			<farm-caption color="black">Total Selecionado</farm-caption>
			<farm-bodytext>{{ brl(totalRequestsValue) }} </farm-bodytext>
		</div>
		<div class="requests-information__item pr-6 mr-6 text-right">
			<farm-caption color="black">Total Encargos</farm-caption>
			<farm-bodytext>{{ brl(charges) }}</farm-bodytext>
		</div>
		<div class="requests-information__item text-right">
			<farm-tooltip
				@mouseover.native="trackTooltipHover"
				@mouseout.native="trackTooltipHover"
			>
				Este valor é calculado com base no valor do pedido e no vencimento escolhido.
				<template #activator>
					<farm-caption color="black">
						Total a Pagar
						<farm-icon size="16" color="primary" class="ml-1">
							help-circle-outline
						</farm-icon>
					</farm-caption>
					<farm-bodytext>{{ brl(totalToPayInRequests) }}</farm-bodytext>
				</template>
			</farm-tooltip>
		</div>
	</farm-col>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';
import type { PropType } from 'vue/types';

import { useAnalytics } from '@/composible';
import { debounce } from '@/helpers/debounce';

import type { Receivable } from '../../types';

export default defineComponent({
	props: {
		requests: {
			type: Array as PropType<Receivable[]>,
			default: () => [],
		},
		totalRequestsValue: {
			type: Number,
			default: 0,
		},
		charges: {
			type: Number,
			default: 0,
		},
		totalToPayInRequests: {
			type: Number,
			default: 0,
		},
	},
	setup() {
		const { trigger } = useAnalytics();

		const { cancel, debouncedFunction } = debounce(
			() =>
				trigger({
					event: 'see_tooltip',
					action: 'mouseover',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tooltip do sumário dos pedidos',
						description: 'Abertura de tooltip',
					},
				}),
			750
		);

		const trackTooltipHover = (event: MouseEvent) => {
			if (event.type === 'mouseout') {
				cancel();
				return;
			}

			debouncedFunction();
		};

		return {
			trackTooltipHover,
			brl,
		};
	},
});
</script>

<style lang="scss">
@import './FinancingRequestSummary.scss';
</style>
