<template>
	<div>
		<farm-caption class="mb-4" variation="regular">
			Para proceder com sua solicitação, entre em contato com nosso time através dos nossos
			canais de atendimento:
		</farm-caption>

		<farm-card class="mb-2">
			<farm-card-content class="d-flex">
				<a
					class="d-flex"
					href="mailto:<EMAIL>"
					@click="analysisTracking('email')"
				>
					<farm-icon size="13" color="secondary-green" variation="darken" class="mr-1">
						email-outline
					</farm-icon>
					<farm-subtitle type="2" color="primary"><EMAIL></farm-subtitle>
				</a>
			</farm-card-content>
		</farm-card>

		<farm-card>
			<farm-card-content>
				<a class="d-flex" href="tel:+551135067800" @click="analysisTracking('whatsapp')">
					<farm-icon size="13" color="secondary-green" variation="darken" class="mr-1">
						whatsapp
					</farm-icon>

					<farm-subtitle type="2" color="primary">(11) 3506-7800</farm-subtitle>
				</a>
			</farm-card-content>
		</farm-card>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { useAnalytics } from '@/composible';

export default defineComponent({
	setup() {
		const { trigger } = useAnalytics();

		const analysisTracking = (type: string) => {
			trigger({
				event: 'contact_click',
				payload: {
					contact_type: type,
				},
			});
		};

		return {
			analysisTracking,
		};
	},
});
</script>
