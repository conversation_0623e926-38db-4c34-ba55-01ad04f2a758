<template>
	<farm-modal
		:value="value"
		:offset-bottom="offsetBottom"
		:offset-top="offsetTop"
		size="xs"
		persistent
		class="onboarding-modal"
	>
		<template v-slot:header>
			<farm-dialog-header :title="title" :has-close-icon="false" />
		</template>

		<template v-slot:content>
			<slot />
		</template>

		<template v-slot:footer>
			<farm-line />

			<div class="d-flex justify-end align-center pa-4">
				<farm-btn @click="$emit('on-close')">{{ buttonText }}</farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		title: {
			type: String,
			default: '',
		},
		buttonText: {
			type: String,
			default: 'Voltar',
		},
		offsetTop: {
			type: Number,
			default: 48,
		},
		offsetBottom: {
			type: Number,
			default: 68,
		},
	},
	setup() {},
});
</script>

<style scoped lang="scss">
@import './OnboardingModal.scss';
</style>
