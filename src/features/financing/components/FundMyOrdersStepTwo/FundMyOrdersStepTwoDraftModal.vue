<template>
	<farm-modal :value="value" :offset-bottom="68" :offset-top="48" size="sm" @input="onClose">
		<template v-slot:header>
			<farm-dialog-header :has-close-icon="false" title="Leia com atenção!" />
		</template>

		<template v-slot:content>
			<farm-row class="mb-4">
				<farm-col cols="12">
					<farm-bodytext type="2">
						Certifique-se que os dados da sua operação estão corretos antes de concluir
						a solicitação.
					</farm-bodytext>
				</farm-col>
			</farm-row>

			<div class="d-flex">
				<farm-checkbox v-model="acceptedTerms" :value="true" size="md" class="mr-2" />

				<farm-caption variation="regular">
					Estou ciente de que a conclusão desta operação estará condicionada à verificação
					de disponibilidade de limite de crédito e de recursos, assim como a verificação
					de critérios definidos na operação e variações de taxas de desconto/mercado
					aplicadas à mesma.
				</farm-caption>
			</div>
		</template>

		<template v-slot:footer>
			<div class="d-flex justify-end align-center pa-4">
				<farm-btn plain @click="onClose">Voltar</farm-btn>
				<farm-btn :disabled="!acceptedTerms" @click="handleFinishDraft">Concluir</farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, ref, computed, watch } from 'vue';

import type { ComputedRef } from 'vue/types';

import { useGetter, useRouter, useSelectedProductId, useStore, useAnalytics } from '@/composible';

import { DRAFT_STATUSES } from '../../constants';
import { EditDraftStepRequest } from '../../services/types';
import type { DraftFinancing } from '../../types';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup(props, { emit }) {
		const store = useStore();
		const router = useRouter();
		const productId = useSelectedProductId().value;
		const { trigger } = useAnalytics();

		let isModalOpenTimer = null;
		const acceptedTerms = ref(false);

		const draftFinancing: ComputedRef<DraftFinancing | null> = computed(
			useGetter('financing', 'draftFinancing')
		);

		const calculateTimestampDifference = (
			timestamp1: number,
			timestamp2: number = Date.now()
		) => {
			const total = Math.floor((timestamp2 - timestamp1) / 1000);
			const minutes = total / 60;

			const difference = {
				hours: Math.floor(minutes / 60)
					.toString()
					.padStart(2, '0'),
				minutes: Math.floor(minutes).toString().padStart(2, '0'),
				seconds: Math.floor(total % 60)
					.toString()
					.padStart(2, '0'),
			};

			return difference;
		};
		const handleFinishDraft = async () => {
			const draftRequest: EditDraftStepRequest = {
				params: {
					productId,
					draftId: draftFinancing.value.id,
					step: DRAFT_STATUSES.FINISH,
				},
			};

			store.commit('financing/setHasFinishedDraftFinancing', true);
			await store.dispatch('financing/editDraftStep', draftRequest);

			const difference = calculateTimestampDifference(isModalOpenTimer);

			trigger({
				event: 'finish_draft',
				payload: {
					event_category: 'title_emission',
					event_label: 'Botão Concluir do modal',
					description: 'Concluiu o rascunho',
					draft_id: draftFinancing.value.id,
					time_before_finish: `${difference.hours}:${difference.minutes}:${difference.seconds}`,
				},
			});

			router.push({ name: 'MyFinancingsHome' });
		};
		const onClose = () => {
			isModalOpenTimer = null;
			emit('on-close');
		};

		watch(acceptedTerms, newValue => {
			if (!newValue) {
				return;
			}

			trigger({
				event: 'agreed_terms',
				payload: {
					event_category: 'title_emission',
					event_label: 'Checkbox de aceite dos termos',
					description: 'Clicou no checkbox aceitando os termos da operação',
					draft_id: draftFinancing.value.id,
				},
			});
		});

		onBeforeMount(() => {
			isModalOpenTimer = Date.now();
		});

		return {
			acceptedTerms,
			onClose,
			handleFinishDraft,
		};
	},
});
</script>
