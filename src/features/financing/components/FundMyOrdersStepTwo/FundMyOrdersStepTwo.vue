<template>
	<farm-box>
		<farm-heading type="6" class="mb-6">
			V<PERSON><PERSON><PERSON> as informações antes de concluir
		</farm-heading>

		<FinancingLimitContainer class="mb-6" :receivables="draftFinancingReceivables" />

		<farm-alertbox color="info" icon="information-outline" class="mb-6">
			Seus pedidos foram <strong>agrupados</strong> pelo
			<strong>vencimento do pedido</strong> e <strong>vencimento do título</strong>.
			{{ draftFinancing.financings.length > 1 ? 'Cada' : 'O' }}
			grupo representará um título com um contrato para ser assinado.
		</farm-alertbox>

		<template v-if="draftFinancing">
			<FinancingBallastSummaryCard
				v-for="financing in draftFinancing.financings"
				:key="financing.id"
				:financing="financing"
				class="mb-6"
			/>
		</template>

		<div class="mx-n6">
			<farm-line />
		</div>

		<farm-row class="pt-6">
			<farm-col cols="12" class="d-flex">
				<farm-btn plain @click="handleBackDraftStep" class="pa-0">
					<farm-icon size="30">chevron-left</farm-icon>
					Voltar
				</farm-btn>

				<farm-btn outlined class="ml-auto" @click="handleCancelDraft"> Cancelar </farm-btn>
				<farm-btn class="ml-2" @click="onOpenModal"> Concluir </farm-btn>
			</farm-col>
		</farm-row>

		<FundMyOrdersStepTwoDraftModal
			v-if="isOpenModal"
			v-model="isOpenModal"
			@on-close="onCloseModal"
		/>

		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-box>
</template>
<script lang="ts">
import { defineComponent, getCurrentInstance, computed, watch } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';
import type { ComputedRef } from 'vue/types';

import {
	useGetter,
	useIsLoading,
	useRouter,
	useSelectedProductId,
	useStore,
	useModal,
	useAnalytics,
} from '@/composible';

import { DRAFT_STATUSES } from '../../constants';
import type { DraftFinancing } from '../../types';
import FinancingBallastSummaryCard from '../FinancingBallastSummary/FinancingBallastSummaryCard';
import FinancingLimitContainer from '../FinancingLimitContainer';
import FinancingRequestSummary from '../FinancingRequestSummary';

import FundMyOrdersStepTwoDraftModal from './FundMyOrdersStepTwoDraftModal.vue';

export default defineComponent({
	components: {
		FinancingRequestSummary,
		FundMyOrdersStepTwoDraftModal,
		FinancingBallastSummaryCard,
		FinancingLimitContainer,
	},
	setup() {
		const internalInstance: any = getCurrentInstance().proxy;
		const store = useStore();
		const router = useRouter();
		const productId = useSelectedProductId().value;
		const { trigger } = useAnalytics();

		const { isOpenModal, onCloseModal, onOpenModal } = useModal();

		const draftFinancing: ComputedRef<DraftFinancing | null> = computed(
			useGetter('financing', 'draftFinancing')
		);
		const preEligibilityRequestStatus = computed(
			useGetter('financing', 'preEligibilityRequestStatus')
		);
		const draftFinancingRequestStatus = computed(
			useGetter('financing', 'draftFinancingRequestStatus')
		);
		const draftFinancingReceivables = computed(() =>
			draftFinancing.value.financings.flatMap(financing => financing.receivables)
		);

		const handleBackDraftStep = () => {
			const request = {
				params: {
					productId,
					draftId: draftFinancing.value.id,
					step: DRAFT_STATUSES.SELECT,
				},
				payload: draftFinancing.value.receivables,
			};

			trigger({
				event: 'step_back',
				payload: {
					event_category: 'title_emission',
					event_label: 'Botão de voltar',
					description: 'Clicou no botão para voltar o rascunho',
					draft_id: draftFinancing.value.id,
				},
			});

			store.dispatch('financing/editDraftStep', request);
		};
		const handleCancelDraft = () => {
			const request = {
				params: {
					productId,
					draftId: draftFinancing.value.id,
				},
			};

			internalInstance.$dialog
				.confirm(
					{
						title: 'Tem certeza que deseja cancelar?',
						body: 'Ao cancelar a sua operação e seu agrupamento serão descartados, deseja continuar?',
					},
					{
						customClass: 'step-two-dialog--cancel',
						okText: 'Cancelar Operação',
						cancelText: 'Voltar',
					}
				)
				.then(async () => {
					trigger({
						event: 'cancel_draft',
						payload: {
							event_category: 'title_emission',
							event_label: 'Botão de cancelar',
							description: 'Clicou no botão para cancelar o rascunho',
							draft_id: draftFinancing.value.id,
						},
					});

					await store.dispatch('financing/deleteDraftFinancing', request);
					router.push({ name: 'MyFinancingsHome' });
				});
		};

		const isLoading = useIsLoading([preEligibilityRequestStatus, draftFinancingRequestStatus]);

		watch(isOpenModal, newValue => {
			if (!newValue) {
				trigger({
					event: 'close_finish_draft_modal',
					payload: {
						event_category: 'title_emission',
						event_label: 'Botão de concluir do rascunho',
						description: 'Fechou o modal de concluir o rascunho',
						draft_id: draftFinancing.value.id,
					},
				});

				return;
			}

			trigger({
				event: 'open_finish_draft_modal',
				payload: {
					event_category: 'title_emission',
					event_label: 'Botão de concluir do rascunho',
					description: 'Abriu o modal de concluir o rascunho',
					draft_id: draftFinancing.value.id,
				},
			});
		});

		return {
			isLoading,
			draftFinancing,
			isOpenModal,
			onCloseModal,
			onOpenModal,
			draftFinancingReceivables,
			defaultDateFormat,
			handleBackDraftStep,
			handleCancelDraft,
		};
	},
});
</script>

<style lang="scss">
@import './FundMyOrdersStepTwo.scss';
</style>
