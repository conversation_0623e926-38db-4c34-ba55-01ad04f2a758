import { ref, computed } from 'vue';

import type { ComputedRef } from 'vue/types';

import {
	useGetter,
	useIsLoading,
	usePageable,
	useRoute,
	useSelectedProductId,
	useStore,
} from '@/composible';
import type {
	GetFinancingListRequest,
	GetFinancingListAllRequest,
	GetFinancingSummaryRequest,
	GetFinancingSummaryAllRequest,
} from '@/features/financing/services/types';
import type { Financing } from '@/features/financing/types';

export default function useMyFinancings(filters?: Record<string, string | number>) {
	const store = useStore();
	const route = useRoute();
	const productId = useSelectedProductId().value;

	const financingListRequest: GetFinancingListRequest | GetFinancingListAllRequest = {
		params: {
			productId,
		},
		query: {
			page: '0',
			limit: '10',
		},
	};

	const financingSummaryRequest: GetFinancingSummaryRequest | GetFinancingSummaryAllRequest = {
		params: {
			productId,
		},
	};

	const sortClicked = ref([]);
	const sort = ref({
		orderby: '',
		order: '',
	});

	const isExternalView = computed(() => Boolean(route.meta?.externalView));
	const financingList: ComputedRef<Financing[]> = computed(
		useGetter('financing', 'financingList')
	);
	const financingListRequestStatus = computed(
		useGetter('financing', 'financingListRequestStatus')
	);
	const financingListPageable = computed(useGetter('financing', 'financingListPageable'));
	const financingSummary = computed(useGetter('financing', 'financingSummary'));
	const financingSummaryRequestStatus = computed(
		useGetter('financing', 'financingSummaryRequestStatus')
	);

	const isLoading = useIsLoading([financingSummaryRequestStatus, financingListRequestStatus]);
	const {
		page,
		pagination,
		onChangePage,
		onChangePageLimit,
		onSortTable,
		onInputChangeMainFilter,
	} = usePageable(
		{
			sort,
			lowercaseSort: true,
			keyInputSearch: '',
			filters,
			calbackFn: filters => {
				const fetchFn = isExternalView.value ? fetchFinancingListAll : fetchFinancingList;

				fetchFn({
					...financingListRequest,
					query: new URLSearchParams(filters),
				});
			},
		},
		financingListPageable
	);

	const fetchFinancingList = (request = financingListRequest) => {
		store.dispatch('financing/getFinancingList', request);
	};
	const fetchFinancingListAll = (request = financingListRequest) => {
		store.dispatch('financing/getFinancingListAll', request);
	};
	const fetchFinancingSummary = () => {
		store.dispatch('financing/getFinancingSummary', financingSummaryRequest);
	};
	const fetchFinancingSummaryAll = () => {
		store.dispatch('financing/getFinancingSummaryAll', financingSummaryRequest);
	};

	return {
		financingList,
		financingSummary,
		sort,
		sortClicked,
		page,
		pagination,
		onChangePage,
		onChangePageLimit,
		onSortTable,
		isLoading,
		isExternalView,
		onInputChangeMainFilter,
		fetchFinancingList,
		fetchFinancingListAll,
		fetchFinancingSummary,
		fetchFinancingSummaryAll,
	};
}
