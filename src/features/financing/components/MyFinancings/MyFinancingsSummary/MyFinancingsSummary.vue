<template>
	<farm-card>
		<farm-card-content class="header-card-content d-flex flex-md-row md:flex-row pa-6">
			<farm-idcaption copyText="" class="me-auto">
				<template v-slot:title> Produto </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.productName : '-' }}
				</template>
			</farm-idcaption>

			<farm-idcaption
				class="header-card-content__item"
				icon="dots-horizontal-circle-outline"
				copyText=""
			>
				<template v-slot:title> Em Análise </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.inProgress : '-' }}
				</template>
			</farm-idcaption>
			<farm-idcaption
				class="header-card-content__item"
				icon="check-circle-outline"
				copyText=""
			>
				<template v-slot:title> Concluídos </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.completed : '-' }}
				</template>
			</farm-idcaption>
			<farm-idcaption
				class="header-card-content__item"
				icon="close-circle-outline"
				copyText=""
			>
				<template v-slot:title> Cancelados </template>
				<template v-slot:subtitle>
					{{ financingSummary ? financingSummary.canceled : '-' }}
				</template>
			</farm-idcaption>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import type { PropType } from 'vue/types';

import type { FinancingSummary } from '../../../types';

export default defineComponent({
	props: {
		financingSummary: {
			type: Object as PropType<FinancingSummary>,
			default: null,
		},
	},
	setup() {
		return {};
	},
});
</script>

<style lang="scss" scoped>
@import 'MyFinancingsSummary.scss';
</style>
