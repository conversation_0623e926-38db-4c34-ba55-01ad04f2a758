<template>
	<v-data-table
		id="my-financings-table"
		hide-default-header
		hide-default-footer
		item-key="id"
		:headers="tableHeaders"
		:items="items"
		:server-items-length="pagination ? pagination.totalElements : 0"
	>
		<template slot="no-data">
			<DataTableEmptyWrapper :bordered="false" :subtitle="emptyTableText" />
		</template>
		<template v-slot:header="{ props }">
			<farm-datatable-header
				:headers="props.headers"
				:headerProps="props"
				:sort-click="sortClicked"
				@onClickSort="onSortTable"
			/>
		</template>
		<template v-slot:[`item.updatedAt`]="{ item }">
			{{ defaultDateFormat(item.updatedAt) }}
		</template>
		<template v-slot:[`item.status`]="{ item }">
			<MyFinancingsTableChip :status="item.status" />
		</template>
		<template v-slot:[`item.dueDate`]="{ item }">
			{{ defaultDateFormat(item.dueDate) }}
		</template>
		<template v-slot:[`item.total`]="{ item }">
			{{ brl(item.total) }}
		</template>
		<template v-slot:[`item.totalPayment`]="{ item }">
			{{ brl(item.totalPayment) }}
		</template>
		<template v-slot:[`item.openInNewTab`]="{ item }">
			<farm-btn plain @click="navigateToTitle(item.id)">
				<farm-icon size="18">open-in-new</farm-icon>
			</farm-btn>
		</template>
		<template v-slot:footer>
			<farm-datatable-paginator
				v-if="items.length > 0"
				class="mt-6 mb-n6"
				:page="page"
				:totalPages="pagination.totalPages"
				:initialLimitPerPage="pagination.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>
	</v-data-table>
</template>

<script lang="ts">
import { defineComponent, computed, watch } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { useRouter, useAnalytics } from '@/composible';

import { myFinancingsHeaders, myClientHeaders } from '../../../configurations/headers';
import MyFinancingsTableChip from '../MyFinancingsTableChip';
import useMyFinancings from '../composables/useMyFinancings';

import type { Financing } from './../../../types';

export default defineComponent({
	components: {
		MyFinancingsTableChip,
	},
	props: {
		items: {
			type: Array,
			default: () => [],
		},
		filters: {
			type: Object,
			default: () => ({}),
		},
	},
	setup(props) {
		const router = useRouter();
		const {
			sort,
			sortClicked,
			page,
			pagination,
			isExternalView,
			onChangePage,
			onChangePageLimit,
			onSortTable,
		} = useMyFinancings(props.filters);
		const { trigger } = useAnalytics();

		const tableHeaders = computed(() =>
			isExternalView.value ? myClientHeaders.myFinancingsHeaders : myFinancingsHeaders
		);
		const emptyTableText = computed(() =>
			isExternalView.value
				? 'Não há títulos disponíveis até o momento'
				: 'Crie um novo título para começar'
		);

		const navigateToTitle = (id: Financing['id']) => {
			trigger({
				event: 'button_click',
				payload: {
					event_category: 'title_emission',
					event_label: 'Botão Abrir título',
					description: 'Clique no botão Abrir um título',
					items: [
						{
							item_id: id,
							item_name: `Título ${id}`,
						},
					],
				},
			});

			const routeName = isExternalView.value
				? 'MyClientsMyFinancingsDetail'
				: 'MyFinancingsDetail';

			router.push({ name: routeName, params: { id: id.toString() } });
		};

		watch(
			() => pagination.value?.pageSize,
			(newValue, oldValue) => {
				if (!newValue || !oldValue) {
					return;
				}
				if (oldValue !== newValue) {
					trigger({
						event: 'update_per_page',
						payload: {
							event_category: 'title_emission',
							event_label: 'Tabela de títulos',
							description: 'Alterou quantidade de itens por página na tabela',
							perPage: newValue,
						},
					});
				}
			}
		);
		watch(page, (newValue, oldValue) => {
			if (!newValue) {
				return;
			}
			if (oldValue !== newValue) {
				trigger({
					event: 'update_page_number',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tabela de títulos',
						description: `Paginou a tabela para ${newValue}`,
						page: newValue,
					},
				});
			}
		});
		watch(
			sort,
			newValue => {
				if (!newValue) {
					return;
				}
				trigger({
					event: 'update_sort',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tabela de títulos',
						description: `Ordenou a tabela por ${newValue.orderby} em ordenação ${
							newValue.order === 'ASC' ? 'ascendente' : 'descendente'
						}`,
						column: newValue.orderby,
						order: newValue.order,
					},
				});
			},
			{
				deep: true,
			}
		);

		return {
			page,
			pagination,
			sortClicked,
			tableHeaders,
			emptyTableText,
			onSortTable,
			onChangePage,
			onChangePageLimit,
			brl,
			defaultDateFormat,
			navigateToTitle,
		};
	},
});
</script>

<style lang="scss">
@import './MyFinancingsTable.scss';
</style>
