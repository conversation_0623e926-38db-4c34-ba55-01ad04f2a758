<template>
	<farm-chip :color="statusItem.color" dense class="my-financings-list-chip">
		<farm-icon v-if="statusItem.icon" size="16" color="">{{ statusItem.icon }}</farm-icon>
		{{ statusItem.text }}
	</farm-chip>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { STATUS_DICTIONARY } from './constants';

export default defineComponent({
	props: {
		status: {
			type: Number,
			default: 1,
		},
	},
	setup({ status }) {
		const statusItem = ref(STATUS_DICTIONARY[status]);

		return {
			statusItem,
		};
	},
});
</script>

<style lang="scss">
@import './MyFinancingsTableChip.scss';
</style>
