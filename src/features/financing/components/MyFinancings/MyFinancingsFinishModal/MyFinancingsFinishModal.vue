<template>
	<farm-modal
		:value="value"
		:offset-bottom="68"
		:offset-top="48"
		@input="$emit('on-close')"
		size="xs"
	>
		<template v-slot:header>
			<farm-dialog-header title="Fique atento aos próximos passos!" :has-close-icon="false" />
		</template>

		<template v-slot:content>
			<farm-caption class="mb-4">
				A solicitação de operação foi realizada.
				<farm-caption variation="semiBold" tag="span" color="primary">
					você receberá no seu e-mail
				</farm-caption>
				as instruções para fazer a
				<farm-caption variation="semiBold" tag="span" color="primary">
					assinatura do contrato
				</farm-caption>
				e dar continuidade à sua operação.
			</farm-caption>
		</template>

		<template v-slot:footer>
			<farm-line no-spacing />

			<div class="d-flex justify-end align-center pa-4">
				<farm-btn @click="$emit('on-close')"><PERSON><PERSON>r</farm-btn>
			</div>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup() {},
});
</script>
