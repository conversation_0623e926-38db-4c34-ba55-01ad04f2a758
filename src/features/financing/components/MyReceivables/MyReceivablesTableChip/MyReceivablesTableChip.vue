<template>
	<farm-chip :color="color" dense>
		{{ status }}
	</farm-chip>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
	props: {
		status: {
			type: String,
			required: true,
		},
	},
	setup({ status }) {
		const color = computed(() => {
			return status === 'Disponível' ? 'success' : 'info';
		});

		return {
			color,
		};
	},
});
</script>
