<template>
	<v-data-table
		id="my-receivables-table"
		hide-default-header
		hide-default-footer
		item-key="id"
		:headers="tableHeaders"
		:items="items"
		:server-items-length="pagination ? pagination.totalElements : 0"
	>
		<template slot="no-data">
			<DataTableEmptyWrapper
				:bordered="false"
				subtitle="Tente filtrar novamente sua pesquisa"
			/>
		</template>
		<template v-slot:header="{ props }">
			<farm-datatable-header
				first-selected
				:headers="props.headers"
				:header-props="props"
				:sort-click="sortClicked"
				:selected-index="1"
				@onClickSort="onSortTable"
			/>
		</template>
		<template v-slot:[`item.status`]="{ item }">
			<MyReceivablesTableChip :status="item.status" />
		</template>
		<template v-slot:[`item.dueDate`]="{ item }">
			{{ defaultDateFormat(item.dueDate) }}
		</template>
		<template v-slot:[`item.value`]="{ item }">
			{{ brl(item.value) }}
		</template>
		<template v-slot:footer>
			<farm-datatable-paginator
				v-if="items.length > 0"
				class="mt-6 mb-n6"
				:page="page"
				:totalPages="pagination.totalPages"
				:initialLimitPerPage="pagination.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>
	</v-data-table>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { useAnalytics } from '@/composible';

import { myClientHeaders, myReceivablesHeaders } from '../../../configurations/headers';
import MyReceivablesTableChip from '../MyReceivablesTableChip';
import useMyReceivables from '../composables/useMyReceivables';

export default defineComponent({
	components: {
		MyReceivablesTableChip,
	},
	props: {
		items: {
			type: Array,
			default: () => [],
		},
		filters: {
			type: Object,
			default: () => ({}),
		},
	},
	setup(props) {
		const {
			sort,
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortTable,
			isExternalView,
		} = useMyReceivables(props.filters);

		const { trigger } = useAnalytics();

		const sortClicked = ref([]);

		const tableHeaders = computed(() =>
			isExternalView.value ? myClientHeaders.myReceivablesHeaders : myReceivablesHeaders
		);

		watch(
			() => pagination.value?.pageSize,
			(newValue, oldValue) => {
				if (!newValue || !oldValue) {
					return;
				}
				if (oldValue !== newValue) {
					trigger({
						event: 'update_per_page',
						payload: {
							event_category: 'title_emission',
							event_label: 'Tabela de recebíveis',
							description: 'Alterou quantidade de itens por página na tabela',
							perPage: newValue,
						},
					});
				}
			}
		);
		watch(page, (newValue, oldValue) => {
			if (!newValue) {
				return;
			}
			if (oldValue !== newValue) {
				trigger({
					event: 'update_page_number',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tabela de recebíveis',
						description: `Paginou a tabela para ${newValue}`,
						page: newValue,
					},
				});
			}
		});
		watch(
			sort,
			newValue => {
				if (!newValue) {
					return;
				}
				trigger({
					event: 'update_sort',
					payload: {
						event_category: 'title_emission',
						event_label: 'Tabela de recebíveis',
						description: `Ordenou a tabela por ${newValue.orderby} em ordenação ${
							newValue.order === 'ASC' ? 'ascendente' : 'descendente'
						}`,
						column: newValue.orderby,
						order: newValue.order,
					},
				});
			},
			{
				deep: true,
			}
		);

		return {
			sort,
			page,
			pagination,
			sortClicked,
			tableHeaders,
			brl,
			defaultDateFormat,
			onSortTable,
			onChangePage,
			onChangePageLimit,
		};
	},
});
</script>

<style lang="scss">
@import './MyReceivablesTable.scss';
</style>
