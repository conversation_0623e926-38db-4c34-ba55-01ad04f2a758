import { ref, computed } from 'vue';

import type { ComputedRef } from 'vue/types';

import {
	useGetter,
	useIsLoading,
	useSelectedProductId,
	useStore,
	useAnalytics,
	usePageable,
	useRoute,
} from '@/composible';
import type {
	GetFinancingListAllRequest,
	GetFinancingListRequest,
	GetReceivablesSummaryAllRequest,
	GetReceivablesSummaryRequest,
} from '@/features/financing/services/types';
import type { Receivable } from '@/features/financing/types';

export default function useMyReceivables(filters?: Record<string, string>) {
	const store = useStore();
	const productId = useSelectedProductId().value;
	const { trigger } = useAnalytics();
	const route = useRoute();

	const sort = ref({
		orderby: '',
		order: '',
	});

	const receivablesListRequest: GetFinancingListRequest | GetFinancingListAllRequest = {
		params: {
			productId,
		},
		query: {
			page: '0',
			limit: '10',
		},
	};

	const receivablesSummaryRequest:
		| GetReceivablesSummaryRequest
		| GetReceivablesSummaryAllRequest = {
		params: {
			productId,
		},
	};

	const isExternalView = computed(() => route.meta.externalView);
	const receivablesList: ComputedRef<Receivable[]> = computed(
		useGetter('financing', 'receivablesList')
	);
	const receivablesListRequestStatus = computed(
		useGetter('financing', 'receivablesListRequestStatus')
	);
	const receivablesListPageable = computed(useGetter('financing', 'receivablesListPageable'));

	const receivablesSummary = computed(useGetter('financing', 'receivablesSummary'));
	const receivablesSummaryRequestStatus = computed(
		useGetter('financing', 'receivablesSummaryRequestStatus')
	);
	const isLoading = useIsLoading([receivablesSummaryRequestStatus, receivablesListRequestStatus]);

	const { page, pagination, onChangePage, onChangePageLimit, onSortTable } = usePageable(
		{
			sort,
			lowercaseSort: true,
			filters,
			keyInputSearch: '',
			calbackFn: async callbackFilters => {
				const fetchFn = isExternalView.value
					? fetchReceivablesListAll
					: fetchReceivablesList;

				await fetchFn({
					...receivablesListRequest,
					query: new URLSearchParams(callbackFilters),
				});

				if (callbackFilters?.receivableNumber) {
					trigger({
						event: 'search',
						payload: {
							event_category: 'title_emission',
							event_label: 'Tabela de recebíveis',
							description: 'Fez uma busca de recebíveis',
							results_count: receivablesListPageable.value.totalElements,
							search_term: callbackFilters.receivableNumber,
						},
					});
				}
			},
		},
		receivablesListPageable
	);

	const fetchReceivablesList = async (
		request: GetFinancingListRequest = receivablesListRequest
	) => {
		await store.dispatch('financing/getReceivablesList', request || receivablesListRequest);
	};
	const fetchReceivablesSummary = () => {
		store.dispatch('financing/getReceivablesSummary', receivablesSummaryRequest);
	};
	const fetchReceivablesListAll = async (
		request: GetFinancingListRequest = receivablesListRequest
	) => {
		await store.dispatch('financing/getReceivablesListAll', request || receivablesListRequest);
	};

	const fetchReceivablesSummaryAll = () => {
		store.dispatch('financing/getReceivablesSummaryAll', receivablesSummaryRequest);
	};

	return {
		sort,
		page,
		pagination,
		onChangePage,
		onChangePageLimit,
		onSortTable,
		receivablesList,
		receivablesListRequest,
		receivablesListPageable,
		receivablesSummary,
		isLoading,
		isExternalView,
		fetchReceivablesList,
		fetchReceivablesSummary,
		fetchReceivablesListAll,
		fetchReceivablesSummaryAll,
	};
}
