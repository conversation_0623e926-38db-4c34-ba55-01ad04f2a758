<template>
	<v-data-table
		id="table-receivables-list"
		ref="tableReceivablesList"
		hide-default-header
		hide-default-footer
		item-value="id"
		:headers="headers"
		:items="items"
	>
		<template slot="no-data">
			<farm-emptywrapper subtitle="Não foi possível recuperar os recebíveis desse título" />
		</template>
		<template v-slot:header="{ props }">
			<farm-datatable-header
				:headers="props.headers"
				:headerProps="props"
				:show-checkbox="false"
				:sort-click="sortClicked"
				@onClickSort="onSortTable"
			/>
		</template>
		<template v-slot:[`item.status`]>
			<farm-icon v-if="isApproved">check-circle-outline</farm-icon>
			<farm-icon v-else color="error">close-circle-outline</farm-icon>
		</template>
		<template v-slot:[`item.dueDate`]="{ item }">
			{{ defaultDateFormat(item.dueDate) }}
		</template>
		<template v-slot:[`item.value`]="{ item }">{{ brl(item.value) }} </template>
		<template v-slot:[`item.tax`]="{ item }">{{
			item.tax ? `${item.tax}% a.m.` : '-'
		}}</template>
		<template v-slot:[`item.charges`]="{ item }">{{ brl(item.charges || 0) }} </template>
		<template v-slot:[`item.total`]="{ item }">
			{{ brl(item.value + (item.charges || 0)) }}
		</template>
	</v-data-table>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { brl, defaultDateFormat, decimals } from '@farm-investimentos/front-mfe-libs-ts';
import type { PropType } from 'vue/types';

import type { Receivable } from '@/features/financing/types';

import { groupedReceivablesHeaders as headers } from '../../../configurations/headers';

export default defineComponent({
	props: {
		items: {
			type: Array as PropType<Receivable[]>,
			required: true,
		},
		isApproved: {
			type: Boolean,
			required: true,
		},
	},
	setup({ items }) {
		const sortClicked = ref([]);

		const onSortTable = () => {
			const { field, descending: order } = sortClicked.value.find(item => item.clicked);

			items.sort((a, b) => {
				let orderNumber = a[field] > b[field] ? 1 : -1;

				if (field === 'total') {
					const aTotal = a.value + (a.charges || 0);
					const bTotal = b.value + (b.charges || 0);

					orderNumber = aTotal >= bTotal ? 1 : -1;
				}

				if (order === 'ASC') {
					orderNumber *= -1;
				}

				return orderNumber;
			});
		};

		return {
			headers,
			sortClicked,
			brl,
			decimals,
			defaultDateFormat,
			onSortTable,
		};
	},
});
</script>
