<template>
	<farm-card>
		<farm-card-content class="pa-6">
			<farm-row justify="space-between" align="center" class="mb-4">
				<farm-col cols="12" md="6">
					<farm-heading type="6" class="mb-6">
						Vencimento do título:
						{{ defaultDateFormat(financing.dueDate) }}
					</farm-heading>
				</farm-col>

				<FinancingRequestSummary
					:requests="financing.receivables"
					:total-requests-value="financing.total"
					:charges="financing.charges"
					:total-to-pay-in-requests="financing.totalPayment"
				/>
			</farm-row>

			<farm-row>
				<farm-col cols="12">
					<farm-collapsible
						title="Pedidos"
						icon="clipboard-text-outline"
						@open="openCollapsible"
					>
						<FinancingBallastSummaryCardTable
							:is-approved="isApproved"
							:items="financing.receivables || []"
						/>
					</farm-collapsible>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';
import type { Ref, PropType } from 'vue/types';

import { useAnalytics } from '@/composible';
import { FINANCING_STATUSES } from '@/features/financing/constants';
import type { Financing } from '@/features/financing/types/Financing';

import FinancingRequestSummary from './../../FinancingRequestSummary';
import FinancingBallastSummaryCardTable from './../FinancingBallastSummaryCardTable';

export default defineComponent({
	components: {
		FinancingRequestSummary,
		FinancingBallastSummaryCardTable,
	},
	props: {
		financing: {
			type: Object as PropType<Financing>,
			required: true,
		},
	},
	setup(props) {
		const { trigger } = useAnalytics();
		const { financing }: { financing: Ref<Financing> } = toRefs(props);

		const isRejectedFinancing = computed(
			() => financing.value.status === FINANCING_STATUSES.FAILED
		);
		const isApproved = computed(() => !financing.value.id || !isRejectedFinancing.value);

		const openCollapsible = (value: boolean) => {
			if (!value) {
				return;
			}

			trigger({
				event: 'open_title_collapsible',
				payload: {
					event_category: 'title_emission',
					event_label: 'Collapsible do título',
					description: 'Abertura de collapsible',
					title_due_date: defaultDateFormat(financing.value.dueDate),
				},
			});
		};

		return {
			isApproved,
			openCollapsible,
			defaultDateFormat,
		};
	},
});
</script>
