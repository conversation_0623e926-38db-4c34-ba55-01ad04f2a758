.limit-visualizer-number {
	grid-area: 1 / 1;

	&__circle {
		&--static {
			stroke: var(--farm-neutral-base);
		}

		&--dynamic {
			stroke: var(--farm-primary-base);
			stroke-linecap: round;
			animation: progress 0.75s ease-out;
			transition: stroke-dasharray 0.75s ease-out 0s;
		}

		&--exceeded {
			stroke: var(--farm-warning-base);
		}
	}
}

@keyframes progress {
	0% {
		stroke-dasharray: 0 100;
	}
}
