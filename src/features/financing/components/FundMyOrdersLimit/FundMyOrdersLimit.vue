<template>
	<svg viewBox="0 0 36 36" class="limit-visualizer-number">
		<path
			class="limit-visualizer-number__circle limit-visualizer-number__circle--static"
			fill="none"
			stroke-width="2"
			stroke-dasharray="100 100"
			d="M18 2.0845
			a 15.9155 15.9155 0 0 1 0 31.831
			a 15.9155 15.9155 0 0 1 0 -31.831"
		/>
		<path
			class="limit-visualizer-number__circle limit-visualizer-number__circle--dynamic"
			:class="{ 'limit-visualizer-number__circle--exceeded': hasExceededLimit }"
			fill="none"
			:stroke-width="strokeWidth"
			:stroke-dasharray="`${percentage} 100`"
			d="M18 2.0845
			a 15.9155 15.9155 0 0 1 0 31.831
			a 15.9155 15.9155 0 0 1 0 -31.831"
		/>
	</svg>
</template>

<script lang="ts">
import { computed } from 'vue';
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		percentage: {
			type: Number,
			required: true,
		},
		hasExceededLimit: {
			type: <PERSON><PERSON><PERSON>,
			default: false,
		},
	},
	setup(props) {
		const strokeWidth = computed(() => (!props.percentage ? 0 : 2));
		return {
			strokeWidth,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './FundMyOrdersLimit.scss';
</style>
