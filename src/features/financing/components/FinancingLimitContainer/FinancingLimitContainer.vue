<template>
	<farm-row justify="space-between" align-content="center">
		<farm-col cols="12" md="3" class="mb-4 mb-md-0">
			<div class="limit-visualizer d-flex align-center">
				<div class="limit-visualizer__used">
					<FundMyOrdersLimit
						:percentage="percentage || 0"
						:has-exceeded-limit="hasExceededLimit"
					/>
					<farm-typography
						class="limit-visualizer__percentage"
						:color="hasExceededLimit ? 'warning' : 'primary'"
						weight="700"
					>
						{{ percentage || 0 }}%
					</farm-typography>
					<farm-typography
						size="sm"
						class="limit-visualizer__copy d-flex flex-column"
						:color="hasExceededLimit ? 'warning' : 'primary'"
						weight="500"
					>
						<span>Limite</span>
						<span>Utilizado</span>
					</farm-typography>
				</div>
				<div class="limit-visualizer__available d-flex flex-column">
					<farm-caption variation="regular">Limite Disponível</farm-caption>
					<farm-bodytext type="2">
						{{ brl(availableLimit) }}
					</farm-bodytext>
				</div>
			</div>
		</farm-col>
		<FinancingRequestSummary
			:requests="receivables"
			:total-requests-value="totalRequestsValue"
			:charges="charges"
			:total-to-pay-in-requests="totalToPayInRequestsText"
		/>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, toRefs, watch } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';
import type { Ref } from 'vue/types';

import { useAnalytics } from '@/composible';

import { useReceivablesCounters } from '../../composables';
import type { Receivable } from '../../types';
import FinancingRequestSummary from '../FinancingRequestSummary';
import FundMyOrdersLimit from '../FundMyOrdersLimit';

export default defineComponent({
	components: { FinancingRequestSummary, FundMyOrdersLimit },
	props: {
		receivables: {
			type: Array,
			default: () => [],
		},
		canAdvanceToNextStep: {
			type: Boolean,
			default: true,
		},
	},
	setup(props) {
		const {
			receivables,
			canAdvanceToNextStep,
		}: { receivables: Ref<Receivable[]>; canAdvanceToNextStep: Ref<boolean> } = toRefs(props);

		const { trigger } = useAnalytics();

		const {
			availableLimit,
			charges,
			hasExceededLimit,
			percentage,
			totalAvailableLimit,
			totalToPayInRequestsText,
			totalRequestsValue,
		} = useReceivablesCounters(receivables, canAdvanceToNextStep);

		watch(hasExceededLimit, newValue => {
			if (!newValue) {
				return;
			}

			trigger({
				event: 'has_exceeded_limit',
				payload: {
					event_category: 'title_emission',
					event_label: 'Seleção de recebíveis',
					initial_limit: totalAvailableLimit.value,
					exceeded: Math.round(
						totalRequestsValue.value + charges.value - totalAvailableLimit.value
					),
				},
			});
		});

		return {
			availableLimit,
			receivables,
			totalRequestsValue,
			hasExceededLimit,
			charges,
			totalToPayInRequestsText,
			percentage,
			brl,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './FinancingLimitContainer.scss';
</style>
