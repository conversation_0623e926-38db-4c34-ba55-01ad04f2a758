import MyClientsMyFinancingsDetail from '../views/MyClients/MyFinancingsDetail';
import MyClientsMyFinancingsHome from '../views/MyClients/MyFinancingsHome';
import MyClientsMyReceivablesHome from '../views/MyClients/MyReceivablesHome';

export const routes = [
	{
		path: 'titulos',
		name: 'MyClientsMyFinancingsHome',
		component: MyClientsMyFinancingsHome,
		meta: {
			title: 'Meus títulos',
			icon: 'currency-usd',
			roleKey: 'meusclientes.financiamentos',
			externalView: true,
		},
	},
	{
		path: 'titulos/pedidos',
		name: 'MyClientsMyReceivablesHome',
		component: MyClientsMyReceivablesHome,
		meta: {
			title: 'Detalhes do título',
			icon: 'currency-usd',
			roleKey: 'meusclientes.financiamentos',
			externalView: true,
		},
	},
	{
		path: 'titulos/:id(\\d+)',
		name: 'MyClientsMyFinancingsDetail',
		component: MyClientsMyFinancingsDetail,
		meta: {
			title: 'Meus Pedidos',
			icon: 'currency-usd',
			roleKey: 'meusclientes.financiamentos',
			externalView: true,
		},
	},
];
