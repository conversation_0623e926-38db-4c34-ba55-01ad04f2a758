import FundMyOrders from '../views/FundMyOrders';
import MyFinancingsDetail from '../views/MyFinancingsDetail';
import MyFinancingsHome from '../views/MyFinancingsHome';
import MyReceivablesHome from '../views/MyReceivablesHome';

export const routes = [
	{
		path: 'titulos',
		name: 'MyFinancingsHome',
		component: MyFinancingsHome,
		meta: {
			title: 'Meus títulos',
			icon: 'currency-usd',
			roleKey: 'wallet.financiamentos',
			useSubProduct: true,
		},
	},
	{
		path: 'titulos/:id(\\d+)',
		name: 'MyFinancingsDetail',
		component: MyFinancingsDetail,
		meta: {
			title: 'Detalhes do título',
			icon: 'currency-usd',
			roleKey: 'wallet.financiamentos',
			useSubProduct: true,
		},
	},
	{
		path: 'titulos/pedidos',
		name: 'MyReceivablesHome',
		component: MyReceivablesHome,
		meta: {
			title: 'Meus Pedidos',
			icon: 'currency-usd',
			roleKey: 'wallet.financiamentos',
			useSubProduct: true,
		},
	},
	{
		path: 'titulos/financiar/novo',
		name: 'FundMyOrders',
		component: FundMyOrders,
		meta: {
			title: 'Emitir novo título',
			icon: 'currency-usd',
			roleKey: 'wallet.financiamentos',
			useSubProduct: true,
		},
	},
];
