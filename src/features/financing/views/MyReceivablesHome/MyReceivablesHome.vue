<template>
	<FinancingsHomeTab>
		<farm-row class="my-6">
			<farm-col cols="12">
				<farm-card>
					<farm-card-content
						class="header-card-content d-flex flex-md-row md:flex-row pa-6"
					>
						<farm-idcaption copyText="" class="me-auto">
							<template v-slot:title> Produto </template>
							<template v-slot:subtitle>
								{{ receivablesSummary ? receivablesSummary.productName : '-' }}
							</template>
						</farm-idcaption>

						<farm-idcaption
							class="header-card-content__item"
							icon="check-circle-outline"
							copyText=""
						>
							<template v-slot:title> Disponíveis </template>
							<template v-slot:subtitle>
								{{ receivablesSummary ? receivablesSummary.available : '-' }}
							</template>
						</farm-idcaption>
						<farm-idcaption
							class="header-card-content__item"
							icon="dots-horizontal-circle-outline"
							copyText=""
						>
							<template v-slot:title> Em Utilização </template>
							<template v-slot:subtitle>
								{{ receivablesSummary ? receivablesSummary.inUse : '-' }}
							</template>
						</farm-idcaption>
					</farm-card-content>
				</farm-card>
			</farm-col>
		</farm-row>

		<farm-row align="center">
			<farm-col cols="12" md="6" class="d-flex justify-start">
				<farm-form-mainfilter
					label="Buscar Pedido"
					:has-extra-filters="false"
					@onInputChange="updateFilter"
				/>
			</farm-col>
		</farm-row>

		<div class="mx-n6 mt-n4">
			<farm-line />

			<MyReceivablesTable :items="receivablesList" :filters="filters" />
		</div>

		<farm-loader mode="overlay" v-if="isLoading" />
	</FinancingsHomeTab>
</template>
<script lang="ts">
import { defineComponent, onBeforeMount, ref } from 'vue';

import FinancingsHomeTab from '../../components/FinancingsHomeTab';
import MyReceivablesTable from '../../components/MyReceivables/MyReceivablesTable';
import useMyReceivables from '../../components/MyReceivables/composables/useMyReceivables';

export default defineComponent({
	components: { FinancingsHomeTab, MyReceivablesTable },
	setup() {
		const filters = ref({
			receivableNumber: '',
		});

		const {
			receivablesList,
			receivablesSummary,
			isLoading,
			isExternalView,
			fetchReceivablesList,
			fetchReceivablesSummary,
			fetchReceivablesListAll,
			fetchReceivablesSummaryAll,
		} = useMyReceivables(filters);

		const updateFilter = (value: string) => {
			filters.value.receivableNumber = value;
		};

		onBeforeMount(() => {
			if (isExternalView.value) {
				fetchReceivablesListAll();
				fetchReceivablesSummaryAll();
			} else {
				fetchReceivablesList();
				fetchReceivablesSummary();
			}
		});

		return {
			filters,
			receivablesSummary,
			receivablesList,
			isLoading,
			updateFilter,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './MyReceivablesHome.scss';
</style>
