const generateLogger = () => {
	const defaultLogger = {
		inProgress: { status: 'info' },
		success: { status: 'success' },
		failure: {
			status: 'error',
			icon: 'close-circle-outline',
		},
	} as const;

	return {
		DEFAULT: defaultLogger,
		LAST_STEP: {
			success: {
				...defaultLogger.success,
				icon: 'check',
			},
			failure: {
				...defaultLogger.failure,
				message: 'Operação cancelada',
			},
		},
	} as const;
};

const LOGGER = generateLogger();

export { LOGGER };
