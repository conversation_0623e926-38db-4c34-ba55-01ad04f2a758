<template>
	<farm-container>
		<farm-row align-content="space-between" class="mb-6">
			<farm-col cols="12" md="6">
				<farm-heading type="6">Detalhes do Título</farm-heading>
			</farm-col>
			<farm-col
				v-if="financingDetail"
				cols="12"
				md="6"
				class="d-flex align-center justify-end"
			>
				<farm-typography
					tag="span"
					size="sm"
					color="black"
					color-variation="30"
					class="mr-1"
				>
					Ultima Atualização:
				</farm-typography>
				<farm-typography tag="span" size="sm" weight="500">
					{{
						financingDetail.closedAt
							? defaultDateFormat(financingDetail.closedAt)
							: defaultDateFormat(financingDetail.updatedAt)
					}}
				</farm-typography>
			</farm-col>
		</farm-row>

		<div class="mx-n6">
			<farm-line no-spacing />
		</div>

		<farm-row class="py-6">
			<farm-col cols="12" v-if="hasAlertMessage">
				<farm-alertbox :icon="alertBox.icon" :color="alertBox.color" class="mb-6">
					{{ alertBox.message }}
				</farm-alertbox>
			</farm-col>
			<farm-col cols="12" class="mb-6">
				<farm-heading type="6">Status e atualizações</farm-heading>
			</farm-col>

			<farm-col cols="12" :class="{ 'mb-6': isFail }">
				<farm-logger
					class="financing-logger"
					:items="loggerItems"
					:vertical="false"
					align="left"
					@click.native="trackLoggerClick"
				/>
			</farm-col>

			<farm-col cols="12" v-if="isFail">
				<farm-card>
					<farm-card-content class="d-flex items-center">
						<farm-subtitle variation="regular" type="2" class="mr-2 mb-0">
							Em caso de dúvidas entre em contato através dos nossos canais de
							atendimento:
						</farm-subtitle>
						<div class="d-flex contact-message-card">
							<p class="contact-message-card__info">
								<farm-icon
									size="13"
									color="secondary-green"
									variation="darken"
									class="mr-1"
								>
									email-outline
								</farm-icon>
								<a href="mailto:<EMAIL>"><EMAIL></a>
							</p>
							<p class="contact-message-card__info">
								<farm-icon
									size="13"
									color="secondary-green"
									variation="darken"
									class="mr-1"
								>
									whatsapp
								</farm-icon>
								<a href="tel:+551135067800">(11) 3506-7800</a>
							</p>
						</div>
					</farm-card-content>
				</farm-card>
			</farm-col>
		</farm-row>

		<div class="mx-n6 mb-6">
			<farm-line no-spacing />
		</div>

		<farm-row v-if="financingDetail" class="mb-6">
			<farm-col v-if="isExternalView" cols="12" class="line-divided-container mb-6">
				<farm-bodytext type="2" tag="span" class="line-divided-container__item">
					<strong>Nome do sacado</strong>: {{ financingDetail.draweeName }}
				</farm-bodytext>
				<farm-bodytext type="2" tag="span" class="line-divided-container__item">
					<strong>Documento</strong>: {{ financingDetail.draweeDocument }}
				</farm-bodytext>
			</farm-col>
			<farm-col cols="12">
				<FinancingBallastSummaryCard :financing="financingDetail" />
			</farm-col>
		</farm-row>

		<div class="mx-n6 mb-6">
			<farm-line no-spacing />
		</div>

		<div class="d-flex justify-end">
			<farm-btn :to="$router.resolve({ name: backButtonRoute }).href">Voltar</farm-btn>
		</div>

		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, computed } from 'vue';

import { defaultDateFormat, formatHour } from '@farm-investimentos/front-mfe-libs-ts';
import type { ComputedRef } from 'vue/types';

import {
	useStore,
	useSelectedProductId,
	useRoute,
	useIsLoading,
	useGetter,
	useAnalytics,
} from '@/composible';

import FinancingBallastSummaryCard from '../../components/FinancingBallastSummary/FinancingBallastSummaryCard';
import { FINANCING_STATUSES } from '../../constants';
import type { FinancingDetailRequest, FinancingDetailAllRequest } from '../../services/types';
import type { Financing } from '../../types';

import { LOGGER } from './constants';

export default defineComponent({
	components: {
		FinancingBallastSummaryCard,
	},
	setup() {
		const route = useRoute();
		const store = useStore();
		const productId = useSelectedProductId().value;
		const { trigger } = useAnalytics();

		const isExternalView = computed(() => route.meta.externalView);
		const financingDetail: ComputedRef<Financing | null> = computed(
			useGetter('financing', 'financingDetail')
		);
		const financingDetailRequestStatus = computed(
			useGetter('financing', 'financingDetailRequestStatus')
		);
		const isSuccess = computed(
			() => financingDetail.value?.status === FINANCING_STATUSES.SUCCESS
		);
		const isFail = computed(() => financingDetail.value?.status === FINANCING_STATUSES.FAILED);
		const isInProgress = computed(
			() => financingDetail.value?.status === FINANCING_STATUSES.IN_PROGRESS
		);
		const loggerItems = computed(() => [
			{
				message: 'Operação criada',
				formattedDate: formatDate(financingDetail.value?.createdAt).formatted,
				status: 'normal',
				icon: 'check',
				...(isSuccess.value || isInProgress.value ? LOGGER.DEFAULT.success : {}),
				...(isFail.value ? LOGGER.DEFAULT.failure : {}),
			},
			{
				message: 'Em Análise',
				formattedDate: formatDate(financingDetail.value?.updatedAt).formatted,
				status: 'normal',
				icon: 'timer-sand',
				...(isInProgress.value ? LOGGER.DEFAULT.inProgress : {}),
				...(isSuccess.value ? LOGGER.DEFAULT.success : {}),
				...(isFail.value ? LOGGER.DEFAULT.failure : {}),
			},
			{
				message: 'Operação concluída',
				formattedDate: formatDate(financingDetail.value?.closedAt).formatted,
				status: 'normal',
				icon: 'check-circle-outline',
				...(isSuccess.value ? LOGGER.LAST_STEP.success : {}),
				...(isFail.value ? LOGGER.LAST_STEP.failure : {}),
			},
		]);
		const alertBox = computed(() => {
			const { date, hours } = formatDate(financingDetail.value?.closedAt);

			const alertProperties = {
				success: {
					color: 'success',
					icon: 'check-circle-outline',
					message: `Sua operação foi concluída dia ${date} às ${hours}`,
				},
				error: {
					color: 'error',
					icon: 'close-circle-outline',
					message: 'Seu título foi cancelado.',
				},
			};

			const typeDictionary = isSuccess.value ? 'success' : 'error';

			return alertProperties[typeDictionary];
		});
		const hasAlertMessage = computed(() => isSuccess.value || isFail.value);
		const backButtonRoute = computed(() =>
			isExternalView.value ? 'MyClientsMyFinancingsHome' : 'MyFinancingsHome'
		);

		const isLoading = useIsLoading([financingDetailRequestStatus]);

		const formatDate = (date: string) => {
			const result = {
				formatted: '',
				date: '',
				hours: '',
			};

			if (!date) return result;

			const splittedDate = date.split('T');

			const correctDate = splittedDate[0];

			result.formatted = `${defaultDateFormat(correctDate)} ${formatHour(
				date.replace(/Z$/, '')
			)}`;
			result.date = defaultDateFormat(correctDate);
			result.hours = formatHour(date.replace(/Z$/, ''));

			return result;
		};
		const trackLoggerClick = () => {
			trigger({
				event: 'stepper_click',
				payload: {
					event_category: 'title_emission',
					event_label: 'Stepper "Status e atualizações"',
					description: 'Clique no stepper "Status e atualizações"',
				},
			});
		};

		onBeforeMount(async () => {
			const request: FinancingDetailRequest | FinancingDetailAllRequest = {
				params: {
					productId,
					id: route.params.id,
				},
			};

			if (isExternalView.value) {
				await store.dispatch('financing/getFinancingDetailAll', request);
			} else {
				await store.dispatch('financing/getFinancingDetail', request);
			}
		});

		return {
			alertBox,
			financingDetail,
			isLoading,
			isFail,
			isSuccess,
			hasAlertMessage,
			loggerItems,
			backButtonRoute,
			isExternalView,
			trackLoggerClick,
			defaultDateFormat,
		};
	},
});
</script>

<style scoped lang="scss">
@import './MyFinancingsDetail.scss';
</style>
