.financing-logger {
	:deep(.logger__divider) {
		&[class$='-to-normal'] {
			background-color: var(--farm-neutral-base);
		}
	}

	:deep(.logger__item--normal) {
		i {
			background: none;
			border-color: var(--farm-bw-black-5);
		}

		> .farm-icon {
			&:before {
				color: var(--farm-gray-darken);
			}
		}
	}
}

.contact-message-card {
	&__info {
		padding: 0 16px;
		margin: 0;
		text-decoration: none;

		a {
			font-size: 14px;
			color: var(--farm-primary-base);
			text-decoration: none;
		}

		&:first-of-type {
			padding-left: 0;
		}

		&:last-of-type {
			padding-right: 0;
		}

		&:not(:last-of-type) {
			border-right: 1px solid var(--farm-stroke-base);
		}
	}
}

.line-divided-container {
	$p: &;

	&__item {
		&:not(:last-of-type) {
			margin-right: 8px;
			padding-right: 8px;
			border-right: 1px solid var(--farm-neutral-base);

			#{$p}--medium & {
				margin-right: 16px;
				padding-right: 16px;
			}

			#{$p}--big & {
				margin-right: 24px;
				padding-right: 24px;
			}
		}
	}
}
