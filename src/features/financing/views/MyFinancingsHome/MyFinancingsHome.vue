<template>
	<FinancingsHomeTab>
		<farm-row class="my-6">
			<farm-col cols="12">
				<MyFinancingsSummary :financing-summary="financingSummary" />
			</farm-col>
		</farm-row>

		<farm-row align="center" class="mb-6">
			<farm-col v-if="!isExternalView" cols="12" class="d-flex justify-end">
				<farm-btn @click="navigateToNewEmission">
					<farm-icon>plus</farm-icon>
					Emitir novo título
				</farm-btn>
			</farm-col>
			<farm-col v-else cols="12" md="6" class="d-flex justify-start">
				<farm-form-mainfilter
					label="Buscar Sacado"
					:has-extra-filters="false"
					@onInputChange="updateFilter"
				/>
			</farm-col>
		</farm-row>

		<div class="mx-n6">
			<farm-line />

			<MyFinancingsTable :items="financingList" :filters="filters" />
		</div>

		<MyFinancingsFinishModal :value="hasFinishedDraftFinancing" @on-close="onClose" />

		<farm-loader mode="overlay" v-if="isLoading" />
	</FinancingsHomeTab>
</template>
<script lang="ts">
import { defineComponent, onBeforeMount, ref, computed } from 'vue';

import { useRouter, useStore, useAnalytics } from '@/composible';

import FinancingsHomeTab from '../../components/FinancingsHomeTab';
import MyFinancingsFinishModal from '../../components/MyFinancings/MyFinancingsFinishModal';
import MyFinancingsSummary from '../../components/MyFinancings/MyFinancingsSummary';
import MyFinancingsTable from '../../components/MyFinancings/MyFinancingsTable';
import useMyFinancings from '../../components/MyFinancings/composables/useMyFinancings';

export default defineComponent({
	components: {
		FinancingsHomeTab,
		MyFinancingsTable,
		MyFinancingsFinishModal,
		MyFinancingsSummary,
	},
	setup() {
		const store = useStore();
		const router = useRouter();
		const { trigger } = useAnalytics();

		const filters = ref({
			search: '',
		});

		const {
			financingSummary,
			financingList,
			isLoading,
			isExternalView,
			onInputChangeMainFilter,
			fetchFinancingList,
			fetchFinancingSummary,
			fetchFinancingSummaryAll,
			fetchFinancingListAll,
		} = useMyFinancings(filters);

		const updateFilter = value => {
			filters.value.search = value;
		};

		const hasFinishedDraftFinancing = computed(
			// @ts-expect-error
			() => store.state.financing.hasFinishedDraftFinancing
		);

		const onClose = () => {
			store.commit('financing/setHasFinishedDraftFinancing', false);
		};
		const navigateToNewEmission = () => {
			trigger({
				event: 'button_click',
				payload: {
					event_category: 'title_emission',
					event_label: 'Botão Emitir novo título',
					description: 'Clique no botão Emitir novo título',
				},
			});

			router.push({ name: 'FundMyOrders' });
		};

		onBeforeMount(() => {
			if (isExternalView.value) {
				fetchFinancingListAll();
				fetchFinancingSummaryAll();
			} else {
				fetchFinancingList();
				fetchFinancingSummary();
			}
		});

		return {
			financingSummary,
			financingList,
			filters,
			isLoading,
			hasFinishedDraftFinancing,
			isExternalView,
			updateFilter,
			onInputChangeMainFilter,
			navigateToNewEmission,
			onClose,
		};
	},
});
</script>
