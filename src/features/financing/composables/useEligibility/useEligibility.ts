import { getCurrentInstance, ref, computed } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';
import type { ComputedRef } from 'vue/types';

import { useGetter, useSelectedProductId, useStore, useAnalytics } from '@/composible';

import { ELIGIBILITY_DIALOG } from '../../constants';
import type {
	CheckPreEligibilityRequest,
	GetPreEligibilityCheckerRequest,
} from '../../services/types';
import type { DraftFinancing, GroupedPreEligibility, PreEligibility } from '../../types';

export default function useEligibility(receivables = { value: [] }) {
	const store = useStore();
	const productId = useSelectedProductId().value;
	const internalInstance = getCurrentInstance().proxy;
	const { trigger } = useAnalytics();

	const retryGroupedReceivables = ref({});

	const draftFinancing: ComputedRef<DraftFinancing | null> = computed(
		useGetter('financing', 'draftFinancing')
	);
	const eligibility: ComputedRef<any> = computed(useGetter('financing', 'preEligibility'));
	const eligibilityRequestStatus = computed(
		useGetter('financing', 'preEligibilityRequestStatus')
	);

	const eligibleReceivablesSummary = computed(() => {
		const initialAccumulator: GroupedPreEligibility = {
			eligibles: [],
			quantityApproved: 0,
			total: 0,
			queueId: [],
			error: [],
		};

		return eligibility.value.reduce((accumulator, eligibility) => {
			if (!eligibility) {
				return accumulator;
			}

			const eligibles = eligibility?.eligibles || [];
			if (eligibility.error) {
				return;
			}
			const accumulatorError = Array.isArray(accumulator.error) ? accumulator.error : [];
			const accumulatorQueueId = Array.isArray(accumulator.queueId)
				? accumulator.queueId
				: [];

			accumulator.queueId = [...accumulatorQueueId, eligibility.queueId];
			accumulator.error = [...accumulatorError, eligibility.error];
			accumulator.eligibles = [...accumulator.eligibles, ...eligibles];
			accumulator.quantityApproved += eligibility.quantityApproved;
			accumulator.total += eligibility.total;

			return accumulator;
		}, initialAccumulator);
	});
	const hasNoReceivableApproved = computed(
		() => eligibleReceivablesSummary.value?.quantityApproved === 0
	);
	const hasAllReceivableApproved = computed(
		() =>
			eligibleReceivablesSummary.value?.quantityApproved ===
			eligibleReceivablesSummary.value?.total
	);
	const hasSomeReceivableApproved = computed(
		() =>
			eligibleReceivablesSummary.value?.quantityApproved > 0 &&
			!hasAllReceivableApproved.value
	);
	const hasAnyEligibilityError = computed(() =>
		(eligibility.value as PreEligibility[])?.some(({ error }) => error)
	);
	const hasRetryValues = computed(() => !!Object.keys(retryGroupedReceivables.value).length);
	const receivablesToSubmit = computed(() => {
		return receivables.value.length
			? receivables.value
			: draftFinancing.value.financings.flatMap(financing => financing.receivables);
	});
	const groupedReceivablesByDate = computed(() => {
		return receivablesToSubmit.value.reduce((accumulator, value) => {
			const key = createUniqueGroupKey(value);

			if (key in accumulator) {
				accumulator[key].push(value);
			} else {
				accumulator[key] = [value];
			}

			return accumulator;
		}, {});
	});
	const retrySubmitMessage = computed(() => {
		const reduceCallback = (accumulator, value) => {
			const parsedDate = defaultDateFormat(value.dueDate);
			const defaultErrorMessage = 'Erro ao enviar';

			return (accumulator += `<p>Vencimento <strong>${parsedDate}</strong>: ${defaultErrorMessage}</p>`);
		};

		if (!hasRetryValues.value) {
			return '';
		}

		return Object.values(retryGroupedReceivables.value)
			.flat()
			.sort((a: any, b: any) => +new Date(a.dueDate) - +new Date(b.dueDate))
			.reduce(reduceCallback, '');
	});

	// Group by due date AND financing date
	// if they are different, different groups will be created
	const createUniqueGroupKey = receivable => `${receivable.dueDate}--${receivable.financingDate}`;
	const mountEligibilityPromise = async (
		payload: GetPreEligibilityCheckerRequest['payload']
	): CheckPreEligibilityRequest['promises'][number] => {
		const request: GetPreEligibilityCheckerRequest = {
			payload,
			params: {
				productId,
			},
		};

		return store.dispatch('financing/getPreEligibilityChecker', request);
	};
	const handleEligibility = async (isSimulation: boolean = true) => {
		const valuesToSubmit = hasRetryValues.value
			? retryGroupedReceivables.value
			: groupedReceivablesByDate.value;

		const promises = Object.values(valuesToSubmit).map(value => {
			return mountEligibilityPromise({
				simulacao: isSimulation,
				dataDesembolso: value[0].dueDate,
				notas: value,
			});
		});

		const request: CheckPreEligibilityRequest = {
			promises,
		};

		await store.dispatch('financing/checkEligibility', request);

		const analytics = {
			description: 'Nenhum recebível passou na elegibilidade',
		};

		if (hasNoReceivableApproved.value || hasSomeReceivableApproved.value) {
			if (hasSomeReceivableApproved.value) {
				analytics.description = `${eligibleReceivablesSummary.value.total} recebíveis foram enviados e ${eligibleReceivablesSummary.value.quantityApproved} não passaram`;
			}

			trigger({
				event: 'eligibility_failed',
				payload: {
					event_category: 'title_emission',
					event_label: 'Elegibilidade',
					description: analytics.description,
				},
			});
		}

		if (hasAllReceivableApproved.value) {
			trigger({
				event: 'eligibility_succeeded',
				payload: {
					event_category: 'title_emission',
					event_label: 'Elegibilidade',
					description: 'Todos os recebíveis passaram na elegibilidade',
				},
			});
		}

		if (hasAnyEligibilityError.value) {
			retryGroupedReceivables.value = Object.values(valuesToSubmit)
				.filter((value, index) => !eligibility.value[index].queueId)
				.reduce((accumulator: Object, value: Object) => {
					const key = createUniqueGroupKey(value[0]);

					return {
						...accumulator,
						[key]: groupedReceivablesByDate.value[key],
					};
				}, {});

			if (isSimulation) {
				await ELIGIBILITY_DIALOG.SIMULATION(internalInstance, retrySubmitMessage.value);
			} else {
				await ELIGIBILITY_DIALOG.ERROR(internalInstance, retrySubmitMessage.value);
			}

			return false;
		}

		return true;
	};

	return {
		eligibleReceivablesSummary,
		hasNoReceivableApproved,
		hasAllReceivableApproved,
		hasSomeReceivableApproved,
		draftFinancing,
		eligibility,
		eligibilityRequestStatus,
		retrySubmitMessage,
		handleEligibility,
	};
}
