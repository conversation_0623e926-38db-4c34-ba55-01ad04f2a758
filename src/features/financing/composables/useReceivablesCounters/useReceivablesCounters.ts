import { ref, computed } from 'vue';

import { decimals } from '@farm-investimentos/front-mfe-libs-ts';
import type { ComputedRef, Ref } from 'vue/types';

import { useGetter } from '@/composible';

import type { Receivable, DraftFinancing } from '../../types';

export default function useReceivablesCounters(
	receivables: Ref<Receivable[]> = ref([]),
	canAdvanceToNextStep: Ref<boolean> = ref(true)
) {
	const draftFinancing: ComputedRef<DraftFinancing | null> = computed(
		useGetter('financing', 'draftFinancing')
	);

	const totalAvailableLimit = computed(() => draftFinancing.value?.availableLimit || 0);
	const charges = computed(() => {
		if (!canAdvanceToNextStep.value) {
			return 0;
		}
		return receivables.value.reduce((accumulator, { charges }) => {
			if (charges <= 0) {
				return accumulator;
			}

			return (accumulator += charges);
		}, 0);
	});
	const totalRequestsValue = computed(() =>
		receivables.value.reduce((accumulator, { value }) => {
			return accumulator + value;
		}, 0)
	);
	const totalToPayInRequests = computed(() => totalRequestsValue.value + charges.value);
	const totalToPayInRequestsText = computed(() =>
		canAdvanceToNextStep.value ? totalToPayInRequests.value : 0
	);
	const calculatedLimit = computed(() => totalAvailableLimit.value - totalToPayInRequests.value);
	const hasExceededLimit = computed(() =>
		totalToPayInRequests.value > 0 ? calculatedLimit.value <= 0 : false
	);
	const canShowUsedLimits = computed(
		() => (!canAdvanceToNextStep.value && hasExceededLimit.value) || canAdvanceToNextStep.value
	);
	const availableLimit = computed(() => {
		if (!canShowUsedLimits.value) {
			return totalAvailableLimit.value;
		}

		return hasExceededLimit.value ? 0 : calculatedLimit.value;
	});
	const percentage = computed(() => {
		if (!canShowUsedLimits.value) {
			return 0;
		}

		const totalPercentage = parseFloat(
			decimals(totalToPayInRequests.value, totalAvailableLimit.value).toString()
		);

		return totalPercentage <= 100 ? totalPercentage : parseInt(totalPercentage.toString());
	});

	return {
		availableLimit,
		charges,
		hasExceededLimit,
		percentage,
		totalAvailableLimit,
		totalRequestsValue,
		totalToPayInRequestsText,
	};
}
