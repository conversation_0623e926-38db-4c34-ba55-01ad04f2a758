import { AxiosResponse } from 'axios';

import { client as configurationClient } from '@/configurations/services/configurations';
import { client } from '@/configurations/services/financing';

import {
	GetFinancingListRequest,
	GetReceivablesByProductListRequest,
	GetFinancingSummaryRequest,
	EditDraftStepRequest,
	SimulateFinancingPriceRequest,
	GetReceivablesSummaryRequest,
	GetAllowedDatesRequest,
	CreateDraftFinancingRequest,
	DeleteDraftFinancingRequest,
	CheckOnboardingStatusRequest,
	GetPreEligibilityCheckerRequest,
	FinancingDetailRequest,
	DeleteReceivableFromFinancing,
} from './types';

export const getReceivablesListByProduct = (
	request: GetReceivablesByProductListRequest
): Promise<AxiosResponse> =>
	client.get(
		`/financing/${request.params.productId}/list/receivables/?${new URLSearchParams(
			request.query
		)}`
	);

export const getReceivablesSummary = (
	request: GetReceivablesSummaryRequest
): Promise<AxiosResponse> => client.get(`/financing/${request.params.productId}/summary/counter`);

export const getFinancingList = (request: GetFinancingListRequest): Promise<AxiosResponse> => {
	const { productId } = request.params;

	return client.get(`/financing/${productId}/list?${new URLSearchParams(request.query)}`);
};

export const getFinancingSummary = (
	request: GetFinancingSummaryRequest
): Promise<AxiosResponse> => {
	const { productId } = request.params;

	return client.get(`/financing/${productId}/summary`);
};

export const getAllowedDates = (request: GetAllowedDatesRequest): Promise<AxiosResponse> => {
	return configurationClient.get(`/v1/origination/${request.params.productId}/datepicker`, {
		params: request.query,
	});
};

export const simulateFinancingPrice = ({
	payload,
}: SimulateFinancingPriceRequest): Promise<AxiosResponse> =>
	client.post(`/financing/simulate/price/`, {
		simulations: payload,
	});

export const createDraftFinancing = (
	request: CreateDraftFinancingRequest
): Promise<AxiosResponse> =>
	client.post(
		`/financing/${request.params.productId}/draft/new?${new URLSearchParams(request.query)}`
	);

export const editDraftStep = (request: EditDraftStepRequest): Promise<AxiosResponse> => {
	const { draftId, productId, step } = request.params;

	return client.post(`/financing/${productId}/draft/${draftId}/step/${step}`, {
		receivables: request.payload,
	});
};

export const deleteDraftFinancing = (
	request: DeleteDraftFinancingRequest
): Promise<AxiosResponse> =>
	client.delete(`/financing/${request.params.productId}/draft/${request.params.draftId}`);

export const checkOnboardingStatus = (
	request: CheckOnboardingStatusRequest
): Promise<AxiosResponse> =>
	client.post(
		`/onboarding/${request.params.productId}/financing/find?${new URLSearchParams(
			request.query
		)}`
	);

export const checkPreEligibility = (
	request: GetPreEligibilityCheckerRequest
): Promise<AxiosResponse> =>
	client.post(
		`/financing/${request.params.productId}/eligibility?${new URLSearchParams(request.query)}`,
		request.payload,
		{ timeout: 120 * 1000 }
	);

export const getFinancingDetail = (request: FinancingDetailRequest): Promise<AxiosResponse> =>
	client.get(`/financing/${request.params.productId}/${request.params.id}/`);

export const deleteReceivableFromFinancing = (
	request: DeleteReceivableFromFinancing
): Promise<AxiosResponse> =>
	client.delete(
		`/financing/${request.params.productId}/draft/${request.params.draftId}/receivable/${request.params.receivableId}`
	);
