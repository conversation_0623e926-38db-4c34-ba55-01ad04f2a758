import { RequestQueryString } from '@/types';

import { Receivable, ReceivableSimulation, PreEligibility } from '../types';

export type EditDraftStepRequest = {
	params: {
		productId: number;
		draftId: number;
		step: 1 | 2 | 3;
	};
	payload?: Receivable[] | { queueEligibility: string };
};

export type SimulateFinancingPriceRequest = {
	payload: ReceivableSimulation[];
};

export type CreateDraftFinancingRequest = {
	params: {
		productId: number;
	};
	query?: RequestQueryString;
};

export type DeleteDraftFinancingRequest = {
	params: {
		productId: number;
		draftId: number;
	};
};

export type GetReceivablesByProductListRequest = {
	params: {
		productId: number;
	};
	query?: RequestQueryString;
};

export type GetReceivablesByProductListAllRequest = GetReceivablesByProductListRequest;

export type GetFinancingListRequest = {
	params: {
		productId: number;
	};
	query?: RequestQueryString;
};

// My clients view
export type GetFinancingListAllRequest = GetFinancingListRequest;

export type GetFinancingSummaryRequest = {
	params: {
		productId: number;
	};
};

// My clients view
export type GetFinancingSummaryAllRequest = GetFinancingSummaryRequest;

export type GetAllowedDatesRequest = {
	params: {
		productId: number;
	};
	query: RequestQueryString;
};

export type GetReceivablesSummaryRequest = {
	params: {
		productId: number;
	};
};

export type GetReceivablesSummaryAllRequest = GetReceivablesSummaryRequest;

export type CheckOnboardingStatusRequest = {
	params: {
		productId: number;
	};
	query?: RequestQueryString;
};

export type GetPreEligibilityCheckerRequest = {
	payload: any;
	params: {
		productId: number;
	};
	query?: RequestQueryString;
};

export type CheckPreEligibilityRequest = {
	promises: Promise<{ data: PreEligibility }>[];
};

export type FinancingDetailRequest = {
	params: {
		productId: number;
		id: number;
	};
};

export type FinancingDetailAllRequest = FinancingDetailRequest;

export type DeleteReceivableFromFinancing = {
	params: {
		productId: number;
		draftId: number;
		receivableId: number;
	};
};
