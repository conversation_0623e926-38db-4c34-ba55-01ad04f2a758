import type { AxiosResponse } from 'axios';

import { client } from '@/configurations/services/financing';

import type {
	FinancingDetailAllRequest,
	GetFinancingListAllRequest,
	GetFinancingSummaryAllRequest,
	GetReceivablesByProductListAllRequest,
	GetReceivablesSummaryAllRequest,
} from './types';

export const getFinancingSummaryAll = (
	request: GetFinancingSummaryAllRequest
): Promise<AxiosResponse> => {
	const { productId } = request.params;

	return client.get(`/financing/${productId}/summary/all`);
};

export const getFinancingListAll = (
	request: GetFinancingListAllRequest
): Promise<AxiosResponse> => {
	const { productId } = request.params;

	return client.get(`/financing/${productId}/list/all`, {
		params: request.query,
	});
};

export const getReceivablesListAll = (
	request: GetReceivablesByProductListAllRequest
): Promise<AxiosResponse> =>
	client.get(`/financing/${request.params.productId}/list/receivables/all/`, {
		params: request.query,
	});

export const getReceivablesSummaryAll = (
	request: GetReceivablesSummaryAllRequest
): Promise<AxiosResponse> =>
	client.get(`/financing/${request.params.productId}/summary/counter/all`);

export const getFinancingDetailAll = (request: FinancingDetailAllRequest): Promise<AxiosResponse> =>
	client.get(`/financing/${request.params.productId}/${request.params.id}/all`);
