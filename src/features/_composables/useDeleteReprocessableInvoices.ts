import { ref, Ref } from 'vue';

import { deleteReprocessableBatchesInvoices } from '../managementCession/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

type DeleteReprocessableBatchesInvoicesArgs = {
	productId: string;
	batches: {
		id: number;
		danfes: string[];
	}[];
};

export function useDeleteReprocessableInvoices() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref(null);
	const error = ref<string | null>(null);

	const deleteReprocessableInvoicesRequest = async (
		payload: DeleteReprocessableBatchesInvoicesArgs
	) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await deleteReprocessableBatchesInvoices(payload);
			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		deleteReprocessableInvoicesRequest,
	};
}
