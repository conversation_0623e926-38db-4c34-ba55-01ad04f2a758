import { ref, Ref } from 'vue';

import { getProductHeaders } from '../managementCession/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

export type ProductHeader = {
	id: number;
	name: string;
	type: string;
};

export function useFetchProductHeaders() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref<ProductHeader | null>(null);
	const error = ref<string | null>(null);

	const fetchProductHeaders = async (productId: number) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await getProductHeaders(productId);
			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		fetchProductHeaders,
	};
}
