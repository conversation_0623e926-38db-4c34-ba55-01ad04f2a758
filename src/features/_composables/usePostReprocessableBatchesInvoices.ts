import { ref, Ref } from 'vue';

import { postReprocessableBatchesInvoices } from '../managementCession/services';
import { PostReprocessableBatchesInvoicesArgs } from '../managementCession/services/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

export function usePostReprocessableBatchesInvoices() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref(null);
	const error = ref<string | null>(null);

	const postReprocessableBatchesInvoicesRequest = async (
		payload: PostReprocessableBatchesInvoicesArgs
	) => {
		state.value = 'LOADING';
		error.value = null;

		try {
			const response = await postReprocessableBatchesInvoices(payload);
			data.value = response.data;
			state.value = 'SUCCESS';
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		postReprocessableBatchesInvoicesRequest,
	};
}
