export function builderReceivables(response) {
	const { content, pageable } = response.data;
	let data = [];

	if (content !== null) {
		data = content.map((item, index) => {
			return {
				keys: `${item.danfe}-${index}`,
				id: item.id,
				number: item.number,
				name: item.draweeName,
				document: item.draweeDocument,
				originatorName: item.providerName,
				originatorDocument: item.providerDocument,
				expirationDate: item.expirationDate,
				emissionDate: item.issueDate,
				valueLiquid: item.valueLiquid,
				valueFree: item.valueFree,
				nominalValue: item.value,
				cfopCode: item.cfopCode,
				part: item.part,
				totalParts: item.totalParts,
				danfe: item.danfe,
				isPart: item.totalParts > 1,
				selected: false,
				disabled: item.valueLiquid === null,
				hasContactInfo: item.hasContactInfo,
				draweeFone: item.draweeFone,
				draweeEmail: item.draweeEmail,
				noLimitDrawees: item.noLimitDrawees,
			};
		});
	}
	return {
		content: data,
		pagination: {
			...pageable,
		},
	};
}
