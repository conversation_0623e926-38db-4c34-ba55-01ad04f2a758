<template>
	<farm-container>
		<header-steps
			title="Selecione os recebíveis que deseja ceder"
			class="mb-4"
			:currentStep="3"
		/>
		<farm-row>
			<farm-col cols="12">
				<information-cession-list
					edit
					:disabledButtonEdit="isLoadingSkeleton"
					:dateDisbursement="headerData.dateDisbursement"
					:limit="headerData.limit"
					:valueDisbursement="totalValueDisbursement"
					:productName="headerData.productName"
					:typeOperation="headerData.typeOperation"
					@onClickDisbursement="onOpenModalDetailsCession"
					@onClickEdit="onClickEdit"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease>
			<farm-line />
		</farm-row>
		<feedback-alert
			color="error"
			v-if="!isLoadingSkeleton && isFeedbackNotPrecified && originators.length > 0"
			class="mb-6"
		>
			Seus recebíveis não foram precificados. Entre em contato com nosso time de atendimento.
		</feedback-alert>
		<feedback-alert
			v-if="!isLoadingSkeleton && isFeedbackLimit && isRoofLimit === 'vermelha'"
			color="error"
			class="mb-6"
		>
			Você ultrapassou seu limite disponível, remova alguns recebíveis da seleção para
			prosseguir.
		</feedback-alert>
		<feedback-alert
			v-if="
				!isLoadingSkeleton &&
				!isFeedbackNotPrecified &&
				isRenderFirstValues !== null &&
				!isRenderFirstValues &&
				isMinValue
			"
			v-model="isMinValue"
			color="error"
			class="mb-6"
		>
			É necessário selecionar no mínimo {{ minValue }} em recebíveis para continuar. Tente
			selecionar mais recebíveis ou faça um upload.
		</feedback-alert>
		<feedback-alert
			v-if="!isLoadingSkeleton && isFeedbackReceivables && !isMinValue"
			class="mb-6"
		>
			Você não tem recebíveis suficientes para atingir o valor de desembolso desejado. Caso
			queira, faça o upload de novos recebíveis.
		</feedback-alert>
		<feedback-alert
			color="neutral"
			v-if="
				!isLoadingSkeleton &&
				!isFeedbackNotPrecified &&
				isRenderFirstValues !== null &&
				!isRenderFirstValues &&
				isRoofLimit === 'cinza'
			"
			class="mb-6"
		>
			Prezado parceiro, o limite máximo para essa cessão no produto
			<b>{{ headerData.productName }}</b> deve ser de {{ labelMaxValue }}. Entre em contato
			com o responsável pela sua conta FarmTech para realizar uma operação de maior valor
			neste produto.
		</feedback-alert>
		<feedback-alert
			color="neutral"
			v-model="isFeedbackValue"
			v-if="
				!isLoadingSkeleton &&
				!isFeedbackLimit &&
				isFeedbackValue &&
				operationDataCache.isEditedValue
			"
			class="mb-6"
		>
			Você ultrapassou o valor desejado informado de <b>{{ dataFeedbackValue }}</b
			>.
		</feedback-alert>
		<filter-skeleton class="mt-6 mb-6" v-if="isLoadingSkeleton" />
		<animated-fade-in v-if="!isLoadingSkeleton && !isError">
			<farm-row align="center" class="mt-6">
				<farm-col md="6" cols="12">
					<div class="d-flex align-center justify-center">
						<farm-form-mainfilter
							outlined
							label="Buscar Recebível"
							:showFilters="isOpenFilter"
							@onInputChange="onInputChangeMainFilter"
							@onClick="onClickMainFilter"
						>
							<div class="d-flex">
								<farm-label class="mb-0 mr-2">Buscar Recebível</farm-label>
								<counted
									v-if="showResultsLabel"
									pluralText="selecionados"
									singularText="selecionado"
									:value="totalFilter"
								/>
							</div>
						</farm-form-mainfilter>
					</div>
				</farm-col>
				<farm-col align="right" md="6" cols="12">
					<div>
						<farm-btn
							v-if="originators.length > 0"
							class="farm-btn--responsive"
							title="Desmarcar Selecionados"
							outlined
							:disabled="isDisabledUncheck"
							@click="onUncheckReceivables"
						>
							Desmarcar Selecionados
						</farm-btn>
					</div>
				</farm-col>
				<farm-col cols="12" v-if="isFeedbackNotEnoughPrecified">
					<feedback-alert color="warning" class="mb-6">
						Alguns dos seus recebíveis não foram precificados. Entre em contato com
						nosso time de atendimento.
					</feedback-alert>
				</farm-col>
			</farm-row>
		</animated-fade-in>
		<collapse-transition :duration="300">
			<form-filter
				v-show="isOpenFilter"
				:filterCurrent="filterCurrent"
				@onFiltersApplied="onFiltersApplied"
				@onApply="onApplyFilter"
			/>
		</collapse-transition>
		<farm-row v-if="!isLoadingSkeleton">
			<farm-col cols="12" md="4" class="mb-4" v-if="originators.length > 0">
				<farm-chip color="primary" variation="lighten" :dense="true">
					{{ totalSeleted }} selecionado (s)
				</farm-chip>
			</farm-col>
		</farm-row>
		<originator-list-skeleton :numberItems="10" v-if="isLoadingSkeleton" />

		<animated-fade-in v-if="!isLoadingSkeleton && !isError">
			<originator-list
				:data="originators"
				:filters="filterCurrent"
				:resetAllCheckbox="resetAllCheckbox"
				:inputFilterActive="inputFilterActive"
				:filterActive="isFilterCounter"
				@onUpdatedLoadingReceivablesCard="onUpdatedLoadingReceivablesCard"
				@onOpenModalOriginator="onOpenModalOriginator"
				@onOpenModalLimitDrawee="onOpenModalLimitDraweeClicked"
			/>
		</animated-fade-in>
		<farm-row extra-decrease>
			<farm-box>
				<farm-datatable-paginator
					v-if="!isError && originators.length > 0"
					class="mt-6 mb-n6"
					:page="page"
					:totalPages="pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<div v-if="!firstRender && isLoading" class="my-4 d-flex justify-center">
			<loading-inline mode="overlay" />
		</div>
		<div v-if="isError" class="my-4 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-buttons
			labelConfirm="Continuar"
			:isDisabledButtonConfirm="isDisabledButtonConfirm"
			:hiddenBack="hiddenButtonBack"
			@onClickBack="onClickBack"
			@onClickConfirm="onClickConfirm"
			@onClickClose="onClickClose"
		/>
		<modal-close-cession
			v-model="isModalCloseCession"
			v-if="isModalCloseCession"
			@onConfirm="onConfirmModalCloseCession"
			@onClose="onCloseModalCloseCession"
		/>
		<modal-detail-cession
			v-if="isModalDetailCession"
			v-model="isModalDetailCession"
			:data="dataModalDetailCession"
			:receivables="false"
			@onClose="onCloseModalModalDetailCession"
		/>
		<modal-date-and-value
			v-if="isModalDateAndValue"
			v-model="isModalDateAndValue"
			:data="dataValuesAndDates"
			@onConfirm="onConfirmModalDateAndValue"
			@onClose="onCloseModalDateAndValue"
		/>
		<modal-drawee
			v-model="isModalDrawee"
			v-if="isModalDrawee"
			:originatorCurrent="originatorCurrent"
			@onClose="onCloseModalDrawee"
		/>
		<modal-limit-drawee
			v-model="isModalLimitDrawee"
			v-if="isModalLimitDrawee"
			:originatorCurrent="originatorCurrent"
			@onClose="onCloseModalLimitDrawee"
		/>
		<farm-loader mode="overlay" v-if="isLoadingReceivablesCard" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref, provide } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import AnimatedFadeIn from '@/components/AnimatedFadeIn';
import Counted from '@/components/Counted';
import FeedbackAlert from '@/components/FeedbackAlert';
import InformationCessionList from '@/components/InformationCessionList';
import LabelRequestResults from '@/components/LabelRequestResults';
import LoadingInline from '@/components/LoadingInline';
import ModalCloseCession from '@/components/ModalCloseCession';
import ModalDetailCession from '@/components/ModalDetailCession';
import { useRoute, useSelectedProductId } from '@/composible';
import { useModal } from '@/composible/useModal';
import { usePageable } from '@/composible/usePageable';
import { currencyUnmask } from '@/helpers/masks';
import { getStorage, removeAll } from '@/helpers/sessionStorage';

import { useOperationCache } from '../../composables/useOperationCache';
import { useProductCache } from '../../composables/useProductCache';
import { useRedirect } from '../../composables/useRedirect';
import { useSelectedProducts } from '../../composables/useSelectedProducts';
import { useStorage } from '../../composables/useStorage';
import FooterButtons from '../FooterButtons';
import HeaderSteps from '../HeaderSteps';

import FilterSkeleton from './components/FilterSkeleton';
import FormFilter from './components/FormFilter';
import ModalDateAndValue from './components/ModalDateAndValue';
import ModalDrawee from './components/ModalDrawee';
import ModalLimitDrawee from './components/ModalLimitDrawee';
import OriginatorList from './components/OriginatorList';
import OriginatorListSkeleton from './components/OriginatorListSkeleton';
import { useAutomaticSelection } from './composables/useAutomaticSelection';
import { useOriginators } from './composables/useOriginators';
import { usePricing } from './composables/usePricing';
import { useReceivablesSelectedCache } from './composables/useReceivablesSelectedCache';

export default defineComponent({
	name: 'body-receivables',
	components: {
		AnimatedFadeIn,
		FooterButtons,
		HeaderSteps,
		InformationCessionList,
		LabelRequestResults,
		ModalCloseCession,
		ModalDetailCession,
		OriginatorList,
		LoadingInline,
		OriginatorListSkeleton,
		FilterSkeleton,
		FormFilter,
		Counted,
		ModalDateAndValue,
		FeedbackAlert,
		ModalDrawee,
		ModalLimitDrawee,
	},
	setup() {
		const route = useRoute();
		const preSectionId = route.query.id_pre_section;
		const {
			commercialProductDataCache,
			operationDataCache,
			addOperationCache,
			addCommercialProductCache,
		} = useOperationCache();

		const { hasData } = useStorage();
		const { redirectStepOne, redirectStepTwo, redirectHome, redirectPageSuccess, hasParams } =
			useRedirect();
		const {
			isOpenModal: isModalCloseCession,
			onCloseModal: onCloseModalCloseCession,
			onOpenModal: openModalCloseCession,
		} = useModal();
		const {
			isOpenModal: isModalDetailCession,
			onCloseModal: onCloseModalModalDetailCession,
			onOpenModal: onOpenModalModalDetailCession,
		} = useModal();
		const {
			isOpenModal: isModalDateAndValue,
			onCloseModal: onCloseModalDateAndValue,
			onOpenModal: onOpenModalDateAndValue,
		} = useModal();
		const {
			isOpenModal: isModalDrawee,
			onCloseModal: onCloseModalDrawee,
			onOpenModal: onOpenModalDrawee,
		} = useModal();
		const {
			isOpenModal: isModalLimitDrawee,
			onCloseModal: onCloseModalLimitDrawee,
			onOpenModal: onOpenModalLimitDrawee,
		} = useModal();

		const { isErrorPricing, isLoadingPricing, sendPricing } = usePricing();
		const {
			getOriginators,
			isLoadingOriginators,
			isErrorOriginators,
			originators,
			originatorsPagination,
		} = useOriginators();
		const { getAutomaticSelection, isErrorAutomaticSelection, isLoadingAutomaticSelection } =
			useAutomaticSelection();
		const {
			receivablesSelectedDataCache,
			updatedReceivablesSelectedCache,
			cleanCache,
			getPayload,
		} = useReceivablesSelectedCache();
		const { addSelectedDataToCache } = useSelectedProducts();
		const { addDataCache } = useProductCache();

		const {
			page,
			pagination,
			isOpenFilter,
			isFilterCounter,
			onApplyFilter,
			onClickMainFilter,
			onFiltersApplied,
		} = usePageable(
			{
				calbackFn: params => {
					filterCurrent.value = {
						...params,
					};
					if (!filterCurrent.value.roof) {
						filterCurrent.value.roof =
							operationDataCache.value?.valueCession ||
							commercialProductDataCache.value?.valueCession;
					}
					getOriginators(filterCurrent.value, updatedOriginators);
				},
				filters: {},
				keyInputSearch: 'search',
				sort: {
					order: 'ASC',
					orderby: 'providerName',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			originatorsPagination
		);

		const valueSeleted = ref(0);
		const totalReceivablesProduct = ref(0);
		const dataModalDetailCession = ref({
			dateDisbursement: null,
			limit: 0,
			valueDisbursement: 0,
			productName: '-',
			typeOperation: '-',
			freeValue: 0,
			nominalValue: 0,
		});
		const headerData = ref({
			dateDisbursement: '-',
			limit: '-',
			valueDisbursement: '-',
			productName: '-',
			typeOperation: '-',
		});
		const isFilterApplied = ref(false);
		const isFilterShowing = ref(false);
		const filterCurrent = ref({});
		const firstRender = ref(true);
		const totalFilter = ref(0);
		const showResultsLabel = ref(false);
		const resetAllCheckbox = ref(false);
		const hiddenButtonBack = ref(false);
		const isFeedbackNotEnoughPrecified = ref(null);
		const isFeedbackNotPrecified = ref(null);
		// const labelMaxValue = ref(brl(checkValueMaxIsNull()));
		const isRenderFirstValues = ref(null);
		const dataValuesAndDates = ref(null);
		const isLoadingReceivablesCard = ref(false);
		const originatorCurrent = ref(null);
		const inputFilterActive = ref(false);

		const isCollapsed = ref(false);

		const labelMaxValue = computed(() => brl(commercialProductDataCache.value.maxValue || 0));
		const minValue = computed(() => brl(commercialProductDataCache.value.minValue || 0));

		const totalValueDisbursement = computed(() => {
			if (receivablesSelectedDataCache.value) {
				return updatedTotalValue(receivablesSelectedDataCache.value.data);
			}
			if (operationDataCache.value) {
				return brl(operationDataCache.value.valueDisbursement);
			}
			return brl(0);
		});
		const totalValueFree = computed(() => {
			if (receivablesSelectedDataCache.value) {
				return updatedTotalValueFree(receivablesSelectedDataCache.value.data);
			}
			return brl(0);
		});
		const totalNominalValue = computed(() => {
			if (receivablesSelectedDataCache.value) {
				return updatedTotalNominalValue(receivablesSelectedDataCache.value.data);
			}
			return brl(0);
		});
		const totalSeleted = computed(() => {
			if (receivablesSelectedDataCache.value) {
				return updatedTotalSeletect(receivablesSelectedDataCache.value.data);
			}
			return 0;
		});
		const isDisabledUncheck = computed(() => {
			if (receivablesSelectedDataCache.value) {
				return receivablesSelectedDataCache.value.length === 0;
			}
			return true;
		});
		const isLoading = computed(() => {
			return (
				isLoadingOriginators.value ||
				isLoadingPricing.value ||
				isLoadingAutomaticSelection.value
			);
		});
		const isError = computed(() => {
			return (
				isErrorOriginators.value || isErrorPricing.value || isErrorAutomaticSelection.value
			);
		});
		const isFeedbackLimit = computed(() => {
			if (
				currencyUnmask(totalValueDisbursement.value) >
				currencyUnmask(headerData.value.limit)
			) {
				return true;
			}
			return false;
		});
		const isLoadingSkeleton = computed(() => {
			return firstRender.value && isLoading.value;
		});
		const isMinValue = computed(() => {
			const value = checkValueMinIsNull();
			return (
				isRenderFirstValues.value !== null &&
				!isRenderFirstValues.value &&
				currencyUnmask(totalValueDisbursement.value) <= value
			);
		});
		const isMaxValue = computed(() => {
			const value = checkValueMaxIsNull();
			return (
				!isLoadingSkeleton.value && currencyUnmask(totalValueDisbursement.value) >= value
			);
		});

		const isFeedbackValue = computed(() => {
			if (
				valueSeleted.value > 0 &&
				valueSeleted.value < currencyUnmask(totalValueDisbursement.value)
			) {
				return true;
			}
			return false;
		});
		const dataFeedbackValue = computed(() => {
			if (operationDataCache.value !== null) {
				if (operationDataCache.value?.valueCession) {
					return brl(operationDataCache.value?.valueCession);
				}
				return headerData.value.limit;
			}
			return headerData.value.limit;
		});
		const isFeedbackReceivables = computed(() => {
			if (
				totalReceivablesProduct.value === totalSeleted.value &&
				currencyUnmask(totalValueDisbursement.value) < currencyUnmask(headerData.limit)
			) {
				return true;
			}
			return false;
		});

		const isRoofLimit = computed(() => {
			const limitAvaliable =
				commercialProductDataCache.value?.availableLimit ||
				operationDataCache.value?.valueAvailable;

			const cashAvaliable = commercialProductDataCache.value?.maxValue;
			const disbursementValue = currencyUnmask(totalValueDisbursement.value);

			if (cashAvaliable > limitAvaliable) {
				if (disbursementValue < cashAvaliable && disbursementValue > limitAvaliable) {
					return 'vermelha';
				} else if (
					disbursementValue > limitAvaliable &&
					disbursementValue > cashAvaliable
				) {
					return 'cinza';
				}
			}

			if (disbursementValue > cashAvaliable && disbursementValue < limitAvaliable) {
				return 'cinza';
			} else if (disbursementValue > cashAvaliable && disbursementValue > limitAvaliable) {
				return 'vermelha';
			}
		});

		const isDisabledButtonConfirm = computed(() => {
			if (isMinValue.value) {
				return true;
			}
			if (isFeedbackNotPrecified.value) {
				return true;
			}
			if (totalSeleted.value === 0) {
				return true;
			}
			if (isFeedbackLimit.value) {
				return true;
			}
			if (isMaxValue.value) {
				return true;
			}
			return false;
		});

		const setCollapse = value => {
			isCollapsed.value = value;
		};

		function onOpenModalLimitDraweeClicked(data) {
			originatorCurrent.value = {
				...data,
				commercialProductId: commercialProductDataCache.value.idProductAccount,
			};
			onOpenModalLimitDrawee();
		}

		function onOpenModalOriginator(data) {
			originatorCurrent.value = data;
			onOpenModalDrawee();
		}

		function updatedTotalValue(data) {
			let total = 0;
			const processedIds = new Set();
			// eslint-disable-next-line no-unused-vars
			for (const [_, value] of Object.entries(data)) {
				// eslint-disable-next-line no-unused-vars
				for (const item of value) {
					if (!processedIds.has(item.id)) {
						total += item.netValue;
						processedIds.add(item.id);
					}
				}
			}

			return brl(total);
		}

		function updatedTotalValueFree(data) {
			let total = 0;
			// eslint-disable-next-line no-unused-vars
			for (const [_, value] of Object.entries(data)) {
				const newValue = value.reduce((acc, item) => {
					return acc + item.freeValue;
				}, 0);
				total += newValue;
			}
			return brl(total);
		}

		function updatedTotalNominalValue(data) {
			let total = 0;
			// eslint-disable-next-line no-unused-vars
			for (const [_, value] of Object.entries(data)) {
				const newValue = value.reduce((acc, item) => {
					return acc + item.nominalValue;
				}, 0);
				total += newValue;
			}
			return brl(total);
		}

		// function updatedTotalSeletect(data) {
		// 	console.log('DATA DO UPDATE SELCTED ->', data);
		// 	let total = 0;

		// 	// eslint-disable-next-line no-unused-vars
		// 	for (const [_, value] of Object.entries(data)) {
		// 		total += value.length;
		// 	}
		// 	return total;
		// }

		function updatedTotalSeletect(data) {
			const uniqueIds = new Set();

			for (const entries of Object.values(data)) {
				for (const item of entries) {
					uniqueIds.add(item.id);
				}
			}
			return uniqueIds.size;
		}

		function onChangePage(value): void {
			filterCurrent.value = {
				...filterCurrent.value,
				page: value - 1,
			};
			if (!filterCurrent.value.roof) {
				filterCurrent.value.roof =
					operationDataCache.value?.valueCession ||
					commercialProductDataCache.value?.valueCession;
			}

			getOriginators(filterCurrent.value, updatedOriginators);
		}

		function onChangePageLimit(value): void {
			filterCurrent.value = {
				...filterCurrent.value,
				page: 0,
				limit: value,
			};
			if (!filterCurrent.value?.roof) {
				filterCurrent.value?.roof =
					operationDataCache.value?.valueCession ||
					commercialProductDataCache.value?.valueCession;
			}
			getOriginators(filterCurrent.value, updatedOriginators);
		}

		function onClickEdit(): void {
			onOpenModalDateAndValue();
		}

		function onUncheckReceivables(): void {
			cleanCache();
			resetAllCheckbox.value = true;
		}

		function onConfirmModalCloseCession(): void {
			redirectHome();
		}

		function onConfirmModalDateAndValue(data): void {
			let valueCession = data.value;

			if (data.editValue) {
				valueCession = currencyUnmask(data.value);
			}
			addOperationCache({
				valueCession,
				availableDateCession: data.availableDates,
			});
			onCloseModalDateAndValue();
			firstRender.value = true;
			load();
		}

		function onClickBack(): void {
			redirectStepTwo();
		}

		function onClickConfirm(): void {
			const invoices = getPayload();
			const sendData = {
				invoices,
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				isEdit: false,
				sumNominalValue: currencyUnmask(totalNominalValue.value),
				valueNominal: currencyUnmask(totalNominalValue.value),
				sumFreeValue: currencyUnmask(totalValueFree.value),
				minValue: commercialProductDataCache.value.minValue,
				maxValue: commercialProductDataCache.value.maxValue || null,
				commercialProductId: commercialProductDataCache.value.idProductAccount,
			};
			const cache = {
				idProduct: commercialProductDataCache.value.productId,
				nameProduct: commercialProductDataCache.value.name,
				idOperation: commercialProductDataCache.value.operationId,
				nameOperation: commercialProductDataCache.value.operationName,
				availableLimit: commercialProductDataCache.value.availableLimit,
				dateDisbursement: operationDataCache.value.availableDateCession,
				valueSeleted: currencyUnmask(totalValueDisbursement.value),
				limit: commercialProductDataCache.value.availableLimit,
				productName: commercialProductDataCache.value.name,
				minValue: commercialProductDataCache.value.minValue,
				maxValue: commercialProductDataCache.value.maxValue,
			};
			addSelectedDataToCache(sendData);
			addDataCache(cache);
			redirectPageSuccess(preSectionId);
		}

		function onUpdatedLoadingReceivablesCard(value): void {
			isLoadingReceivablesCard.value = value;
		}

		function onClickClose(): void {
			openModalCloseCession();
		}

		function onOpenModalDetailsCession(): void {
			const { availableLimit, operationName, name } = commercialProductDataCache.value;
			const { availableDateCession } = operationDataCache.value;
			dataModalDetailCession.value = {
				dateDisbursement: availableDateCession,
				limit: availableLimit,
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				productName: name,
				typeOperation: operationName,
				freeValue: currencyUnmask(totalValueFree.value),
				nominalValue: currencyUnmask(totalNominalValue.value),
			};

			onOpenModalModalDetailCession();
		}

		function hasDataCache(): boolean {
			if (commercialProductDataCache.value === null && operationDataCache.value === null) {
				return false;
			}
			return true;
		}

		function updatedOriginators(hasError, originatorData) {
			if (hasError) {
				return;
			}
			showResultsLabel.value = false;
			if (isFilterCounter.value) {
				totalFilter.value = originatorData.totalFilter;
				showResultsLabel.value = true;
			}
			if (firstRender.value) {
				totalReceivablesProduct.value = originatorData.totalFilter;
				firstRender.value = false;
				getAutomaticSelection(
					{
						commercialProductId: commercialProductDataCache.value.idProductAccount,
						roof:
							operationDataCache.value?.valueCession ||
							commercialProductDataCache.value?.availableLimit,
						dtDisrbursement: operationDataCache.value.availableDateCession,
						idOperation:
							route.query.isBack === 'true' ? route.query.id_pre_section : null,
					},
					updatedAutomaticSelection
				);
			}
			valueSeleted.value =
				operationDataCache.value?.valueCession || currencyUnmask(headerData.value.limit);
		}

		function onInputChangeMainFilter(value: string): void {
			if (value.length > 3) {
				inputFilterActive.value = true;
				if (!filterCurrent.value.roof) {
					filterCurrent.value.roof =
						operationDataCache.value?.valueCession ||
						commercialProductDataCache.value?.valueCession;
				}
				filterCurrent.value = {
					...filterCurrent.value,
					search: value,
				};
				getOriginators({ ...filterCurrent.value, search: value }, updatedOriginators);
				return;
			}
			filterCurrent.value = {
				...filterCurrent.value,
				search: null,
			};
			getOriginators({ ...filterCurrent.value, search: null }, updatedOriginators);
			inputFilterActive.value = false;
			getOriginators(filterCurrent.value, updatedOriginators);
		}

		function checkValueMinIsNull(): number {
			if (commercialProductDataCache.value === null) {
				return 0;
			}
			return commercialProductDataCache.value.minValue;
		}

		function checkValueMaxIsNull(): number {
			if (commercialProductDataCache.value === null) {
				return 0;
			}
			return commercialProductDataCache.value.maxValue;
		}

		function updatedListOriginators(dataSelected): void {
			const newData = originators.value.map(item => {
				if (dataSelected[item.document]) {
					return {
						...item,
						countSeleted: dataSelected[item.document].length,
						selected: dataSelected[item.document].length === item.quantity,
					};
				}
				return {
					...item,
				};
			});
			originators.value = [...newData];
		}

		function updatedAutomaticSelection(hasError, data): void {
			if (hasError) {
				return;
			}
			isFeedbackNotEnoughPrecified.value = !data.allPrecified && data.anyPrecified;
			isFeedbackNotPrecified.value = !data.allPrecified && !data.anyPrecified;
			updatedReceivablesSelectedCache({ data: data.receivablesSelected });
			updatedListOriginators(data.receivablesSelected);
			// add flag e para remover a primeiro chamada
			setTimeout(() => {
				isRenderFirstValues.value = false;
			}, 500);
		}

		function updatedPage(hasError): void {
			if (hasError) {
				return;
			}
			const params = {
				page: 0,
				limit: 10,
				order: 'ASC',
				orderby: 'providerName',
				commercialProductId: commercialProductDataCache.value.idProductAccount,
				roof: operationDataCache.value?.valueCession,
				dtDisrbursement: operationDataCache.value?.availableDateCession,
				idOperation: route.query.isBack === 'true' ? route.query.id_pre_section : null,
			};
			if (firstRender.value) {
				filterCurrent.value = { ...params };
			}

			filterCurrent.value.commercialProductId =
				operationDataCache.value.commercialProductId ||
				commercialProductDataCache.value.idProductAccount ||
				commercialProductDataCache.value.productId;

			getOriginators(filterCurrent.value, updatedOriginators);
		}

		function updatedHeaders(): void {
			if (hasData()) {
				return;
			}

			const { availableLimit, operationName, name, idProductAccount } =
				commercialProductDataCache.value;
			const { availableDateCession } = operationDataCache.value;
			headerData.value = {
				dateDisbursement: defaultDateFormat(availableDateCession),
				limit: brl(availableLimit),
				valueDisbursement: 0,
				productName: name,
				typeOperation: operationName,
			};

			dataModalDetailCession.value = {
				dateDisbursement: availableDateCession,
				limit: availableLimit,
				valueDisbursement: 0,
				productName: name,
				typeOperation: operationName,
				freeValue: 0,
				nominalValue: 0,
			};
			dataValuesAndDates.value = {
				limit: availableLimit,
				minValue: commercialProductDataCache.value.minValue,
				maxValue: commercialProductDataCache.value.maxValue,
				quantity: commercialProductDataCache.value.quantity,
			};

			sendPricing(
				{
					commercialProductId: idProductAccount,
					disbursementDate: availableDateCession,
					productId: null,
				},
				updatedPage
			);
		}

		function callAPIs(): void {
			if (hasParams()) {
				updatedHeaders();
				hiddenButtonBack.value = true;
				return;
			}
			if (hasDataStorage()) {
				const data = JSON.parse(getStorage('juca'));
				addCommercialProductCache({
					availableLimit: data.availableLimit,
					dueDateLimit: data.availableDateCession,
					idProductAccount: data.idProductAccount,
					maxValue: data.maxValue,
					minValue: data.minValue,
					name: data.nameProductAccount,
					operationId: data.operationId,
					operationName: data.operationName,
					usedLimit: 0,
					productId: useSelectedProductId().value,
				});
				addOperationCache({
					valueCession: data.valueCession,
					availableDateCession: data.availableDateCession,
				});

				removeAll();
			}
			hiddenButtonBack.value = false;
			updatedHeaders();
		}

		function hasDataStorage(): boolean {
			return getStorage('juca') ? true : false;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			if (!hasParams() && !hasDataCache() && !hasDataStorage()) {
				redirectStepOne();
				return;
			}
			callAPIs();
		}

		onMounted(() => {
			filterCurrent.value.roof = commercialProductDataCache.value?.valueCession;
			load();
		});

		provide('isCollapsed', isCollapsed);
		provide('setCollapse', setCollapse);

		return {
			isLoadingSkeleton,
			isLoading,
			isLoadingReceivablesCard,
			isError,
			isFilterApplied,
			isFilterShowing,
			isDisabledButtonConfirm,
			isModalCloseCession,
			isModalDetailCession,
			isDisabledUncheck,
			isOpenFilter,
			isFilterCounter,
			isModalDateAndValue,
			isFeedbackNotEnoughPrecified,
			isFeedbackNotPrecified,
			isFeedbackLimit,
			isMinValue,
			isMaxValue,
			isFeedbackValue,
			isRenderFirstValues,
			isFeedbackReceivables,
			isModalDrawee,
			isModalLimitDrawee,

			inputFilterActive,
			originatorCurrent,
			dataValuesAndDates,
			totalReceivablesProduct,
			labelMaxValue,
			resetAllCheckbox,
			totalSeleted,
			firstRender,
			commercialProductDataCache,
			operationDataCache,
			dataModalDetailCession,
			headerData,
			totalValueDisbursement,
			totalValueFree,
			totalNominalValue,
			totalFilter,
			showResultsLabel,
			hiddenButtonBack,
			originators,
			filterCurrent,
			pagination,
			page,
			dataFeedbackValue,
			minValue,

			onCloseModalCloseCession,
			onConfirmModalCloseCession,
			onClickBack,
			onClickConfirm,
			onClickClose,
			onOpenModalDetailsCession,
			onCloseModalModalDetailCession,
			onClickEdit,
			isRoofLimit,
			onClickMainFilter,
			onInputChangeMainFilter,
			onUncheckReceivables,
			onFiltersApplied,
			onApplyFilter,
			onChangePage,
			onChangePageLimit,
			onReload,
			onCloseModalDateAndValue,
			onConfirmModalDateAndValue,
			onUpdatedLoadingReceivablesCard,
			onCloseModalDrawee,
			onOpenModalOriginator,
			onCloseModalLimitDrawee,
			onOpenModalLimitDraweeClicked,
		};
	},
});
</script>
