<template>
	<farm-card>
		<farm-card-content gutter="md">
			<div class="originator-card-header">
				<div class="originator-card-header-data">
					<farm-row>
						<farm-col cols="12" md="4">
							<div class="originator-card-content-checkbox">
								<div class="checkbox-content">
									<farm-checkbox
										v-model="checkboxValue"
										:value="true"
										:checked="data.selected"
										ref="checkboxElement"
									/>
									<farm-caption
										class="count mt-1"
										variation="regular"
										color="black"
										color-variation="30"
									>
										{{ countSeleted }}/{{ data.quantity }}
									</farm-caption>
								</div>
								<div class="title-content">
									<farm-caption
										variation="regular"
										color="black"
										color-variation="30"
									>
										Cedente
									</farm-caption>
									<farm-subtitle ellipsis variation="medium">
										{{ data.originatorName || 'N/A' }}
									</farm-subtitle>
								</div>
							</div>
						</farm-col>
						<farm-col cols="12" md="1">
							<div class="content-flex">
								<div>
									<farm-caption
										variation="regular"
										color="black"
										color-variation="30"
									>
										Raiz
										<farm-icon
											class="icon-hover"
											size="xs"
											color="primary"
											@click.prevent="onDocument"
										>
											open-in-new
										</farm-icon>
									</farm-caption>
									<farm-subtitle ellipsis variation="medium">
										{{ data.document }}
									</farm-subtitle>
								</div>
							</div>
						</farm-col>
						<farm-col cols="12" sm="6" md="2">
							<div class="content-flex">
								<div>
									<farm-caption
										variation="regular"
										color="black"
										color-variation="30"
									>
										Total de Recebíveis
									</farm-caption>
									<farm-subtitle ellipsis variation="medium">
										{{ data.quantity }}
									</farm-subtitle>
								</div>
							</div>
						</farm-col>
						<farm-col cols="12" sm="6" md="2">
							<div class="content-flex">
								<div>
									<farm-caption
										variation="regular"
										color="black"
										color-variation="30"
									>
										Valor Líquido Disponível
									</farm-caption>
									<farm-subtitle ellipsis variation="medium">
										{{ formatMoney(data.totalValues) }}
									</farm-subtitle>
								</div>
							</div>
						</farm-col>
						<farm-col cols="12" sm="6" md="3">
							<div class="content-flex">
								<div>
									<farm-btn plain @click="onClickDrawee">
										<farm-icon color="primary" size="24px"
											>eye-outline</farm-icon
										>
										Visualizar Limite dos Sacados
									</farm-btn>
								</div>
							</div>
						</farm-col>
					</farm-row>
				</div>
				<div class="originator-card-header-button">
					<farm-btn icon @click="onToggleDetails" class="originator-card-button">
						<farm-icon size="md">{{
							showDetails ? 'chevron-up' : 'chevron-down'
						}}</farm-icon>
					</farm-btn>
				</div>
			</div>
		</farm-card-content>
		<collapse-transition :duration="300" v-show="showDetails">
			<div>
				<farm-line noSpacing />
				<farm-card-content gutter="md" background="base">
					<receivables-list
						:document="data.document"
						:request="showDetails"
						:selectedAll="checkboxValue"
						:filtersCurrent="filters"
						@onUpdatedLoadingReceivablesCard="onUpdatedLoadingReceivablesCard"
					/>
				</farm-card-content>
			</div>
		</collapse-transition>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref, computed, watch, inject } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import { useReceivables } from '../../composables/useReceivables';
import { useReceivablesSelectedCache } from '../../composables/useReceivablesSelectedCache';
import ReceivablesList from '../ReceivablesList';


export default defineComponent({
	name: 'originator-card',
	components: {
		ReceivablesList,
	},
	props: {
		data: {
			type: Object,
			require: true,
		},
		filters: {
			type: Object,
			require: true,
		},
		resetAllCheckbox: {
			type: Boolean,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data, filters, resetAllCheckbox } = toRefs(props);
		const {
			receivablesSelectedDataCache,
			findReceivablesSelectedCache,
			addDataCacheAll,
			removeDataCacheAll,
		} = useReceivablesSelectedCache();
		const { getReceivables, isLoadingReceivablesBank } = useReceivables();

		const checkboxElement = ref(null);
		const checkboxDisabled = ref(false);
		const checkboxValue = ref(false);
		const showDetails = ref(false);
		const isUpdatingProgrammatically = ref(false);

		const isCollapsed = inject('isCollapsed');

		const countSeleted = computed(() => {
			if (receivablesSelectedDataCache.value === null) {
				return 0;
			}
			const hasDataCache = findReceivablesSelectedCache(data.value.document);
			if (hasDataCache) {
				return hasDataCache.length;
			}
			return 0;
		});

		function formatMoney(value: number): string {
			return brl(value || 0);
		}

		function onToggleDetails(): void {
			showDetails.value = !showDetails.value;
			
			if (showDetails.value && checkboxValue.value) {
				callAPIReceivables();
			}
		}

		function onDocument(): void {
			emit('onOpenModalOriginator', data.value);
		}
		function onClickDrawee(): void {
			emit('onOpenModalLimitDrawee', data.value);
		}

		function reset(): void {
			checkboxElement.value.reset();
		}

		function parseReceivableCache(data) {
			const newData = data.map(item => {
				return {
					id: item.id,
					value: item.valueLiquid,
					freeValue: item.valueFree,
					nominalValue: item.nominalValue,
					disabled: item.disabled,
				};
			});
			return newData;
		}

		function updatePage(hasError, dataReceivables) {
			if (hasError) {
				return;
			}
			addDataCacheAll({
				all: true,
				data: parseReceivableCache(dataReceivables),
				reset,
				document: data.value.document,
			});
		}

		function callAPIReceivables() {
			const params = {
				...filters.value,
				page: 0,
				limit: data.value.quantity,
				order: 'ASC',
				orderby: 'id',
			};
			getReceivables(
				{
					document: data.value.document,
					number: filters.value.search,
				},
				params,
				updatePage
			);
		}

		function onUpdatedLoadingReceivablesCard(data): void {
			emit('onUpdatedLoadingReceivablesCard', data);
		}

		watch(resetAllCheckbox, newValue => {
			if (newValue) {
				isUpdatingProgrammatically.value = true;
				data.value.selected = false;
				checkboxValue.value = false;
				reset();

				// Reset da flag após pequeno delay
				setTimeout(() => {
					isUpdatingProgrammatically.value = false;
				}, 10);
			}
		});

		// Sincroniza data.selected com checkboxValue de forma programática
		watch(() => data.value.selected, (newValue) => {
			isUpdatingProgrammatically.value = true;
			checkboxValue.value = newValue;
			
			// Reset da flag após pequeno delay
			setTimeout(() => {
				isUpdatingProgrammatically.value = false;
			}, 10);
		});


		watch(checkboxValue, newValue => {
			// Evita ações automáticas durante atualizações programáticas
			if (isUpdatingProgrammatically.value) {
				return;
			}
			if (newValue) {
				data.value.selected = true;
				return;
			}
			removeDataCacheAll({
				all: true,
				data: data.value,
			});
		});

		watch(countSeleted, newValue => {
			isUpdatingProgrammatically.value = true;
			if (newValue === data.value.quantity) {
				data.value.selected = true;
				checkboxValue.value = true;
			}
			if (newValue !== data.value.quantity ) {
				data.value.selected = false;
			}
			setTimeout(() => {
				isUpdatingProgrammatically.value = false;
			}, 10);
		});

		watch(
			() => isLoadingReceivablesBank.value,
			newValue => {
				emit('onUpdatedLoadingReceivablesCard', newValue);
			}
		);

		watch(isCollapsed, newValue => {
			if (newValue) {
				showDetails.value = false;
			}
		});

		return {
			isCollapsed,
			showDetails,
			data,
			checkboxDisabled,
			checkboxValue,
			checkboxElement,
			formatMoney,
			countSeleted,
			onToggleDetails,
			onDocument,
			onClickDrawee,
			onUpdatedLoadingReceivablesCard,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './OriginatorCard';
</style>
