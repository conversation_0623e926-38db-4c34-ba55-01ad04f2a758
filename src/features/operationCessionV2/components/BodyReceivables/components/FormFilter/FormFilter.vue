<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-raiz"> Raiz do Cedente </farm-label>
					<farm-textfield-v2 id="form-filter-raiz" v-model="document" />
					<div class="mt-2">
						<farm-label for="form-filter-switch3"> Exibir Apenas Recebíveis Não Precificados? </farm-label>
						<div class="d-flex">
							<farm-switcher
								class="mt-1"
								id="form-filter-switch3"
								v-model="nonPricedReceivables"
								block
							></farm-switcher>
							<farm-bodytext
								class="ml-3 mt-1"
								color="neutral"
								colorVariation="darken"
								variation="regular"
								:type="2"
							>
								{{ nonPricedReceivables ? 'Sim': 'Não' }}
							</farm-bodytext>
						</div>
					</div>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-emissionDate">
						Data de Emissão (Início/Fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerEmissionDate"
						inputId="form-filter-emissionDate"
						v-model="emissionDateRange"
						@input="onInputEmissionDateRange"
					/>
					<div class="mt-2">
						<farm-label for="form-filter-switch1"> Exibir Apenas Sacados Sem Limite? </farm-label>
						<div class="d-flex">
							<farm-switcher
								class="mt-1"
								id="form-filter-switch1"
								v-model="noLimitDrawees"
								block
							></farm-switcher>
							<farm-bodytext
								class="ml-3 mt-1"
								color="neutral"
								colorVariation="darken"
								variation="regular"
								:type="2"
							>
								{{ noLimitDrawees ? 'Sim': 'Não' }}
							</farm-bodytext>
						</div>
					</div>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-expiredAtRange">
						Data de Vencimento (Início/Fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerExpirationDate"
						inputId="form-filter-expiredAtRange"
						v-model="expiredAtRange"
						@input="onInputExpiredDateRange"
					/>
					<div class="mt-2">
						<farm-label for="form-filter-switch2"> Exibir Apenas Sacados Sem Contato? </farm-label>
						<div class="d-flex">
							<farm-switcher
								class="mt-1"
								id="form-filter-switch2"
								v-model="noContactDrawees"
								block
							></farm-switcher>
							<farm-bodytext
								class="ml-3 mt-1"
								color="neutral"
								colorVariation="darken"
								variation="regular"
								:type="2"
							>
								{{ noContactDrawees ? 'Sim': 'Não' }}
							</farm-bodytext>
						</div>
					</div>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Valor Nominal </farm-label>
					<farm-select
						id="form-filter-status"
						v-model="nominalValue"
						:items="nominalItems"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
			</farm-row>
			<farm-row class="mt-6">
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="onFilterConfirm"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, inject } from 'vue';
import { nextTick } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';

import { nominalValueData } from './configurations';

export default defineComponent({
	name: 'form-filter',
	props: {
		filterCurrent: {
			type: Object,
			require: true,
		},
	},
	setup(props, { emit }) {
		const { filterCurrent } = toRefs(props);

		const { trigger } = useAnalytics();

		const document = ref('');
		const emissionDateRange = ref([]);
		const expiredAtRange = ref([]);
		const nominalValue = ref('');
		const nominalItems = ref(nominalValueData);
		const noLimitDrawees = ref(false);
		const noContactDrawees = ref(false);
		const nonPricedReceivables = ref(false);

		const setCollapse = inject('setCollapse');

		const filters = ref({
			document: document.value,
			expirationAtStart: null,
			expirationAtEnd: null,
			createdAtStart: null,
			createdAtEnd: null,
			nominalValueAtStart: null,
			nominalValueAtEnd: null,
			noLimitDrawees: false,
			noContactDrawees: false,
			nonPricedReceivables: false
		});

		function forceCollapseUpdate() {
			if (setCollapse) {
				setCollapse(false);
				nextTick(() => {
					setCollapse(true);
				});
			}
		}

		function onFilterClear(): void {
			forceCollapseUpdate();
			filters.value = {
				document: null,
				expirationAtStart: null,
				expirationAtEnd: null,
				createdAtStart: null,
				createdAtEnd: null,
				nominalValueAtStart: null,
				nominalValueAtEnd: null,
				noLimitDrawees: false,
				noContactDrawees: false,
				nonPricedReceivables: false
			};
			document.value = null;
			nominalValue.value = null;
			expiredAtRange.value = [];
			emissionDateRange.value = [];
			noLimitDrawees.value = false;
			noContactDrawees.value = false;
			nonPricedReceivables.value = false;
			const dataTrigger = {
				event: 'receivables_bank',
				payload: {
					description: 'clicou na opção de limpar filtro',
				},
			};
			trigger(dataTrigger);
			emit('onApply', {
				...filters.value,
				commercialProductId: filterCurrent.value.commercialProductId,
				dtDisrbursement: filterCurrent.value.dtDisrbursement,
				roof: filterCurrent.value.roof,
				idOperation: filterCurrent.value.idOperation,
			});
			emit('onFiltersApplied', false);
		}

		function onFilterConfirm(): void {
			forceCollapseUpdate();

			if (expiredAtRange.value?.length > 0) {
				filters.value.expirationAtStart = expiredAtRange.value[0];
				filters.value.expirationAtEnd = expiredAtRange.value[1];
			}
			if (emissionDateRange.value?.length > 0) {
				filters.value.createdAtStart = emissionDateRange.value[0];
				filters.value.createdAtEnd = emissionDateRange.value[1];
			}
			const dataTrigger = {
				event: 'receivables_bank',
				payload: {
					description: 'clicou na opção de aplicar filtro',
					filters: {
						document: document.value || 'N/A',
						dateExpired:
							(filters.value.expirationAtStart || 'N/A') +
							' até ' +
							(filters.value.expirationAtEnd || 'N/A'),
						issueDate:
							(filters.value.createdAtStart || 'N/A') +
							' até ' +
							(filters.value.createdAtEnd || 'N/A'),
						nominalValue: nominalValue.value || 'N/A',
						noLimitDrawees: noLimitDrawees.value ? "Sim" : "Não",
						noContactDrawees: noContactDrawees.value ? "Sim" : "Não",
						nonPricedReceivables: nonPricedReceivables.value ? "Sim" : "Não"
					},
				},
			};
			trigger(dataTrigger);

			if (nominalValue.value?.length > 0) {
				filters.value.nominalValueAtStart = nominalValue.value[0];
				filters.value.nominalValueAtEnd = nominalValue.value[1];
			}
			const payload = {
				...filters.value,
				document: document.value || null,
				commercialProductId: filterCurrent.value.commercialProductId,
				dtDisrbursement: filterCurrent.value.dtDisrbursement,
				roof: filterCurrent.value.roof,
				idOperation: filterCurrent.value.idOperation,
				noLimitDrawees: noLimitDrawees.value,
				noContactDrawees: noContactDrawees.value,
				nonPricedReceivables: nonPricedReceivables.value
			};

			emit('onApply', payload);
			emit('onFiltersApplied', true);
		}

		function getValueDates(value: Array<string>): Array<string> {
			const startDate = new Date(value[0]);
			const endDate = new Date(value[1]);
			if (startDate > endDate) {
				return [value[1], value[0]];
			}
			return value;
		}

		function onInputExpiredDateRange(value: Array<string>): void {
			expiredAtRange.value = value.length === 2 ? getValueDates(value) : value;
		}

		function onInputEmissionDateRange(value: Array<string>): void {
			emissionDateRange.value = value.length === 2 ? getValueDates(value) : value;
		}

		return {
			document,
			emissionDateRange,
			expiredAtRange,
			nominalValue,
			nominalItems,
			noLimitDrawees,
			noContactDrawees,
			nonPricedReceivables,
			onFilterClear,
			onFilterConfirm,
			onInputEmissionDateRange,
			onInputExpiredDateRange,
		};
	},
});
</script>
