<template>
	<farm-box>
		<div v-if="!isError">
			<div v-for="(item, index) in dataList" :key="item.key">
				<receivables-card
					:data="item"
					:lastItem="index === dataList.length - 1"
					:document="document"
					:filtersCurrent="filtersCurrent"
				/>
			</div>
			<farm-row extra-decrease>
				<farm-box>
					<farm-datatable-paginator
						v-if="dataList.length > 0"
						class="mt-6 mb-n6"
						:page="page"
						:totalPages="pagination.totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangePageLimit"
					/>
				</farm-box>
			</farm-row>
			<farm-row extra-decrease v-if="!isError && !isLoading && dataList.length === 0">
				<farm-box>
					<farm-emptywrapper subtitle="" />
				</farm-box>
			</farm-row>
		</div>
		<div
			v-if="!isError && isLoading && loadingInline === ''"
			:class="{
				'my-10 d-flex justify-center': loadingInline === '',
			}"
		>
			<farm-loader :mode="loadingInline" />
		</div>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref, watch, computed } from 'vue';

import { usePageable } from '@/composible/usePageable';

import { useReceivables } from '../../composables/useReceivables';
import { useReceivablesSelectedCache } from '../../composables/useReceivablesSelectedCache';
import ReceivablesCard from '../ReceivablesCard';

export default defineComponent({
	name: 'receivables-list',
	components: {
		ReceivablesCard,
	},
	props: {
		document: {
			type: String,
			require: true,
		},
		number: {
			type: Number,
			require: true,
		},
		request: {
			type: Boolean,
			default: false,
		},
		selectedAll: {
			type: Boolean,
			default: false,
		},
		filtersCurrent: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { document, request, selectedAll, filtersCurrent } = toRefs(props);
		const { getReceivables, receivablesPagination, receivables } = useReceivables();
		const { findReceivablesSelectedCache } = useReceivablesSelectedCache();

		const { page, pagination } = usePageable(
			{
				calbackFn: params => {
					if (filtersCurrent.value.search) {
						params = params + `&number=${filtersCurrent.value.search}`;
					}

					getReceivables(
						{
							document: document.value,
							number: filtersCurrent.value.search,
						},
						params,
						updatePage
					);
					//cleanCache();
				},
				filters: {},
				keyInputSearch: 'id_pre_section',
				sort: {
					order: 'ASC',
					orderby: 'id',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			receivablesPagination
		);

		//const { cleanCache } = useReceivablesSelectedCache();

		const loadingInline = ref('');
		const isError = ref(false);
		const isLoading = ref(true);
		const firstRender = ref(true);
		const dataList = ref([]);

		const checkAll = computed(() => {
			return selectedAll.value;
		});
		const filterParent = computed(() => {
			return filtersCurrent.value;
		});

		function onUpdatedLoadingReceivablesCard(data): void {
			emit('onUpdatedLoadingReceivablesCard', data);
		}

		function onChangePage(newPage: number): void {
			const params = {
				...filterParent.value,
				page: newPage - 1,
				limit: pagination.value.pageSize,
				order: 'ASC',
				orderby: 'id',
				number: filtersCurrent.value.search,
			};
			loadingInline.value = 'overlay';
			onUpdatedLoadingReceivablesCard(true);
			getReceivables(
				{
					document: document.value,
				},
				params,
				updatePage
			);
			//cleanCache();
		}

		function onChangePageLimit(newPageLimit: number): void {
			const params = {
				...filterParent.value,
				page: 0,
				limit: newPageLimit,
				order: 'ASC',
				orderby: 'id',
				number: filtersCurrent.value.search,
			};

			loadingInline.value = 'overlay';
			onUpdatedLoadingReceivablesCard(true);
			getReceivables(
				{
					document: document.value,
				},
				params,
				updatePage
			);
			//cleanCache();
		}

		function updatedCheckbox(data, all) {
			const updatedData = data.map(item => {
				return {
					...item,
					selected: all,
				};
			});
			return updatedData;
		}

		function selectedCheckbox(data) {
			
			const receivablesCache = findReceivablesSelectedCache(document.value);
			const dataCache = receivablesCache || [];
			const newData = data.map(item => {
				const hasDocument = dataCache.filter(s => {
					return s.id === item.id;
				});
				return {
					...item,
					selected: hasDocument.length > 0,
				};
			});
			return newData;
		}

		function updatePage(hasError, data): void {
			if (hasError) {
				onUpdatedLoadingReceivablesCard(false);
				isError.value = true;
				return;
			}
			isError.value = false;
			isLoading.value = false;
			onUpdatedLoadingReceivablesCard(false);
			const newData = selectedCheckbox(data);
			dataList.value = [...newData];
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			isLoading.value = true;
			loadingInline.value = '';
			dataList.value = [];

			const params = {
				...filterParent.value,
				page: 0,
				limit: 10,
				order: 'ASC',
				orderby: 'id',
				number: filtersCurrent.value.search,
			};

			getReceivables(
				{
					document: document.value,
				},
				params,
				updatePage
			);
			//cleanCache();
		}

		watch(request, newValue => {
			if (newValue) {
				load();
			}
		});

		watch(checkAll, newValue => {
			dataList.value = [...updatedCheckbox(dataList.value, newValue)];
		});


		return {
			isError,
			isLoading,
			receivables,
			document,
			loadingInline,
			dataList,
			firstRender,
			selectedAll,
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onReload,
		};
	},
});
</script>
