<template>
	<GeneralInfoModal
		v-model="value"
		:data="receivablesCurrent"
		@onClose="onClose"
	/>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import GeneralInfoModal from '@/components/GeneralInfoModal/GeneralInfoModal.vue';

export default defineComponent({
	name: 'modal-detail-receivables',
	components: {
		GeneralInfoModal,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		receivablesCurrent: {
			type: Object,
			required: true,
		},
	},
	emits: ['update:value', 'onClose'],
	setup(props, { emit }) {
		function onClose() {
			emit('onClose');
		}
		return {
			onClose,
		};
	},
});
</script>

<style lang="scss" scoped>
.darken {
	color: #5c5c5c;
}

.custom-clipboard-wrapper {
	position: relative;
}

.custom-clipboard-style {
	position: absolute;
	transform: translateY(-50%) translateX(-0.5em);
}
</style>
