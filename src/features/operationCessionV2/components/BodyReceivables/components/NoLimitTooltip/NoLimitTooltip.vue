<template>
	<farm-tooltip position="top-right" fluid>
		<template #title>
			<div>
				<farm-icon class="icon-title" size="sm" color="white">custom-money-off</farm-icon>
				<strong> Sacado sem Limite Disponível</strong>
			</div>
		</template>
		<span>
			Este sacado não possui limite disponível para<br />
			realizar uma cessão.
		</span>
		<template #activator>
			<span class="icon-container">
				<farm-icon size="md" color="error">custom-money-off</farm-icon>
			</span>
		</template>
	</farm-tooltip>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'no-limit-tooltip',
});
</script>

<style lang="scss" scoped>
.icon-title {
	transform: scale(1.2);
}

.icon-container {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	border-radius: 100%;
	background-color: #ffebee;
	cursor: pointer;
}
</style>
