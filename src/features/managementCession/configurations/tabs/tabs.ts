import {
	<PERSON><PERSON><PERSON>,
	RECEIVABLES_HISTORY,
	REP<PERSON>CESS_RECEIVABLES,
} from '@/features/managementCession/constants';
import { TabTypes } from '@/types';

export const tabsManagementCession: Array<TabTypes> = [
	{
		name: 'CANC<PERSON><PERSON> OPERAÇÃO',
		path: CANCEL,
	},
	{
		name: 'HISTÓRICO DE RECEBÍVEIS',
		path: RECEIVABLES_HISTORY,
	},
	{
		name: 'REPROCE<PERSON><PERSON> RECEBÍVEIS',
		path: REPROCESS_RECEIVABLES,
	},
];
