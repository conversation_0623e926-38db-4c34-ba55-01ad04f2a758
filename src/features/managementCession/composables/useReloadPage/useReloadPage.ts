import { watch, computed } from 'vue';

import { useGetter, useStore } from '@/composible';

type UseReloadPage = {
	statusReload: boolean;
	reset: Function;
	updatedReload: Function;
}

export function useReloadPage(): UseReloadPage {
	const store = useStore();

	const statusReload = computed(
		useGetter('managementCession', 'reloadPage')
	);

	function reset(): void {
		updatedReload(false);
	}

	function updatedReload(status: boolean): void {
		store.dispatch('managementCession/updatedReloadPage', { data: status });
	}

	return {
		statusReload,
		reset,
		updatedReload
	};
}
