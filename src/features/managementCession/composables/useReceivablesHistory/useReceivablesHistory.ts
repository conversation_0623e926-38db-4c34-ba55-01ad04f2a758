import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';
import { ComputedRef, Ref } from 'vue/types';

import { builderReceivablesHistory } from '@/features/managementCession/helpers/builderReceivablesHistory';
import { getReceivablesHistory as getReceivablesHistoryService } from '@/features/managementCession/services';

type UseReceivablesHistory = {
	receivablesHistoryPagination: Ref<any>;
	receivablesHistory: Ref<Array<any>>;
	isLoadingReceivablesHistory: ComputedRef<boolean>;
	isErrorReceivablesHistory: ComputedRef<boolean>;
	getReceivablesHistory: (filters: any) => void;
	isSuccessReceivablesHistory: ComputedRef<boolean>;
};

export function useReceivablesHistory(): UseReceivablesHistory {
	const receivablesHistory = ref([]);
	const receivablesHistoryPagination = ref(null);

	const { isLoading, isError, mutate, isSuccess } = useMutation({
		mutationFn: (params: any) => getReceivablesHistoryService(params),
		onSuccess: response => {
			const { content, pagination } = builderReceivablesHistory(response.data.data);
			receivablesHistory.value = content;
			receivablesHistoryPagination.value = pagination;
		},
	});

	const isLoadingReceivablesHistory = computed(() => isLoading.value);

	const isErrorReceivablesHistory = computed(() => isError.value);

	const isSuccessReceivablesHistory = computed(() => isSuccess.value);

	function getReceivablesHistory(filters): void {
		const params = queryString(filters, {});
		mutate({ filters: params });
	}

	return {
		receivablesHistoryPagination,
		receivablesHistory,
		isLoadingReceivablesHistory,
		isErrorReceivablesHistory,
		getReceivablesHistory,
		isSuccessReceivablesHistory,
	};
}
