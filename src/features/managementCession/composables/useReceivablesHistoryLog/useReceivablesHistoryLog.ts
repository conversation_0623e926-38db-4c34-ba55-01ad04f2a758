import { computed, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';
import { ComputedRef, Ref } from 'vue/types';

import { builderReceivablesHistoryLog } from '@/features/managementCession/helpers/builderReceivablesHistoryLog';
import { getReceivablesHistoryLog as getReceivablesHistoryLogService } from '@/features/managementCession/services';

type UseReceivablesHistoryLog = {
	receivablesHistoryLog: Ref<Array<any>>;
	isLoadingReceivablesHistoryLog: ComputedRef<boolean>;
	isErrorReceivablesHistoryLog: ComputedRef<boolean>;
	getReceivablesHistoryLog: (filters: any) => void;
	isSuccessReceivablesHistoryLog: ComputedRef<boolean>;
};

export function useReceivablesHistoryLog(): UseReceivablesHistoryLog {
	const receivablesHistoryLog = ref([]);

	const { isLoading, isError, mutate, isSuccess } = useMutation({
		mutationFn: ({ productId }: { productId: number }) =>
			getReceivablesHistoryLogService(productId),
		onSuccess: ({ data }) => {
			const content = builderReceivablesHistoryLog(data.data.content);
			receivablesHistoryLog.value = content;
		},
	});

	const isLoadingReceivablesHistoryLog = computed(() => isLoading.value);

	const isErrorReceivablesHistoryLog = computed(() => isError.value);

	const isSuccessReceivablesHistoryLog = computed(() => isSuccess.value);

	function getReceivablesHistoryLog(productId: number): void {
		mutate({ productId });
	}

	return {
		receivablesHistoryLog,
		isLoadingReceivablesHistoryLog,
		isErrorReceivablesHistoryLog,
		getReceivablesHistoryLog,
		isSuccessReceivablesHistoryLog,
	};
}
