import { ref, computed } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { getReceivablesHistoryCardDetails } from '../../services';

export function useHistoryCardDetails() {
	const cardDetails = ref([]);
	const cardDetailsPagination = ref(null);

	const { isLoading, isError, mutate, isSuccess } = useMutation({
		mutationFn: ({ productId, providerDocument, params }) =>
			getReceivablesHistoryCardDetails({ productId, providerDocument, params }),
		onSuccess: ({ data }) => {
			cardDetails.value = data.data.content;
			cardDetailsPagination.value = data.data.pageable;
		},
	});

	function getHistoryCardDetails({ productId, providerDocument, params }) {
		mutate({ productId, providerDocument, params });
	}

	const cardDetailsIsLoading = computed(() => {
		return isLoading.value;
	});

	const cardDetailsIsError = computed(() => {
		return isError.value;
	});

	const cardDetailsIsSuccess = computed(() => {
		return isSuccess.value;
	});

	return {
		getHistoryCardDetails,
		cardDetailsIsLoading,
		cardDetailsIsError,
		cardDetails,
		cardDetailsPagination,
		cardDetailsIsSuccess,
	};
}
