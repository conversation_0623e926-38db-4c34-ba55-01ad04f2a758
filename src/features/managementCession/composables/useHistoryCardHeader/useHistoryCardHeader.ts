import { computed, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { useRoute } from '@/composible';

import { builderReceivablesHistoryCardHeader } from '../../helpers/builderReceivablesHistoryCardHeader';
import { getReceivablesHistoryCardHeader } from '../../services';

export function useHistoryCardHeader() {
	const route = useRoute();
	const productId = route.params.productId;

	const historyHeaders = ref([]);
	const historyPagination = ref(null);
	const shouldFilter = ref(false);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params =>
			getReceivablesHistoryCardHeader({
				productId,
				params,
				shouldFilter: shouldFilter.value,
			}),
		onSuccess: ({ data }) => {
			const { content, pagination } = builderReceivablesHistoryCardHeader(data.data);
			historyHeaders.value = content;
			historyPagination.value = pagination;
		},
	});

	function getHistoryCardHeaders({ params }) {
		mutate(params);
	}

	const cardHeadersIsLoading = computed(() => {
		return isLoading.value;
	});

	const cardHeadersIsError = computed(() => {
		return isError.value;
	});

	return {
		getHistoryCardHeaders,
		cardHeadersIsLoading,
		cardHeadersIsError,
		historyHeaders,
		historyPagination,
	};
}
