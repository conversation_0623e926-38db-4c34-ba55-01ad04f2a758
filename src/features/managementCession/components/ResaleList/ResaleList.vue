<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters >
			<farm-col cols="12" v-for="item in data" :key="item.id">
				<farm-collapsible
					custom
					title=""
					@open="onOpen"
				>
					<template #custom>
						<header-collapsible :cession="item.cessions.length" :resale="item.name" />
					</template>
					<cessionList :data="item.cessions" />
				</farm-collapsible>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';

import CessionList from '../CessionList';
import HeaderCollapsible from '../HeaderCollapsible';

export default defineComponent({
	name:"resale-list",
	components:{
		CessionList,
		HeaderCollapsible
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props) {
		const isOpen = ref(false);
		const isDataEmpty = computed(() => props.data.length === 0);

		function onOpen(): void {
			isOpen.value = !isOpen.value;
		}

		return {
			isOpen,
			isDataEmpty,
			onOpen
		};
	},
});

</script>
