import { computed, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { builderResale } from '@/features/managementCession/helpers/builderResale';
import { getResale as getResaleService } from '@/features/managementCession/services';

type UseResale = {
	resale: Array<any>;
	isLoadingResale: {
		value: boolean
	};
	isErrorResale: {
		value: boolean
	};
	getResale: Function;
};

export function useResale(): UseResale {
	const resale = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getResaleService(),
		onSuccess: response => {
			resale.value = builderResale(response.data);
		},
	});

	const isLoadingResale = computed(() => {
		return isLoading.value;
	});

	const isErrorResale = computed(() => {
		return isError.value;
	});

	function getResale(): void {
		mutate();
	}

	return {
		resale,
		isLoadingResale,
		isErrorResale,
		getResale,
	};
}
