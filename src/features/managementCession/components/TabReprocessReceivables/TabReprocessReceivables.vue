<template>
	<div>
		<farm-row justify="space-between">
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="cached" copyText="">
					<template v-slot:title>
						<farm-heading :type="6"> Reprocessar Recebíveis </farm-heading>
					</template>
					<template v-slot:subtitle>
						Selecione o Parceiro para visualizar os recebíveis aptos ao reprocessamento.
					</template>
				</farm-idcaption>
			</farm-col>
		</farm-row>

		<farm-row justify="space-between mt-2 ">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Parceiro"
					:hasExtraFilters="false"
					@onInputChange="inputChangeMainFilter"
				>
					<div class="d-flex">
						<farm-label class="mb-0 mr-2">
							Buscar Parceiro
							<farm-tooltip fluid position="top-center">
								<template #title> Buscar Parceiro </template>
								Realize sua busca pelo Nome ou<br />ID do Parceiro.
								<template #activator>
									<farm-icon size="sm" class="ml-n1" color="gray"
										>help-circle</farm-icon
									>
								</template>
							</farm-tooltip>
						</farm-label>
					</div>
				</farm-form-mainfilter>
			</farm-col>

			<farm-col cols="12" md="2" align="right">
				<farm-select
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="selectedSort"
					:items="sortOptions"
					@change="changeSort"
				/>
			</farm-col>
		</farm-row>

		<farm-row>
			<farm-col
				cols="12"
				md="4"
				v-for="item in receivableHistoryList"
				:key="item.id"
				class="mb-4"
			>
				<ReprocessReceivablesCard :card-data="item" />
			</farm-col>
		</farm-row>

		<farm-row extra-decrease v-if="!receivableHistoryError && receivableHistoryList.length > 0">
			<farm-box>
				<farm-datatable-paginator
					:initialLimitPerPage="itemsPerPage"
					:page="currentPage"
					:perPageOptions="perPageOptions"
					:totalPages="totalPages"
					@onChangePage="changePage"
					@onChangeLimitPerPage="changePageLimit"
				/>
			</farm-box>
		</farm-row>
		<farm-row
			justify="center"
			v-if="receivableHistoryList.length === 0 && !isReceivableHistoryLoading"
		>
			<FilterEmptyState :subtitle="emptyStateMessage" class="my-10" />
		</farm-row>

		<farm-loader mode="overlay" v-if="isReceivableHistoryLoading" />
	</div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import FilterEmptyState from '@/components/FilterEmptyState';
import { useFetchReprocessableInvoices } from '@/features/_composables';

import { ReprocessableInvoicesParams } from '../../services';

import ReprocessReceivablesCard from './components/ReprocessReceivablesCard.vue';

type SortOrder = ReprocessableInvoicesParams['order'];

type SortOption = {
	label: string;
	value: 'date_desc' | 'date_asc' | 'name_asc' | 'name_desc';
	order: SortOrder;
	orderby: 'name' | 'lastSentAt';
};

export default defineComponent({
	name: 'tab-reprocess-receivables',
	components: {
		ReprocessReceivablesCard,
		FilterEmptyState,
	},
	setup() {
		const {
			data: receivablesHistoryData,
			error: receivableHistoryError,
			state: receivableHistoryState,
			fetchReprocessableInvoices,
		} = useFetchReprocessableInvoices();
		const receivablesHistorySort: SortOption[] = [
			{
				label: 'Envio Mais Recente - Menos Recente',
				value: 'date_desc',
				order: 'DESC',
				orderby: 'lastSentAt',
			},
			{
				label: 'Envio Menos Recente - Mais Recente',
				value: 'date_asc',
				order: 'ASC',
				orderby: 'lastSentAt',
			},
			{
				label: 'Alfabética A-Z',
				value: 'name_asc',
				order: 'ASC',
				orderby: 'name',
			},
			{
				label: 'Alfabética Z-A',
				value: 'name_desc',
				order: 'DESC',
				orderby: 'name',
			},
		];

		const selectedSort = ref<string>('date_desc');
		const sortOptions = ref(receivablesHistorySort);
		const itemsPerPage = ref(12);
		const currentPage = ref(1);
		const totalPages = ref(1);
		const perPageOptions = ref([6, 12, 24, 60, 120]);
		const emptyStateMessage = ref('Nenhum recebível encontrado');

		const filters = ref<ReprocessableInvoicesParams>({
			limit: 12,
			search: '',
			order: 'DESC',
			orderby: 'lastSentAt',
			page: 0,
		});
		const receivableHistoryList = ref([]);

		const isReceivableHistoryLoading = computed(
			() => receivableHistoryState.value === 'LOADING'
		);

		computed(() => {
			const selectedOption = receivablesHistorySort.find(
				option => option.value === selectedSort.value
			);
			if (selectedOption) {
				filters.value.order = selectedOption.order;
				filters.value.orderby = selectedOption.orderby;
			}
		});

		function changeSort(value: string): void {
			selectedSort.value = value;

			const selectedOption = receivablesHistorySort.find(option => option.value === value);

			if (selectedOption) {
				filters.value.order = selectedOption.order;
				filters.value.orderby = selectedOption.orderby;
			}

			loadReprocessableInvoices();
		}

		function inputChangeMainFilter(value: string): void {
			filters.value.page = 0;
			filters.value.search = value;
			currentPage.value = 1;
			loadReprocessableInvoices();
		}

		function changePage(page: number): void {
			filters.value.page = page - 1;
			currentPage.value = page;
			loadReprocessableInvoices();
		}

		function changePageLimit(limit: number): void {
			filters.value.page = 0;
			filters.value.limit = limit;
			currentPage.value = 1;
			loadReprocessableInvoices();
		}

		async function loadReprocessableInvoices() {
			await fetchReprocessableInvoices({ filters: filters.value });

			if (receivableHistoryError.value) {
				receivableHistoryList.value = [];
				totalPages.value = 0;
				emptyStateMessage.value = 'Tente filtrar novamente sua pesquisa.';
				return;
			}

			receivableHistoryList.value = receivablesHistoryData.value.content;
			totalPages.value = receivablesHistoryData.value.totalPages;
		}

		async function reloadReceivableHistory() {
			loadReprocessableInvoices();
		}

		onMounted(async () => {
			loadReprocessableInvoices();
		});

		return {
			receivablesHistorySort,
			sortOptions,
			selectedSort,
			receivableHistoryList,
			receivableHistoryError,
			isReceivableHistoryLoading,
			currentPage,
			perPageOptions,
			totalPages,
			itemsPerPage,
			emptyStateMessage,
			changePage,
			changePageLimit,
			reloadReceivableHistory,
			inputChangeMainFilter,
			changeSort,
		};
	},
});
</script>
