import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { deleteCession as deleteCessionService } from '@/features/managementCession/services';

type UseCancelCession = {
	isLoadingDeleteCession: {
		value: boolean;
	};
	isErrorDeleteCession: boolean;
	deleteCession: Function;
};

export function useCancelCession(): UseCancelCession {
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => deleteCessionService(params),
		onSuccess: () => {
			createNotification();
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
		onError: () => {
			createNotificationError();
		},
	});

	function createNotification(): void {
		notification(RequestStatusEnum.SUCCESS, `Operação cancelada com sucesso!`);
	}

	function createNotificationError(): void {
		notification(RequestStatusEnum.ERROR, `Erro ao cancelar a Operação`);
	}

	const isLoadingDeleteCession = computed(() => {
		return isLoading.value;
	});

	const isErrorDeleteCession = computed(() => {
		return isError.value;
	});

	function deleteCession({ id, operationTypeId }, callback: Function): void {
		mutate({ id, operationTypeId });
		callFunc = callback;
	}

	return {
		isLoadingDeleteCession,
		isErrorDeleteCession,
		deleteCession,
	};
}
