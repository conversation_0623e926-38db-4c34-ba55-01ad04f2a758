import { computed, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { getDateAndHour as getDateAndHourService } from '@/features/managementCession/services';

type UseGetDateAndHour = {
	isLoadingDateAndHour: {
		value: boolean
	};
	isErrorDateAndHour: {
		value: boolean
	};
	getDateAndHour: Function;
};

export function useGetDateAndHour(): UseGetDateAndHour {
	let callFunc: Function | null = null;
	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getDateAndHourService(),
		onSuccess: response => {
			if(callFunc !== null){
				setTimeout(() => {
					callFunc(response.data, false);
				}, 1500);
			}
		},
		onError: () => {
			callFunc(null, true);
		},
	});

	const isLoadingDateAndHour = computed(() => {
		return isLoading.value;
	});

	const isErrorDateAndHour = computed(() => {
		return isError.value;
	});

	function getDateAndHour(callback: Function): void {
		mutate();
		callFunc = callback;
	}

	return {
		isLoadingDateAndHour,
		isErrorDateAndHour,
		getDateAndHour,
	};
}
