import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { editDateAndHour as editDateAndHourService } from '@/features/managementCession/services';

type UseEditDateAndHour = {
	isLoadingEditDateAndHour: {
		value: boolean
	};
	isErrorEditDateAndHour: boolean;
	editDateAndHour: Function;
};

export function useEditDateAndHour(): UseEditDateAndHour {
	let callFunc: Function | null = null;
	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => editDateAndHourService(params),
		onSuccess: () => {
			createNotification();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 2000);
			}
		},
		onError:() => {
			createNotificationError();
		}
	});

	function createNotification(): void {
		notification(
			RequestStatusEnum.SUCCESS,
			`Informações atualizadas com sucesso.`
		);
	}

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			`Erro ao atualizar as informações.`
		);
	}

	const isLoadingEditDateAndHour = computed(() => {
		return isLoading.value;
	});

	const isErrorEditDateAndHour = computed(() => {
		return isError.value;
	});

	function editDateAndHour(payload, callback: Function): void {
		mutate({ payload });
		callFunc = callback;
	}

	return {
		isLoadingEditDateAndHour,
		isErrorEditDateAndHour,
		editDateAndHour,
	};
}
