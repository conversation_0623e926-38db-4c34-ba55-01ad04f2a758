<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" yGridGutters>
			<farm-col cols="12" md="4" v-for="item in data" :key="item.id">
				<receivables-history-card :data="item" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import ReceivablesHistoryCard from '../ReceivablesHistoryCard';

export default defineComponent({
	name: 'receivables-history-list',
	components: {
		ReceivablesHistoryCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isDataEmpty(): boolean {
			return this.data.length === 0;
		},
	},
});
</script>
