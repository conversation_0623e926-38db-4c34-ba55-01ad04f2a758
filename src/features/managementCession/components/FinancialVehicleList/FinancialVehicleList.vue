<template>
	<farm-box class="mt-4">
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="4" v-for="item in data" :key="item.id">
				<financial-vehicle-card
					:data="item"
					@onUpdatedFinancialVehicleMinValues="onUpdatedFinancialVehicleMinValues"
					@onRevalidate="onRevalidate"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import FinancialVehicleCard from '../FinancialVehicleCard';

export default defineComponent({
	name:'financial-vehicle-list',
	components: {
		FinancialVehicleCard,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
	},
	setup(props, { emit }) {

		const { data } = toRefs(props);

		const isDataEmpty = computed(() => {
			return data.value.length === 0;
		});

		function onUpdatedFinancialVehicleMinValues(value): void {
			emit('onUpdatedFinancialVehicleMinValues', value);
		}

		function onRevalidate(value): void {
			emit('onRevalidate', value);
		}

		return {
			isDataEmpty,
			data,
			onUpdatedFinancialVehicleMinValues,
			onRevalidate
		};
	}
});
</script>
