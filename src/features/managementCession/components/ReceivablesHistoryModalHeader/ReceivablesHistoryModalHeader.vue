<template>
	<farm-box class="mb-2">
		<farm-row>
			<farm-col>
				<ListInformation v-if="data" :data="data" />
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { ListInformation } from '../ListInformation';

import { HeaderFormTypes } from './types';

export default defineComponent({
	components: {
		ListInformation,
	},
	props: {
		data: {
			type: Object as PropType<HeaderFormTypes>,
			default: () => ({}),
		},
	},
});
</script>
