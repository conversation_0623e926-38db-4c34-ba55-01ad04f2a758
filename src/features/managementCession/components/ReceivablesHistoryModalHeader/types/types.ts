export type ListInformationTypes = {
	subtitle: string;
	value: string;
	icon: string;
	copyText: string;
};

export type ListInformationHeader = {
	title: string;
	titleValue: string | number;
};

export type HeaderFormTypes = {
	dataList: Array<ListInformationTypes>;
	header: ListInformationHeader;
};

export const headerFormModel: HeaderFormTypes = {
	dataList: [],
	header: {
		title: '',
		titleValue: '',
	},
};
