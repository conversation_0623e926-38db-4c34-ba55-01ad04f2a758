<template>
	<farm-card>
		<farm-card-content gutter="md">
			<farm-row>
				<farm-col cols="6" class="cash-available-content">
					<farm-typography tag="h3" bold size="sm" class="mt-1">
						Veículo Financeiro
					</farm-typography>
					<farm-typography
						tag="p"
						size="md"
						class="cash-name"
						ellipsis
						:title="data.name"
					>
						{{ data.name }}
					</farm-typography>
				</farm-col>
				<farm-col cols="6" class="cash-available-content">
					<farm-label>Valor mínimo</farm-label>
					<farm-textfield-v2
						v-model="inputVal"
						:mask="currencyMask"
						:rules="[rules.required]"
						@change="onChange"
					/>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>
<script lang="ts">
import { defineComponent, computed, ref, toRefs, watch } from 'vue';

import { currency as currencyMask } from '@/helpers/masks';

import { parse, format } from '../../helpers/financialVehicle';

export default defineComponent({
	name:'financial-vehicle-card',
	props: {
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }){

		const { data } = toRefs(props);

		const inputVal = ref(format(data.value.value));
		const timer = ref(null);

		const rules = computed(() => {
			return {
				required: value => (value !== 'R$' && !!value) || 'Campo obrigatório'
			};
		});

		function onChange(value: string): void{
			if (timer) {
				clearTimeout(timer);
				timer.value = null;
			}
			timer.value = setTimeout(() => {
				const payload = {
					id: data.value.id,
					value: parse(value) || '0'
				};
				emit('onUpdatedFinancialVehicleMinValues', payload);
			}, 750);
		}

		watch(data, newData => {
			inputVal.value = format(newData.value);
		});

		watch(inputVal, newData => {
			emit('input', newData);
			inputVal.value = newData;
		});

		return {
			inputVal,
			rules,
			timer,
			onChange,
			currencyMask
		};
	}
});
</script>
<style lang="scss" scoped>
@import './FinancialVehicleCard.scss';
</style>
