<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="76" size="lg" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Histórico de Recebível" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<div class="my-2">
				<ReceivablesHistoryModalHeader :data="headerList" v-if="item" />
			</div>

			<farm-row>
				<farm-col class="ml-6">
					<farm-logger :items="receivablesHistoryLog" />
				</farm-col>
			</farm-row>

			<farm-loader mode="overlay" v-if="isLoadingReceivablesHistoryLog" />

			<div v-if="isErrorReceivablesHistoryLog" class="mt-10 mb-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>

			<farm-row v-if="!isErrorReceivablesHistoryLog && isDataEmpty">
				<farm-box>
					<farm-emptywrapper subtitle="O recebível não possui histórico disponível." />
				</farm-box>
			</farm-row>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer confirmLabel="Fechar" :hasCancel="false" @onConfirm="onClose" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue';

import { parseHeaderReceivablesModal } from '@/helpers/parseHeaderReceivablesModal';

import { useReceivablesHistoryLog } from '../../composables/useReceivablesHistoryLog';
import { modalHeadersList } from '../../configurations';
import { ReceivablesHistoryModalHeader } from '../ReceivablesHistoryModalHeader';

export default defineComponent({
	components: { ReceivablesHistoryModalHeader },
	name: 'modal-receivables-history',
	props: {
		value: {
			type: Boolean,
			default: false,
		},
		item: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const {
			getReceivablesHistoryLog,
			isErrorReceivablesHistoryLog,
			isLoadingReceivablesHistoryLog,
			receivablesHistoryLog,
		} = useReceivablesHistoryLog();

		const headerList = ref(null);

		function updateModalData() {
			headerList.value = parseHeaderReceivablesModal(
				{
					listIcons: [
						[null, props.item.number],
						[`Sacado: ${props.item.draweeName}`, props.item.draweeDocument],
					],
				},
				modalHeadersList
			);
		}

		function onClose(): void {
			emit('onClose', false);
		}

		function onReload() {
			getReceivablesHistoryLog(props.item.id);
		}

		const isDataEmpty = computed(() => receivablesHistoryLog.value.length === 0);

		watch(
			() => props.value,
			newValue => {
				if (newValue) {
					updateModalData();
					getReceivablesHistoryLog(props.item.id);
				}
			}
		);

		return {
			onClose,
			headerList,
			receivablesHistoryLog,
			isLoadingReceivablesHistoryLog,
			isErrorReceivablesHistoryLog,
			onReload,
			isDataEmpty,
		};
	},
});
</script>
