<template>
	<div class="list-information">
		<farm-idcaption
			tooltipColor="gray"
			v-for="(item, index) in data.dataList"
			:key="`list-information-${item.id}-${index}`"
			:icon="item.icon"
			:copyText="item.copyText"
			:class="{
				'list-information-item': true,
				'with-line': index !== data.dataList.length - 1,
			}"
			:successMessage="messageSucess"
		>
			<template v-slot:title v-if="item.title">
				{{ item.title }}
			</template>
			<template v-slot:subtitle>
				{{ item.subtitle }}: {{ item.value || 'Carregando...' }}
			</template>
		</farm-idcaption>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { HeaderFormTypes } from './types';

export default defineComponent({
	props: {
		data: {
			type: Object as PropType<HeaderFormTypes>,
			required: true,
		},
		messageSucess: {
			type: String,
			default: 'Raiz copiada para área de transferência!',
		},
	},
	computed: {
		hasSuccessMessage() {},
	},
});
</script>

<style lang="scss" scoped>
@import './ListInformation';
</style>
