<template>
	<farm-container>
		<HeaderForm :data="dataHeader" v-if="dataHeader" />
		<farm-row extraDecrease class="mb-4">
			<farm-line noSpacing />
		</farm-row>
		<farm-row justify="space-between" align="center">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Recebível"
					:showFilters="isOpenFilter"
					@onInputChange="handleSearchFilters"
					@onClick="onClickMainFilter"
				/>
			</farm-col>

			<farm-col cols="12" md="3" align="right">
				<farm-select-auto-complete
					class="mt-8"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sortItems"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>

		<collapse-transition :duration="300">
			<history-filters
				v-show="isOpenFilter"
				@onApply="onApplyFilter"
				@onFiltersApplied="handleApplyFilters"
			/>
		</collapse-transition>

		<history-card-list
			v-if="!isError"
			:data="historyHeaders"
			:isSearching="isSearching"
			:params="currentAppliedFilter"
		/>

		<farm-row extra-decrease>
			<farm-box>
				<farm-datatable-paginator
					v-if="pagination && pagination.totalPages > 0"
					:page="page"
					:totalPages="pagination && pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>

		<farm-loader mode="overlay" v-if="isLoading" />

		<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>

		<farm-row extraDecrease class="mb-4">
			<farm-line noSpacing />
		</farm-row>

		<farm-row align="center">
			<farm-col align="end">
				<farm-btn outlined @click="redirectToHistoryHome">Cancelar</farm-btn>
			</farm-col>
		</farm-row>
	</farm-container>
</template>
<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from 'vue';

import { usePageable, useRoute, useRouter } from '@/composible';
import { parseHeaderReceivablesHistory } from '@/helpers/parseHeaderReceivablesHistory';

import { useHistoryCardHeader } from '../../composables/useHistoryCardHeader';
import { useReceivablesHistory } from '../../composables/useReceivablesHistory';
import { headersPages, sort as sortItems } from '../../configurations';
import { HeaderForm } from '../HeaderForm';
import { HistoryCardList } from '../HistoryCardList';
import { HistoryFilters } from '../HistoryFilters';

export default defineComponent({
	name: 'body-history-home',
	components: {
		HistoryCardList,
		HeaderForm,
		HistoryFilters,
	},
	setup() {
		const route = useRoute();
		const router = useRouter();
		const resaleId = route.params.productId;

		function redirectToHistoryHome() {
			router.push(`/admin/wallet/gestao?path=historico_de_recebiveis`);
		}

		const isSearching = ref<boolean | string>(false);
		const currentAppliedFilter = ref<object>({});

		const {
			getHistoryCardHeaders,
			cardHeadersIsError,
			cardHeadersIsLoading,
			historyHeaders,
			historyPagination,
		} = useHistoryCardHeader();

		const {
			getReceivablesHistory,
			isErrorReceivablesHistory,
			isLoadingReceivablesHistory,
			receivablesHistory,
			isSuccessReceivablesHistory,
		} = useReceivablesHistory();

		const dataHeader = ref(null);

		const filters = ref({
			search: null,
		});

		const sortModel = ref('name_ASC');

		const sort = ref({
			order: 'ASC',
			orderby: 'name',
		});

		const isLoading = computed(() => {
			const status = [cardHeadersIsLoading.value, isLoadingReceivablesHistory.value];
			return status.some(status => !!status);
		});

		const isError = computed(() => {
			const status = [isErrorReceivablesHistory.value, cardHeadersIsError.value];
			return status.some(status => !!status);
		});

		const isFiltering = ref(false);

		function handleSearchFilters(value: string) {
			isFiltering.value = value.length > 2;
			isSearching.value = isFiltering.value ? value : '';
			onInputChangeMainFilter(value);
		}

		function handleApplyFilters(value: string) {
			onFiltersApplied(value);
		}

		function checkIfHasCorrectFilter(params) {
			if (!params.limit) {
				params.limit = 10;
			}

			if (!params.search && !!isSearching.value) {
				params.search = isSearching.value;
			}

			currentAppliedFilter.value = params;

			return params;
		}

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
			onApplyFilter,
			isOpenFilter,
			onClickMainFilter,
			onFiltersApplied,
		} = usePageable(
			{
				sort: sort,
				lowercaseSort: true,
				keyInputSearch: 'search',
				filters,
				charInputSearch: 2,
				calbackFn: params => {
					getHistoryCardHeaders({ params: checkIfHasCorrectFilter(params) });
				},
			},
			historyPagination
		);

		function updateHeaderForm() {
			dataHeader.value = parseHeaderReceivablesHistory(
				{
					title: receivablesHistory.value[0].name,
					listIcons: [
						receivablesHistory.value[0].productId,
						[null, receivablesHistory.value[0].quantity.toString()],
					],
				},
				headersPages
			);
		}

		watch(isSuccessReceivablesHistory, newValue => {
			if (newValue) {
				updateHeaderForm();
			}
		});

		onMounted(() => {
			getHistoryCardHeaders(filters);
			getReceivablesHistory({ nameOrId: resaleId });
		});

		function onReload() {
			getHistoryCardHeaders(filters);
		}

		return {
			dataHeader,
			sort,
			sortModel,
			sortItems,
			onInputChangeMainFilter,
			onClickMainFilter,
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onApplyFilter,
			isOpenFilter,
			onReload,
			historyHeaders,
			historyPagination,
			onFiltersApplied,
			isLoading,
			isError,
			isFiltering,
			redirectToHistoryHome,
			isSearching,
			handleSearchFilters,
			handleApplyFilters,
			currentAppliedFilter,
		};
	},
});
</script>
