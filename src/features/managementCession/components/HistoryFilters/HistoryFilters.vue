<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-emissionDate">
						Data de Emissão (Início/fim)</farm-label
					>
					<farm-input-rangedatepicker
						ref="datepickerEmissionDate"
						inputId="form-filter-emission-date"
						v-model="emissionDate"
						@input="onInputDatepickerEmissionDate"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-due-date">
						Data de Vencimento (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerCreatedAt"
						inputId="form-filter-due-date"
						v-model="dueDate"
						@input="onInputDatepickerDueDate"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Valor Nominal </farm-label>
					<farm-select
						id="form-filter-nominal-value"
						v-model="nominalValue"
						:items="historyfilterValues"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="onFilterConfirm"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { historyfilterValues } from '../../configurations';

export default defineComponent({
	name: 'history-filters',
	setup(_, { emit }) {
		const nominalValue = ref('');
		const dueDate = ref([]);
		const emissionDate = ref([]);

		function f(value: string) {
			if (value.length === 2) {
				const startDate = new Date(value[0]);
				const endDate = new Date(value[1]);
				if (startDate > endDate) {
					return [value[1], value[0]];
				}
			}
			return value;
		}

		function checkRangeFilterValues(data, keyStart, keyEnd) {
			return {
				[keyStart]: data[0],
				[keyEnd]: data[1],
			};
		}

		function validDataFilter() {
			let obj = {};
			if (nominalValue.value) {
				const result = checkRangeFilterValues(
					nominalValue.value,
					'nominalValueAtStart',
					'nominalValueAtEnd'
				);

				obj = {
					...obj,
					...result,
				};
			}

			if (dueDate.value && dueDate.value.length === 2) {
				const result = checkRangeFilterValues(
					dueDate.value,
					'expirationAtStart',
					'expirationAtEnd'
				);
				obj = {
					...obj,
					...result,
				};
			}
			if (emissionDate.value && emissionDate.value.length === 2) {
				const result = checkRangeFilterValues(
					emissionDate.value,
					'createdAtStart',
					'createdAtEnd'
				);
				obj = {
					...obj,
					...result,
				};
			}

			return obj;
		}

		function onInputDatepickerEmissionDate(value: string): void {
			emissionDate.value = f(value);
		}

		function onFilterConfirm(): void {
			emit('onApply', validDataFilter());
			emit('onFiltersApplied', true);
		}

		function onFilterClear(): void {
			nominalValue.value = null;
			dueDate.value = null;
			emissionDate.value = null;
			emit('onApply', {});
			emit('onFiltersApplied', false);
		}

		return {
			nominalValue,
			dueDate,
			emissionDate,
			onFilterConfirm,
			onFilterClear,
			onInputDatepickerEmissionDate,
			historyfilterValues,
		};
	},
});
</script>
