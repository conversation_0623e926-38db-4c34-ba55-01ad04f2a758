import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import {
	updatedAllFinancialVehicleMinimumValue as updatedAllFinancialVehicleMinimumValueService
} from '@/features/managementCession/services';

type UseUpdatedAllMinimumValue = {
	isLoadingUpdatedAllMinimumValue: {
		value: boolean
	};
	isErrorUpdatedAllMinimumValue: boolean;
	updatedAll: Function;
};

export function useUpdatedAllMinimumValue(): UseUpdatedAllMinimumValue {
	let callFunc: Function | null = null;
	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => updatedAllFinancialVehicleMinimumValueService(params),
		onSuccess: () => {
			createNotification();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 2000);
			}
		},
		onError:() => {
			createNotificationError();
		}
	});

	function createNotification(): void {
		notification(
			RequestStatusEnum.SUCCESS,
			`Informações atualizadas com sucesso.`
		);
	}

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			`Erro ao atualizar as informações.`
		);
	}

	const isLoadingUpdatedAllMinimumValue = computed(() => {
		return isLoading.value;
	});

	const isErrorUpdatedAllMinimumValue = computed(() => {
		return isError.value;
	});

	function updatedAll(payload, callback: Function): void {
		mutate({ payload });
		callFunc = callback;
	}

	return {
		isLoadingUpdatedAllMinimumValue,
		isErrorUpdatedAllMinimumValue,
		updatedAll,
	};
}
