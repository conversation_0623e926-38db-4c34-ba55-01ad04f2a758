<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="38" size="sm" @onClose="onCloseModal">
		<template v-slot:header>
			<farm-dialog-header title="Definir para todos" @onClose="onCloseModal" />
		</template>
		<template v-slot:content>
			<div class="product-modal-main">
				<div class="product-modal-content mt-2">
					<farm-caption>
						Esta ação irá definir o mesmo valor mínimo para todos os veículos financeiros:
					</farm-caption>
				</div>
				<div class="mt-4">
					<farm-label class="mb-0 ">
						Valor mínimo
					</farm-label>
				</div>
				<farm-textfield-v2
					class="mt-1"
					ref="input"
					v-model="inputModel"
					:mask="mask"
					:rules="[rules.required]"
					@change="onChangeInput"
				/>
			</div>
			<farm-loader mode="overlay" v-if="isLoading" />
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				close-label="Cancelar"
				confirmLabel="Salvar"
				:isConfirmDisabled="disabledButton"
				@onClose="onCloseModal"
				@onConfirm="onConfirmButton"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue';

import { currency as currencyMask, currencyUnmask } from '@/helpers/masks';

import { useUpdatedAllMinimumValue } from './composables/useUpdatedAllMinimumValue';

export default defineComponent({
	name: 'financial-vehicle-modal',
	props: {
		value: {
			type: Boolean,
			required: true,
		}
	},
	setup(_, { emit }) {
		const {
			isLoadingUpdatedAllMinimumValue,
			updatedAll
		} = useUpdatedAllMinimumValue();

		const inputModel = ref('');
		const mask = ref(currencyMask);
		const disabledButton = ref(true);
		const validForm = ref(false);

		const rules = computed(() => {
			return {
				required: value => (value !== 'R$' && !!value) || 'Campo obrigatório',
			};
		});

		const isLoading = computed(() => {
			return isLoadingUpdatedAllMinimumValue.value;
		});

		function updatedPage(): void {
			emit('onClose');
			emit('onReload');
		}

		function onConfirmButton(): void {
			const payload = {
				value: currencyUnmask(inputModel.value).toString()
			};
			updatedAll(payload, updatedPage);
		}

		function onChangeInput(value): void {
			if (value.length <= 2) {
				validForm.value = false;
				return;
			}
			validForm.value = true;
		}

		function onCloseModal(): void {
			emit('onClose');
		}

		watch(validForm, newValue => {
			if (newValue) {
				disabledButton.value = false;
				return;
			}
			disabledButton.value = true;
		});

		return {
			isLoading,
			rules,
			inputModel,
			mask,
			disabledButton,
			onCloseModal,
			onConfirmButton,
			onChangeInput,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './FinancialVehicleModal.scss';
</style>
