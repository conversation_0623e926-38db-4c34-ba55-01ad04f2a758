import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { builderCession } from '@/features/managementCession/helpers/builderCession';
import { getCession as getCessionService } from '@/features/managementCession/services';

type UseCession = {
	cessionPagination: any;
	cessions: Array<any>;
	isLoadingCession: {
		value: boolean;
	};
	isErrorCession: {
		value: boolean;
	};
	getCession: Function;
};

export function useCession(): UseCession {
	const cessions = ref([]);
	const cessionPagination = ref(null);
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getCessionService(params),
		onSuccess: response => {
			const { content, pagination } = builderCession(response.data);
			cessions.value = content;
			cessionPagination.value = pagination;
		},
		onError: error => {
			cessions.value = [];
			cessionPagination.value = null;
		},
	});

	const isLoadingCession = computed(() => {
		return isLoading.value;
	});

	const isErrorCession = computed(() => {
		return isError.value;
	});

	function getCession(filters, callback?: Function): void {
		const params = queryString(filters, {});
		mutate({ filters: params });
		callFunc = callback || null;
	}

	return {
		cessionPagination,
		cessions,
		isLoadingCession,
		isErrorCession,
		getCession,
	};
}
