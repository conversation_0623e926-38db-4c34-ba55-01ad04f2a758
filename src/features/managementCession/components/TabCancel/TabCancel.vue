<template>
	<div>
		<farm-row justify="space-between">
			<farm-col cols="12" class="mb-4">
				<farm-idcaption icon="delete-outline" copyText="">
					<template v-slot:title>
						<farm-heading :type="6"> Cancelar Operação </farm-heading>
					</template>
					<template v-slot:subtitle>
						Cancelamento das operações abertas pela Revenda, e que estejam com status Em
						Análise ou Formalização.
					</template>
				</farm-idcaption>
			</farm-col>
			<farm-col cols="12" md="6">
				<div class="d-flex align-center justify-center">
					<farm-form-mainfilter
						label="Buscar Operação"
						:showFilters="isOpenFilter"
						@onInputChange="onInputChangeMainFilter"
						@onClick="onClickMainFilter"
					>
						<div class="d-flex">
							<farm-label class="mb-0 mr-2">Buscar ID da Operação</farm-label>
							<label-request-results
								v-if="isFilterCounter"
								:totalItems="pagination.operationsTotal || 0"
							/>
						</div>
					</farm-form-mainfilter>
				</div>
			</farm-col>
			<farm-col cols="12" md="2" align="right">
				<farm-select
					class="mt-8"
					id="cession-sort"
					item-text="label"
					item-value="value"
					v-model="sortModel"
					:items="sort"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<filters
				v-show="isOpenFilter"
				@onFiltersApplied="onFiltersApplied"
				@onApply="onApplyFilter"
			/>
		</collapse-transition>
		<resale-list :data="cessions" />
		<farm-row extra-decrease>
			<farm-box>
				<farm-datatable-paginator
					v-if="cessions.length > 0"
					class="mt-6 mb-n6"
					:page="page"
					:totalPages="pagination.totalPages"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, watch } from 'vue';

import LabelRequestResults from '@/components/LabelRequestResults';
import { usePageable } from '@/composible/usePageable';

import { useReloadPage } from '../../composables/useReloadPage';
import { sort } from '../../configurations';
import Filters from '../Filters';
import ResaleList from '../ResaleList';

import { useCession } from './composables/useCession';

export default defineComponent({
	name: 'tab-cancel',
	components: {
		LabelRequestResults,
		Filters,
		ResaleList,
	},
	setup() {
		const { cessionPagination, cessions, getCession, isLoadingCession } = useCession();

		const {
			page,
			pagination,
			isOpenFilter,
			isFilterCounter,
			onApplyFilter,
			onChangePage,
			onChangePageLimit,
			onClickMainFilter,
			onInputChangeMainFilter,
			onSortSelect,
			onFiltersApplied,
		} = usePageable(
			{
				calbackFn: params => {
					getCession(params, updatedPage);
				},
				keyInputSearch: 'search',
				filters: {},
				sort: {
					order: 'ASC',
					orderby: 'name',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			cessionPagination
		);
		const { statusReload } = useReloadPage();

		const sortModel = ref('name_ASC');
		const errorCallAPIs = ref(false);

		const isLoading = computed(() => {
			return isLoadingCession.value;
		});
		const isError = computed(() => {
			return errorCallAPIs.value;
		});

		function updatedPage(hasError: boolean): void {
			if (hasError) {
				errorCallAPIs.value = true;
				return;
			}
			errorCallAPIs.value = false;
		}

		function load(): void {
			const params = {
				page: 0,
				limit: 10,
				order: 'ASC',
				orderby: 'name',
			};
			getCession(params, updatedPage);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		watch(statusReload, (newValue: boolean): void => {
			if (newValue) {
				load();
			}
		});

		return {
			isLoading,
			isError,
			isOpenFilter,
			isFilterCounter,
			cessions,
			sortModel,
			sort,
			page,
			pagination,
			onSortSelect,
			onClickMainFilter,
			onInputChangeMainFilter,
			onFiltersApplied,
			onApplyFilter,
			onReload,
			onChangePage,
			onChangePageLimit,
		};
	},
});
</script>
