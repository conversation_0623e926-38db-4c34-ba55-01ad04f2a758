<template>
	<farm-box>
		<farm-row>
			<farm-col cols="4">
				<CardTextBody label="Cedente" :value="data.providerName" />
			</farm-col>
			<farm-col cols="4">
				<CardTextBody label="CNPJ" :value="data.providerDocument" />
			</farm-col>
			<farm-col cols="4">
				<CardTextBody label="Total de Recebíveis" :value="data.quantity" />
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import Cards from '@/components/Cards';

export default defineComponent({
	name: 'history-card-header',
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
});
</script>
