<template>
	<farm-card>
		<farm-card-content>
			<div class="cession-card-content">
				<div class="d-flex align-center">
					<div>
						
						<farm-idcaption :copy-text="item.id.toString()" tooltipColor="gray" no-height class="mb-2"> 
							<template v-slot:subtitle>
								<farm-bodytext>
									<b>ID: {{ item.id }} </b>
								</farm-bodytext>
							</template>
						</farm-idcaption>
					
						<farm-bodysmall variation="bold" color="black" color-variation="50">
							Operação: {{ getOperationTypeName(item.operationType) }}
						</farm-bodysmall>
					
					</div>
					<div class="cession-card-separation"></div>
					<div class="ml-2 mr-8">
						<farm-idcaption no-height>
							<template v-slot:title>
								<farm-caption variation="regular">
									<b>Recebíveis:</b> {{ item.receivables }}
								</farm-caption>
							</template>
						</farm-idcaption>
						<farm-idcaption no-height>
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									<b>Aprovados:</b> {{ item.approved }}
								</farm-caption>
							</template>
						</farm-idcaption>
						<farm-idcaption no-height>
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									<b>Recusados:</b> {{ item.refused }}
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
					<div class="cession-card-separation"></div>
					<div class="mr-8">
						<farm-idcaption no-height>
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									<b>Criação:</b>
									{{ formatDate(cleanDate(item.createdAt)) || 'N/A' }}
								</farm-caption>
							</template>
						</farm-idcaption>
						<farm-idcaption no-height>
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									<b>Atualização:</b>
									{{ formatDate(cleanDate(item.updatedAt)) || 'N/A' }}
								</farm-caption>
							</template>
						</farm-idcaption>
						<farm-idcaption no-height>
							<template v-slot:title>
								<farm-caption :variation="'regular'">
									<b>Desembolso:</b>
									{{ formatDate(cleanDate(item.dateDisbursement)) || 'N/A' }}
								</farm-caption>
							</template>
						</farm-idcaption>
					</div>
					<div class="cession-card-separation"></div>
					<div class="mr-8">
						<farm-caption>
							<farm-tooltip>
								<span> Valor nominal dos recebíveis da operação. </span>
								<template v-slot:activator>
									<b>Valor Nominal </b>
									<farm-icon size="sm" color="gray">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-caption>
						<farm-caption>
							{{ formatValueOrNA(item.valueNominal) }}
						</farm-caption>
					</div>
					<div class="cession-card-separation"></div>
					<div class="mr-8">
						<farm-caption>
							<farm-tooltip>
								<span> Valor nominal menos o valor do excedente. </span>
								<template v-slot:activator>
									<b>Valor Livre </b>
									<farm-icon size="sm" color="gray">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-caption>
						<farm-caption>
							{{ formatValueOrNA(item.totalFreeValue) }}
						</farm-caption>
					</div>
					<div class="cession-card-separation"></div>
					<div class="mr-8">
						<farm-caption>
							<farm-tooltip>
								<span> Valor desembolsado/a desembolsar da operação. </span>
								<template v-slot:activator>
									<b>Valor Liquido </b>
									<farm-icon size="sm" color="gray">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-caption>
						<farm-caption>
							{{ formatValueOrNA(item.totalLiquidValue) }}
						</farm-caption>
					</div>
				</div>
				<div class="cession-card-right">
					<cession-tooltip :status="item.sectionStatus">
						<cession-status :status="item.sectionStatus" class="mr-2" />
					</cession-tooltip>
					<button-cancel :item="item" />
				</div>
			</div>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { defaultDateFormat, brl } from '@farm-investimentos/front-mfe-libs-ts';

import { operationTypes } from '@/features/managementCession/configurations/operationTypes';

import ButtonCancel from '../ButtonCancel';
import CessionStatus from '../CessionStatus';
import CessionTooltip from '../CessionTooltip';

export default defineComponent({
	name: 'cession-card',
	components: {
		CessionStatus,
		CessionTooltip,
		ButtonCancel,
	},
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	setup() {
		function cleanDate(data) {
			if (data && data.indexOf('T') > 0) {
				const dataSplit = data.split('T')[0];
				return dataSplit;
			}
			return data;
		}

		function formatValueOrNA(value) {
			if (value === null || value === undefined || isNaN(value)) {
				return 'N/A';
			}
			return brl(value);
		}

		function getOperationTypeName(typeId) {
			if (!typeId) return 'N/A';
			const typeIdNumber = parseInt(typeId, 10);
			const operationType = operationTypes.find(type => type.id === typeIdNumber);
			return operationType ? operationType.label : 'N/A';
		}

		return {
			formatDate: defaultDateFormat,
			formatMoney: brl,
			formatValueOrNA,
			getOperationTypeName,
			cleanDate
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CessionCard';
</style>
