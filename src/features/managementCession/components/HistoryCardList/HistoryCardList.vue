<template>
	<farm-box>
		<farm-row y-grid-gutters v-if="!isDataEmpty">
			<farm-col cols="12" v-for="(item, index) in data" :key="`${item.productId}::${index}`">
				<farm-collapsible
					custom
					title=""
					:open="isOpenCollapse[index]"
					@open="
						currentCollapseStatus =>
							handlePerCollapseDetails(currentCollapseStatus, index, item)
					"
					:key="generateResetKey(index)"
					custom-body
				>
					<template #custom>
						<history-card-header :data="item" />
					</template>

					<div v-if="!!perCollapseData[index]">
						<history-card-details
							v-for="details in perCollapseData[index]"
							:data="details"
							:key="details.id"
							@onClick="handleModalStatus"
						/>
					</div>

					<farm-row
						style="background-color: #f5f5f5"
						extra-decrease
						class="px-4"
						v-if="perCollapsePagination[index]"
					>
						<farm-box class="collapse-pagination">
							<farm-datatable-paginator
								:page="perCollapsePagination[index].pageNumber"
								:totalPages="perCollapsePagination[index].totalPages"
								@onChangePage="value => handlePageChange(index, value)"
								@onChangeLimitPerPage="value => handleLimitPerPage(index, value)"
							/>
						</farm-box>
					</farm-row>
					<farm-row extra-decrease v-if="!perCollapseData[index]">
						<farm-box>
							<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
						</farm-box>
					</farm-row>
				</farm-collapsible>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa." />
			</farm-box>
		</farm-row>

		<ReceivablesHistoryModal
			v-model="isModalOpen"
			:item="itemModalSelected"
			@onClose="handleCloseModal"
		/>

		<farm-loader mode="overlay" v-if="cardDetailsIsLoading" />

		<div v-if="cardDetailsIsError" class="mt-10 mb-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { computed, defineComponent, ref, watch, toRefs } from 'vue';

import { usePageable } from '@farm-investimentos/front-mfe-libs-ts';
import { PropType } from 'vue/types/v3-component-props';

import { useRoute } from '@/composible';

import { useHistoryCardDetails } from '../../composables/useHistoryCardDetails';
import { HistoryCardDetails } from '../HistoryCardDetails';
import { HistoryCardHeader } from '../HistoryCardHeader';
import { ReceivablesHistoryModal } from '../ReceivablesHistoryModal';

export type Cards = {
	quantity: number;
	productName: string;
	providerDocument: string;
	providerName: string;
	productId: number;
};

export default defineComponent({
	name: 'history-card-list',
	components: {
		HistoryCardHeader,
		HistoryCardDetails,
		ReceivablesHistoryModal,
	},
	props: {
		data: {
			type: Array as PropType<Cards[]>,
			default: () => [],
		},
		isSearching: {
			type: [String, Boolean],
			required: true,
		},
		params: {
			type: Object,
			required: true,
		},
	},
	setup(props) {
		const { params } = toRefs(props);
		const isOpen = ref(false);
		const isDataEmpty = computed(() => props.data?.length === 0);
		const isFiltering = ref<boolean>(false);
		const isOpenCollapse = ref([]);
		const isModalOpen = ref(false);
		const route = useRoute();
		const resaleId = route.params.productId;
		const historyCardIndex = ref<number | null>(null);
		const itemModalSelected = ref({});

		function handleCloseModal() {
			isModalOpen.value = false;
		}

		function updateCollapseIndex(index: number) {
			historyCardIndex.value = index;
		}

		function handlePageChange(itemIndex: number, value: number) {
			updateCollapseIndex(itemIndex);
			onChangePage(value);
		}

		function generateResetKey(index) {
			const filterKeyPart = JSON.stringify(currentAppliedFilter.value);
			return `collapse-${index}-${isOpenCollapse.value[index]}-${filterKeyPart}`;
		}

		function handleLimitPerPage(itemIndex: number, value: number) {
			updateCollapseIndex(itemIndex);
			onChangePageLimit(value);
		}

		function handleModalStatus(item) {
			isModalOpen.value = !isModalOpen.value;
			itemModalSelected.value = item;
		}

		const currentPagination = ref({
			pageNumber: 0,
			pageSize: 10,
			totalPages: 0,
		});

		const currentAppliedFilter = computed(() => filterAppliedFilters(params.value));

		const providerDocumentRef = ref(null);

		const valueIsSearching = computed(() => props.isSearching || null);

		function checkIfHasCorrectFilter(params) {
			isFiltering.value = true;

			if (valueIsSearching.value) {
				params.number = valueIsSearching.value;
			}

			return params;
		}

		const {
			cardDetailsIsError,
			cardDetailsIsLoading,
			getHistoryCardDetails,
			cardDetails,
			cardDetailsPagination,
			cardDetailsIsSuccess,
		} = useHistoryCardDetails();

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onInputChangeMainFilter,
			onSortSelect,
			onFiltersApplied,
		} = usePageable(
			{
				callbackFn: params => {
					getHistoryCardDetails({
						productId: resaleId,
						providerDocument: providerDocumentRef.value,
						params: {
							...currentAppliedFilter.value,
							...checkIfHasCorrectFilter(params),
						},
					});
				},
				filters: {},
				sort: {},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			currentPagination
		);

		watch(
			() => props.params,
			() => {
				closeAllCollapses();
			},
			{ deep: true }
		);

		watch(
			() => valueIsSearching.value,
			() => {
				closeAllCollapses();
			}
		);

		function closeAllCollapses() {
			const updatedCollapseState = {};
			for (const key in isOpenCollapse.value) {
				updatedCollapseState[key] = false;
			}
			isOpenCollapse.value = { ...updatedCollapseState };
		}

		function filterAppliedFilters(filters) {
			return Object.fromEntries(
				Object.entries(filters).filter(
					([key, value]) =>
						!['order', 'orderby', 'limit', 'page', 'name', 'search'].includes(key) &&
						value !== undefined &&
						value !== null
				)
			);
		}

		function handlePerCollapseDetails(status, collapsibleIndex, item): void {
			isFiltering.value = false;
			historyCardIndex.value = collapsibleIndex;
			providerDocumentRef.value = item.providerDocument;

			isOpenCollapse.value = { ...isOpenCollapse.value, [collapsibleIndex]: status };
			if (status) {
				getHistoryCardDetailsWithDefaultValues();
			}
		}

		const perCollapseData = ref([]);
		const perCollapsePagination = ref([]);

		function getHistoryCardDetailsWithDefaultValues() {
			let params = { ...currentAppliedFilter.value };
			if (valueIsSearching.value) {
				params = {
					...params,
					number: valueIsSearching.value,
				};
			}

			getHistoryCardDetails({
				productId: resaleId,
				providerDocument: providerDocumentRef.value,
				params: { ...params, limit: 10, page: 0 },
			});
		}

		function updatePerCollapseListObject() {
			perCollapseData.value[historyCardIndex.value] = cardDetails.value;

			if (!isFiltering.value) {
				cardDetailsPagination.value.pageNumber = 1;
				perCollapsePagination.value[historyCardIndex.value] = cardDetailsPagination.value;
				return;
			}

			perCollapsePagination.value[historyCardIndex.value].totalPages =
				cardDetailsPagination.value.totalPages;
			perCollapsePagination.value[historyCardIndex.value].pageSize =
				cardDetailsPagination.value.pageSize;
		}

		watch(cardDetailsIsSuccess, newValue => {
			if (newValue) {
				updatePerCollapseListObject();
			}
		});

		watch(
			() => props.isSearching,
			(newValue, oldValue) => {
				if (newValue != oldValue) {
					for (const [key] of Object.entries(isOpenCollapse.value)) {
						isOpenCollapse.value[key] = false;
					}
				}
			}
		);

		function onReload() {
			getHistoryCardDetails({
				productId: resaleId,
				providerDocument: providerDocumentRef.value,
				params: {
					limit: 10,
					page: 0,
					...currentAppliedFilter.value,
				},
			});
		}

		return {
			isOpen,
			isDataEmpty,
			handlePerCollapseDetails,
			cardDetailsIsLoading,
			cardDetailsIsError,
			cardDetails,
			onReload,
			perCollapseData,
			perCollapsePagination,
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onInputChangeMainFilter,
			onSortSelect,
			onFiltersApplied,
			historyCardIndex,
			updateCollapseIndex,
			cardDetailsPagination,
			currentPagination,
			isOpenCollapse,
			handleLimitPerPage,
			handlePageChange,
			isModalOpen,
			itemModalSelected,
			handleCloseModal,
			handleModalStatus,
			currentAppliedFilter,
			generateResetKey,
		};
	},
});
</script>

<style lang="scss">
@import './HistoryCardList';
</style>
