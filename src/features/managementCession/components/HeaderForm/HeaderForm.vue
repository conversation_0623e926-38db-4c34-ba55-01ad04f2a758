<template>
	<farm-box class="mb-2">
		<farm-row>
			<farm-col cols="12">
				<farm-heading :type="6" class="mb-4">
					{{ data.title || 'Carregando...' }}
				</farm-heading>
			</farm-col>
			<farm-col cols="12">
				<HeaderFormList
					class="mb-4"
					:data="data.dataList"
					:messageSucess="data.messageSucess"
					:hide-copy-btn="hideCopyBtn"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="withLine">
			<farm-line noSpacing />
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { HeaderFormList } from '../HeaderFormList';

import { HeaderFormTypes } from './types';

export default defineComponent({
	components: {
		HeaderFormList,
	},
	props: {
		data: {
			type: Object as PropType<HeaderFormTypes>,
			default: () => ({}),
		},
		withLine: {
			type: Boolean,
			default: () => false,
		},
		hideCopyBtn: {
			type: Boolean,
			default: () => false,
		},
	},
});
</script>
