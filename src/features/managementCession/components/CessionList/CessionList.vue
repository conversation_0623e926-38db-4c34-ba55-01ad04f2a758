<template>
	<farm-row>
		<farm-col cols="12" v-for="item in data" :key="item.id">
			<cession-card class="my-2" :item="item" />
		</farm-col>
	</farm-row >
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import CessionCard from '../CessionCard';
import HeaderCollapsible from '../HeaderCollapsible';

export default defineComponent({
	name:"cession-list",
	components:{
		CessionCard,
		HeaderCollapsible
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	}
});

</script>
