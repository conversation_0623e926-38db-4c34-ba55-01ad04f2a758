<template>
	<farm-container>
		<HeaderForm :data="dataHeader" v-if="dataHeader" hide-copy-btn />
		<farm-row extraDecrease class="mb-4">
			<farm-line noSpacing />
		</farm-row>

		<farm-row justify="space-between" align="center">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Recebível"
					tooltip="Realize sua busca pelo ID da Importação, Número do Recebível, Chave de Acesso da NF-e, Nome ou CNPJ do Cedente, e Nome ou CNPJ do Sacado."
					:showFilters="isFilterOpen"
					@onInputChange="handleSearchFilters"
					@onClick="onClickMainFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="4" align="right" class="d-flex justify-end">
				<farm-btn outlined @click="reprocessSelected">Reprocessar Selecionados</farm-btn>
				<farm-btn @click="reprocessAll" class="ml-4">Reprocessar Todos</farm-btn>
			</farm-col>
		</farm-row>

		<collapse-transition :duration="300">
			<ReprocessableInvoicesFilters
				v-if="isFilterOpen"
				@applyFilters="applyFilters"
				@clearFilters="clearFilters"
			/>
		</collapse-transition>

		<farm-row extraDecrease v-if="showAtLeastOneSelectedMessage">
			<farm-col cols="12" md="12" class="mb-6">
				<farm-alertbox icon="alert-circle-outline" color="error" class="w-full mx-2">
					Ao menos um recebível deve ser selecionado para reprocessamento.
				</farm-alertbox>
			</farm-col>
		</farm-row>

		<farm-row justify="space-between" class="space-between align-center mb-6">
			<farm-col cols="12" md="2">
				<farm-chip v-show="cardHeaderList.length" color="primary" variation="lighten" dense>
					{{ selectedInvoiceAmount }} selecionados
				</farm-chip>
			</farm-col>
			<farm-col cols="12" md="3">
				<farm-select
					item-text="label"
					item-value="value"
					v-model="selectedSort"
					:items="reprocessableInvoicesSort"
					@change="changeSort"
					class="d-flex align-center"
					style="height: auto"
				/>
			</farm-col>
		</farm-row>

		<div class="mt-4 mb-8" v-if="cardHeaderList.length && !isLoading">
			<ReprocessableInvoiceCardHeader
				v-for="cardInfo in cardHeaderList"
				:product-id="productId"
				:key="cardInfo.id"
				:info="cardInfo"
				:is-select-all-checked="
					reprocessableInvoicesRequestParams.some(batch => batch.id === cardInfo.id)
				"
				:nfe-search="nfeSearch"
				:should-update-state="shouldUpdateHeaderCardState"
				@removeInvoice="loadData"
				@reprocessInvoice="loadData"
				@update:selected="updateSelected"
			/>
		</div>
		<div v-if="!cardHeaderList.length && !isLoading" class="mt-10 mb-10">
			<FilterEmptyState :subtitle="emptyStateSubtitle" />
		</div>

		<farm-row extra-decrease v-if="cardHeaderList.length">
			<farm-box>
				<farm-datatable-paginator
					:page="pagination.page"
					:totalPages="pagination.totalPages"
					:initial-limit-per-page="pagination.limit"
					@onChangePage="changePage"
					@onChangeLimitPerPage="changePageLimit"
				/>
			</farm-box>
		</farm-row>

		<farm-row extraDecrease class="mb-4">
			<farm-line noSpacing />
			<ModalReprocessBulkSelectedOnly
				:show="showReprocessBulkSelectedModal"
				:amount="selectedInvoiceAmount"
				@close="closeReprocessBulkSelectedModal"
				@confirm="confirmReprocessBulkSelectedModal"
			/>
			<ModalReprocessAllInBulk
				:show="showReprocessAllInBulkModal"
				:amount="selectedInvoiceAmount"
				@close="closeReprocessAllInBulkModal"
				@confirm="confirmReprocessAllInBulkModal"
			/>
		</farm-row>

		<farm-row align="center">
			<farm-col align="end">
				<farm-btn outlined @click="redirectToReprocessableInvoicesListing"
					>Cancelar</farm-btn
				>
			</farm-col>
		</farm-row>

		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-container>
</template>

<script lang="ts">
import { computed, Ref, ref, watch, onMounted, nextTick } from 'vue';

import { notification } from '@farm-investimentos/front-mfe-libs-ts';

import FilterEmptyState from '@/components/FilterEmptyState';
import { useRouter } from '@/composible';
import { useFetchProductHeaders } from '@/features/_composables/useFetchProductHeaders';
import {
	ReprocessableBatch,
	useFetchReprocessableBatches,
} from '@/features/_composables/useFetchReprocessableBatches';
import { usePostReprocessableBatchesInvoices } from '@/features/_composables/usePostReprocessableBatchesInvoices';

import { PostReprocessableBatchesInvoicesArgs } from '../../services/services';
import { HeaderForm } from '../HeaderForm';

import ModalReprocessAllInBulk from './components/ModalReprocessAllInBulk.vue';
import ModalReprocessBulkSelectedOnly from './components/ModalReprocessBulkSelectedOnly.vue';
import ReprocessableInvoiceCardHeader from './components/ReprocessableInvoicesCardHeader.vue';
import ReprocessableInvoicesFilters, {
	ProcessableInvoicesFilters,
} from './components/ReprocessableInvoicesFilters.vue';
import { SelectedInvoice } from './components/ReprocessableInvoicesListing.vue';

type SortOrder = 'ASC' | 'DESC';

type UpdateSelectedValue = {
	isAllSelected: boolean;
	id: number;
	danfes: SelectedInvoice[];
	danfesToNotReprocess: SelectedInvoice[];
	totalInvoices: number;
	selectedAmount: number;
	incrementInvoicesBy: number;
	reprocessEntireBatch: boolean;
};

export default {
	name: 'BodyReprocessableInvoices',
	components: {
		HeaderForm,
		ReprocessableInvoiceCardHeader,
		ReprocessableInvoicesFilters,
		FilterEmptyState,
		ModalReprocessBulkSelectedOnly,
		ModalReprocessAllInBulk,
	},
	setup() {
		const REPROCESS_INVOICES_ROUTE = `/admin/wallet/gestao?path=reprocessar_recebiveis`;
		const router = useRouter();
		const isFilterOpen = ref(false);
		const { productId } = router.currentRoute.params;
		const cardHeaderList = ref<ReprocessableBatch[]>([]);
		const { data: productHeadersData, fetchProductHeaders } = useFetchProductHeaders();
		const {
			data: reprocessableBatchesData,
			state: reprocessableBatchesState,
			fetchReprocessableBatches,
		} = useFetchReprocessableBatches();
		const { postReprocessableBatchesInvoicesRequest } = usePostReprocessableBatchesInvoices();
		const isLoading = computed(() => reprocessableBatchesState.value === 'LOADING');
		const searchTerm = ref('');
		const filters = ref<ProcessableInvoicesFilters>(null);
		const pagination = ref({
			page: 1,
			totalPages: 0,
			totalItems: 0,
			limit: 10,
		});
		const selectedSort = ref<SortOrder>('DESC');
		const reprocessableInvoicesSort = ref([
			{
				label: 'Envio mais recente - menos recente',
				value: 'DESC',
			},
			{
				label: 'Envio menos recente - mais recente',
				value: 'ASC',
			},
		]);
		const dataHeader = ref({
			title: '',
			dataList: [
				{
					subtitle: 'ID',
					value: null,
					icon: 'account-box-outline',
				},
				{
					subtitle: 'Tipo',
					value: null,
					icon: 'tag-outline',
				},
				{
					subtitle: 'Quantidade de recebíveis',
					value: null,
					icon: 'clipboard-text-outline',
				},
			],
		});
		const invoiceBatchesInfo = ref<Partial<UpdateSelectedValue>[]>([]);
		const showAtLeastOneSelectedMessage = ref(false);
		const shouldUpdateHeaderCardState = ref(false);
		const showReprocessBulkSelectedModal = ref(false);
		const showReprocessAllInBulkModal = ref(false);
		const reprocessableInvoicesRequestParams = ref<
			PostReprocessableBatchesInvoicesArgs['batches']
		>([]);
		const selectedInvoiceAmount = computed(() => {
			return invoiceBatchesInfo.value.reduce((acc, batch) => acc + batch.selectedAmount, 0);
		});

		const hasInvoicesButResponseIsEmpty = computed(() => {
			return (
				reprocessableBatchesData.value?.meta.totalInvoices &&
				!reprocessableBatchesData.value.content.length
			);
		});
		const emptyStateSubtitle = computed(() => {
			return hasInvoicesButResponseIsEmpty
				? `Tente filtrar novamente sua pesquisa.`
				: `Não existem recebíveis passíveis de reprocessamento no momento.`;
		});

		const nfeSearch = computed(() => {
			return searchTerm.value;
		});

		function redirectToReprocessableInvoicesListing() {
			router.push(REPROCESS_INVOICES_ROUTE);
		}

		function normalizeHeaders() {
			dataHeader.value = {
				title: productHeadersData.value.name,
				dataList: [
					{
						subtitle: 'ID',
						value: productHeadersData.value.id,
						icon: 'account-box-outline',
					},
					{
						subtitle: 'Tipo',
						value: productHeadersData.value.type,
						icon: 'tag-outline',
					},
					{
						subtitle: 'Quantidade de recebíveis',
						value: reprocessableBatchesData.value.meta.totalInvoices,
						icon: 'clipboard-text-outline',
					},
				],
			};
		}

		async function loadHeaders() {
			await fetchProductHeaders(+productId);
		}

		async function loadProcessableBatches() {
			await fetchReprocessableBatches(+productId, {
				page: pagination.value.page - 1,
				limit: pagination.value.limit,
				order: selectedSort.value,
				orderby: 'createdAt',
				status: filters.value?.status,
				createdAtStart: filters.value?.dateRange?.from,
				createdAtEnd: filters.value?.dateRange?.to,
				...(searchTerm.value && { search: searchTerm.value }),
			});

			pagination.value.totalItems = reprocessableBatchesData.value.totalItems;
			pagination.value.totalPages = reprocessableBatchesData.value.totalPages;

			cardHeaderList.value = reprocessableBatchesData.value.content;
		}

		async function loadData() {
			await Promise.all([loadHeaders(), loadProcessableBatches()]);
			normalizeHeaders();
		}

		function changePage(page: number) {
			pagination.value.page = page;
			loadData();
		}

		function changePageLimit(limit: number) {
			pagination.value.limit = limit;
			pagination.value.page = 1;

			loadData();
		}

		function handleSearchFilters(search: string) {
			pagination.value.page = 1;
			searchTerm.value = search.trim();
			loadData();
		}

		function applyFilters(filtersEvent: Ref<ProcessableInvoicesFilters>) {
			filters.value = filtersEvent.value;

			loadData();
		}

		function clearFilters() {
			filters.value = null;
			loadData();
		}

		function changeSort(value: any) {
			selectedSort.value = value;
			loadData();
		}

		function reprocessSelected() {
			if (!selectedInvoiceAmount.value) {
				showAtLeastOneSelectedMessage.value = true;
				return;
			}
			showReprocessBulkSelectedModal.value = true;
		}

		function reprocessAll() {
			showReprocessAllInBulkModal.value = true;
		}

		function onClickMainFilter() {
			isFilterOpen.value = !isFilterOpen.value;
		}

		function buildReprocessableInvoicesRequestParams(batches: Partial<UpdateSelectedValue>[]) {
			return batches
				.filter(batch => {
					const hasInvoicesSelected = batch.danfes.length > 0;
					const hasInvoicesToNotReprocess = batch.danfesToNotReprocess.length > 0;
					const isEntireBatchSelected = batch.isAllSelected;

					return (
						isEntireBatchSelected || hasInvoicesSelected || hasInvoicesToNotReprocess
					);
				})
				.map(batch => ({
					id: batch.id,
					danfes: batch.danfes.map(d => d.danfe),
					danfesToNotReprocess: batch.danfesToNotReprocess.map(d => d.danfe),
					reprocessEntireBatch: batch.isAllSelected,
				}));
		}

		function updateSelected(selectedBatch: UpdateSelectedValue) {
			invoiceBatchesInfo.value = Array.from(
				new Map(
					[
						...invoiceBatchesInfo.value,
						{
							isAllSelected: selectedBatch.isAllSelected,
							id: selectedBatch.id,
							danfes: selectedBatch.danfes,
							danfesToNotReprocess: selectedBatch.danfesToNotReprocess,
							selectedAmount: selectedBatch.selectedAmount,
						},
					].map(batch => [batch.id, batch])
				).values()
			);

			reprocessableInvoicesRequestParams.value = buildReprocessableInvoicesRequestParams(
				invoiceBatchesInfo.value
			);
		}

		function cleanState() {
			shouldUpdateHeaderCardState.value = true;
			reprocessableInvoicesRequestParams.value = [];
			invoiceBatchesInfo.value = [];

			nextTick(() => {
				shouldUpdateHeaderCardState.value = false;
			});
		}

		function closeReprocessBulkSelectedModal() {
			showReprocessBulkSelectedModal.value = false;
		}

		async function confirmReprocessBulkSelectedModal() {
			await postReprocessableBatchesInvoicesRequest({
				productId,
				batches: reprocessableInvoicesRequestParams.value,
				reprocessAll: false,
			});

			showReprocessBulkSelectedModal.value = false;

			notification('SUCCESS', 'Reprocessamento em andamento');
			cleanState();
			await loadData();

			if (cardHeaderList.value.length === 0) {
				redirectToReprocessableInvoicesListing();
			}
		}

		function closeReprocessAllInBulkModal() {
			showReprocessAllInBulkModal.value = false;
		}

		async function confirmReprocessAllInBulkModal() {
			await postReprocessableBatchesInvoicesRequest({
				productId,
				batches: [],
				reprocessAll: true,
			});

			showReprocessAllInBulkModal.value = false;

			notification('SUCCESS', 'Reprocessamento em andamento');

			setTimeout(() => {
				redirectToReprocessableInvoicesListing();
			}, 1500);
		}

		watch(selectedInvoiceAmount, value => {
			if (value) {
				showAtLeastOneSelectedMessage.value = false;
			}
		});

		onMounted(async () => {
			loadData();
		});

		return {
			shouldUpdateHeaderCardState,
			showAtLeastOneSelectedMessage,
			showReprocessBulkSelectedModal,
			closeReprocessBulkSelectedModal,
			confirmReprocessBulkSelectedModal,
			showReprocessAllInBulkModal,
			closeReprocessAllInBulkModal,
			confirmReprocessAllInBulkModal,
			productId,
			redirectToReprocessableInvoicesListing,
			changePage,
			changePageLimit,
			handleSearchFilters,
			onClickMainFilter,
			applyFilters,
			clearFilters,
			nfeSearch,
			dataHeader,
			isFilterOpen,
			cardHeaderList,
			pagination,
			changeSort,
			selectedSort,
			reprocessableInvoicesSort,
			reprocessSelected,
			reprocessAll,
			selectedInvoiceAmount,
			updateSelected,
			hasInvoicesButResponseIsEmpty,
			loadData,
			isLoading,
			emptyStateSubtitle,
			invoiceBatchesInfo,
			reprocessableInvoicesRequestParams,
		};
	},
};
</script>
