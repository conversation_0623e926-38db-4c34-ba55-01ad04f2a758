<template>
	<farm-box class="mb-6">
		<farm-form>
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Status </farm-label>
					<farm-select
						v-model="selectedStatus"
						:items="statusList"
						item-text="label"
						item-value="value"
					/>
				</farm-col>

				<farm-col cols="12" md="3">
					<farm-label for="form-filter-due-date"> Data de Envio (Início/fim) </farm-label>
					<farm-input-rangedatepicker
						v-model="dateRange"
						inputId="form-filter-date-range"
						@input="inputDatepickerDateRange"
					/>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col>
					<farm-btn-confirm
						outlined
						title="Aplicar Filtros"
						@click="onFilterConfirm"
						class="ml-0"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn plain depressed title="Limpar Filtros" @click="onFilterClear">
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export type ProcessableInvoiceFilterDate = `${number}-${number}-${number}` | 'Invalid Date';

export type ProcessableInvoicesFilters = {
	status: number | null;
	dateRange?: {
		from: ProcessableInvoiceFilterDate;
		to: ProcessableInvoiceFilterDate;
	} | null;
};

export default defineComponent({
	name: 'processable-invoices-filters',
	props: {
		statusValues: {
			type: Array,
			default: () => [],
		},
	},
	setup(_, { emit }) {
		const selectedStatus = ref<number>(null);
		const dateRange = ref([]);
		const emissionDate = ref([]);
		const filters = ref<ProcessableInvoicesFilters>({
			status: null,
			dateRange: null,
		});
		const statusList = [
			{
				value: 2,
				label: 'Validando',
			},
			{
				value: 4,
				label: 'Concluído',
			},
		];

		function onFilterClear(): void {
			selectedStatus.value = null;
			dateRange.value = [];

			emit('clearFilters');
		}

		function onFilterConfirm() {
			const [from, to] = dateRange.value;

			if (!from || !to) {
				filters.value = {
					status: selectedStatus.value,
				};

				emit('applyFilters', filters);

				return;
			}

			const transformDate = (rawDate: string) => {
				const date = new Date(rawDate);
				const formattedDate = date.toISOString().split('T')[0];

				return formattedDate as ProcessableInvoiceFilterDate;
			};

			filters.value = {
				status: selectedStatus.value,
				dateRange: { from: transformDate(from), to: transformDate(to) },
			};

			emit('applyFilters', filters);
		}

		function inputDatepickerDateRange(range: ProcessableInvoiceFilterDate[]): void {
			const [from, to] = range;

			dateRange.value = range;
			filters.value.dateRange = {
				from,
				to,
			};
		}

		return {
			dateRange,
			emissionDate,
			statusList,
			selectedStatus,
			onFilterConfirm,
			onFilterClear,
			inputDatepickerDateRange,
		};
	},
});
</script>
