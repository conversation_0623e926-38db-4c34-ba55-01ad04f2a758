<template>
	<farm-modal v-model="show" :offsetTop="48" :offsetBottom="68" size="sm" @onClose="close">
		<template v-slot:header>
			<farm-dialog-header title="Reprocessar Recebíveis" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-caption>
				Tem certeza que deseja reprocessar todos os recebíveis {{ `(${amount})` }}?
			</farm-caption>
			<farm-caption class="mb-4">
				Você pode visualizar o processamanto no histórico de importações no Banco de
				Recebíveis.
			</farm-caption>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Sim"
				closeLabel="Cancelar"
				@onConfirm="confirm"
				@onClose="close"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name: 'modal-cancel-cession',
	props: {
		show: {
			type: Boolean,
			required: true,
		},
		amount: {
			type: Number,
			required: false,
			default: 0,
		},
	},
	setup(props, { emit }) {
		const inputModel = ref(false);

		function close() {
			emit('close');
		}

		function confirm() {
			emit('confirm');
		}

		return {
			inputModel,
			confirm,
			close,
		};
	},
});
</script>
