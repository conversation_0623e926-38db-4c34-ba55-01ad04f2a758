<template>
	<general-info-modal
		v-model="isVisible"
		:data="invoice"
		@onClose="closeDetails"
	/>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';

import GeneralInfoModal from '@/components/GeneralInfoModal/GeneralInfoModal.vue';
import { ReprocessableBatchesInvoice } from '@/features/_composables/useFetchReprocessableBatchesInvoices';

export default defineComponent({
	name: 'ReprocessableInvoicesRowDetails',
	components: {
		GeneralInfoModal,
	},
	props: {
		show: {
			type: Boolean,
			required: true,
		},
		invoice: {
			type: Object as () => ReprocessableBatchesInvoice,
			required: true,
		},
	},
	setup(props, { emit }) {
		const isVisible = ref(props.show);

		function closeDetails() {
			emit('closeDetails');
		}

		watch(
			() => props.show,
			newVal => {
				isVisible.value = newVal;
			}
		);

		return { isVisible, closeDetails };
	},
});
</script>

<style lang="scss" scoped>
.darken {
	color: #5c5c5c;
}

.custom-clipboard-wrapper {
	position: relative;
}

.custom-clipboard-style {
	position: absolute;
	transform: translateY(-50%) translateX(-0.5em);
}
</style>
