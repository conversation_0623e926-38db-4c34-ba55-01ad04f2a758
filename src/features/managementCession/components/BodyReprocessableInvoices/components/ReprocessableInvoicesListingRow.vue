<template>
	<div>
		<farm-row class="d-flex align-center my-4">
			<farm-col class="d-flex justify-center" :style="flexBasisStyles[0]">
				<farm-checkbox v-model="isSelected" :value="true" size="sm" />
			</farm-col>
			<farm-col class="d-flex align-center pl-0" :style="flexBasisStyles[1]">
				<farm-chip :color="status && status.color" style="width: 95px" dense>
					{{ status.text || '-' }}
				</farm-chip>
			</farm-col>
			<farm-col class="text-truncate" :style="flexBasisStyles[2]">
				<div>
					<farm-caption variation="regular" color="black" color-variation="30">
						Nfe
					</farm-caption>
					<farm-typography ellipsis color="gray" size="md" weight="700">
						{{ invoice.danfe || '-' }}
					</farm-typography>
				</div>
			</farm-col>
			<farm-col class="text-truncate" :style="flexBasisStyles[3]">
				<div>
					<farm-caption variation="regular" color="black" color-variation="30">
						Reprocessamento
					</farm-caption>
					<farm-typography ellipsis color="gray" size="md" weight="700">
						{{ invoice.tries || '-' }}
					</farm-typography>
				</div>
			</farm-col>
			<farm-col class="text-truncate" :style="flexBasisStyles[4]">
				<div :title="invoice.otReason || '-'">
					<farm-caption variation="regular" color="black" color-variation="30">
						Motivo OT
					</farm-caption>
					<farm-typography ellipsis color="gray" size="md" weight="700">
						{{ invoice.otReason || '-' }}
					</farm-typography>
				</div>
			</farm-col>
			<farm-col class="text-truncate pr-0" :style="flexBasisStyles[5]">
				<div :title="invoice.reason || '-'">
					<farm-caption variation="regular" color="black" color-variation="30">
						Motivo Exibido
					</farm-caption>
					<farm-typography ellipsis color="gray" size="md" weight="700">
						{{ invoice.reason || '-' }}
					</farm-typography>
				</div>
			</farm-col>
			<farm-col class="d-flex justify-end ml-0" :style="flexBasisStyles[6]">
				<farm-context-menu
					:items="[
						{
							label: 'Reprocessar',
							handler: 'reprocessInvoice',
							icon: { color: 'primary', type: 'rotate-left' },
						},
						{
							label: 'Detalhes',
							handler: 'showDetails',
							icon: { color: 'primary', type: 'open-in-new' },
						},
						{
							label: 'Remover',
							handler: 'removeInvoice',
							icon: { color: 'error', type: 'trash-can-outline' },
						},
					]"
					@showDetails="showDetails"
					@reprocessInvoice="openReprocessSingleInvoiceModal"
					@removeInvoice="openRemoveInvoiceModal"
				/>
			</farm-col>
		</farm-row>

		<ReprocessableInvoicesRowDetails
			:show="isDetailsOpen"
			:invoice="invoice"
			@closeDetails="isDetailsOpen = false"
		/>

		<farm-line noSpacing v-if="!isLastInvoice" />

		<ModalRemoveInvoice
			:show="removeInvoiceModal"
			@close="closeRemoveInvoiceModal"
			@confirm="confirmRemoveInvoice"
		/>

		<ModalReprocessSingleInvoice
			:show="reprocessSingleInvoiceModal"
			:id="invoice.danfe"
			@close="closeReprocessSingleInvoiceModal"
			@confirm="confirmReprocessSingleInvoice"
		/>
	</div>
</template>

<script lang="ts">
import { computed, ref, watch } from 'vue';

import { notification } from '@farm-investimentos/front-mfe-libs-ts';

import { useDeleteReprocessableInvoices } from '@/features/_composables/useDeleteReprocessableInvoices';
import { ReprocessableBatchesInvoice } from '@/features/_composables/useFetchReprocessableBatchesInvoices';
import { usePostReprocessableBatchesInvoices } from '@/features/_composables/usePostReprocessableBatchesInvoices';

import ModalRemoveInvoice from './ModalRemoveInvoice.vue';
import ModalReprocessSingleInvoice from './ModalReprocessSingleInvoice.vue';
import ReprocessableInvoicesRowDetails from './ReprocessableInvoicesRowDetails.vue';

export default {
	name: 'ReprocessableInvoicesRow',
	props: {
		productId: {
			type: String,
			required: true,
		},
		batchId: {
			type: Number,
			required: true,
		},
		invoice: {
			type: Object as () => ReprocessableBatchesInvoice,
			required: true,
		},
		isSelected: {
			type: Boolean,
			required: false,
		},
		isLastInvoice: {
			type: Boolean,
			required: false,
		},
	},
	components: {
		ReprocessableInvoicesRowDetails,
		ModalRemoveInvoice,
		ModalReprocessSingleInvoice,
	},
	setup(props, { emit }) {
		const isSelected = ref(props.isSelected);
		const removeInvoiceModal = ref(false);
		const reprocessSingleInvoiceModal = ref(false);
		const isDetailsOpen = ref(false);
		const statusOptions = {
			0: {
				color: 'warning',
				text: 'Pendente',
			},
			1: {
				color: 'info',
				text: 'Validando',
			},
			2: {
				color: 'error',
				text: 'Não Validado',
			},
		};
		const { postReprocessableBatchesInvoicesRequest } = usePostReprocessableBatchesInvoices();
		const { deleteReprocessableInvoicesRequest } = useDeleteReprocessableInvoices();
		const proportions = [0.5, 0.7, 2.8, 1, 1.5, 2.8, 0.7];
		const flexBasisStyles = generateFlexBasisStyles(proportions);

		const status = computed(() => statusOptions[props.invoice.status]);

		function generateFlexBasisStyles(sizes: number[]) {
			const total = sizes.reduce((sum, size) => sum + size, 0);
			return sizes.map(size => `flex-basis: ${(size / total) * 100}%;`);
		}

		async function reprocessInvoice() {
			await postReprocessableBatchesInvoicesRequest({
				productId: props.productId,
				batches: [
					{
						id: props.batchId,
						danfes: [props.invoice.danfe],
						danfesToNotReprocess: [],
						reprocessEntireBatch: false,
					},
				],
				reprocessAll: false,
			});

			emit('reprocessInvoice', {
				batchId: props.batchId,
				danfe: props.invoice.danfe,
			});
		}

		function showDetails() {
			isDetailsOpen.value = true;
		}

		function openRemoveInvoiceModal() {
			removeInvoiceModal.value = true;
		}

		function closeRemoveInvoiceModal() {
			removeInvoiceModal.value = false;
		}

		function closeReprocessSingleInvoiceModal() {
			reprocessSingleInvoiceModal.value = false;
		}

		async function confirmRemoveInvoice() {
			await removeInvoice();
			closeRemoveInvoiceModal();
			notification('SUCCESS', 'Recebível removido com sucesso!');
		}

		function openReprocessSingleInvoiceModal() {
			reprocessSingleInvoiceModal.value = true;
		}

		async function removeInvoice() {
			await deleteReprocessableInvoicesRequest({
				productId: props.productId,
				batches: [{ id: props.batchId, danfes: [props.invoice.danfe] }],
			});
			emit('removeInvoice', {
				batchId: props.batchId,
				danfe: props.invoice.danfe,
			});
		}

		async function confirmReprocessSingleInvoice() {
			await reprocessInvoice();
			closeReprocessSingleInvoiceModal();
			notification('SUCCESS', 'Reprocessamento em andamento!');
		}

		watch(isSelected, newValue => {
			emit('update:selected', { selected: !!newValue, danfe: props.invoice.danfe });
		});

		return {
			isDetailsOpen,
			isSelected,
			status,
			flexBasisStyles,
			reprocessInvoice,
			showDetails,
			removeInvoice,
			confirmReprocessSingleInvoice,
			reprocessSingleInvoiceModal,
			closeReprocessSingleInvoiceModal,
			removeInvoiceModal,
			openRemoveInvoiceModal,
			closeRemoveInvoiceModal,
			confirmRemoveInvoice,
			openReprocessSingleInvoiceModal,
		};
	},
};
</script>
