<template>
	<farm-modal v-model="show" :offsetTop="48" :offsetBottom="68" size="sm" @onClose="close">
		<template v-slot:header>
			<farm-dialog-header title="Reprocessar Recebível" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-caption class="mb-4">
				Tem certeza que deseja reprocessar o recebível
				<b>{{ id }}</b
				>? Você pode visualizar o processamento no histórico de importações no Banco de
				Recebíveis.
			</farm-caption>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Sim"
				closeLabel="Cancelar"
				@onConfirm="confirm"
				@onClose="close"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name: 'modal-cancel-cession',
	props: {
		show: {
			type: Boolean as () => boolean,
			required: true,
		},
		id: {
			type: String as () => string,
			required: false,
			default: 0,
		},
	},
	setup(props, { emit }) {
		const inputModel = ref(false);

		function close() {
			emit('close');
		}

		function confirm() {
			emit('confirm');
		}

		return {
			inputModel,
			confirm,
			close,
		};
	},
});
</script>
