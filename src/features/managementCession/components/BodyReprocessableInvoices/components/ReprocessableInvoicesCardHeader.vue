<template>
	<farm-card class="mb-4">
		<farm-card-content gutter="md">
			<farm-row>
				<farm-col class="d-flex justify-center align-center" :style="flexBasisStyles[0]">
					<div>
						<farm-checkbox
							v-model="isSelectAllChecked"
							:value="true"
							size="sm"
							class="d-flex justify-center"
						/>
						<farm-caption
							class="mt-1"
							variation="regular"
							color="black"
							color-variation="30"
						>
							{{ selectedAmount }}
							/
							{{ info.invoices }}
						</farm-caption>
					</div>
				</farm-col>
				<farm-col class="text-truncate pl-0" :style="flexBasisStyles[1]">
					<farm-caption variation="regular" color="black" color-variation="30">
						ID
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">{{ propsInfo.id }}</farm-subtitle>
				</farm-col>
				<farm-col class="text-truncate" :style="flexBasisStyles[2]">
					<farm-caption variation="regular" color="black" color-variation="30">
						Nome do Arquivo
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">{{
						propsInfo.filename
					}}</farm-subtitle>
				</farm-col>
				<farm-col :style="flexBasisStyles[3]">
					<farm-caption variation="regular" color="black" color-variation="30">
						Data de Envio
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ formatDate(info.createdAt) }}
					</farm-subtitle>
				</farm-col>
				<farm-col :style="flexBasisStyles[4]">
					<farm-caption variation="regular" color="black" color-variation="30">
						Recebíveis para Reprocessamento
					</farm-caption>
					<farm-subtitle ellipsis variation="medium">
						{{ info.invoices }}
					</farm-subtitle>
				</farm-col>
				<farm-col class="d-flex justify-end align-center pr-0" :style="flexBasisStyles[5]">
					<farm-chip
						:color="status[info.status].color"
						:dense="true"
						style="width: 106px"
					>
						{{ status[info.status].text }}
					</farm-chip>
				</farm-col>
				<farm-col class="d-flex justify-end pl-0" :style="flexBasisStyles[6]">
					<farm-btn icon @click="isOpen = !isOpen">
						<farm-icon size="md">chevron-down</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-card-content>
		<collapse-transition :duration="300" v-show="isOpen">
			<ReprocessableInvoicesListing
				:is-visible="isOpen"
				:product-id="productId"
				:batch-id="info.id"
				:nfe-search="nfeSearch"
				:should-update-state="shouldUpdateState"
				:is-select-all-checked="isSelectAllChecked"
				@removeInvoice="$emit('removeInvoice', $event)"
				@reprocessInvoice="reprocessInvoice"
				@update:selected="updateSelectedList"
			/>
		</collapse-transition>
	</farm-card>
</template>

<script lang="ts">
import { ref, watch, nextTick } from 'vue';

import { ReprocessableBatch } from '@/features/_composables/useFetchReprocessableBatches';
import { ReprocessableBatchesInvoice } from '@/features/_composables/useFetchReprocessableBatchesInvoices';

import ReprocessableInvoicesListing, { SelectedInvoice } from './ReprocessableInvoicesListing.vue';

type SelectedInvoicesEvent = {
	danfes: SelectedInvoice[];
	danfesToNotReprocess: SelectedInvoice[];
	isAllSelected: boolean;
};

export default {
	name: 'ReprocessableInvoiceCardHeader',
	components: {
		ReprocessableInvoicesListing,
	},
	props: {
		info: {
			type: Object as () => ReprocessableBatch,
			required: true,
		},
		productId: {
			type: String as () => string,
			required: true,
		},
		isSelectAllChecked: {
			type: Boolean as () => boolean,
			required: true,
		},
		shouldUpdateState: {
			type: Boolean as () => boolean,
			required: false,
			default: false,
		},
		nfeSearch: {
			type: String as () => string,
			required: false,
			default: null,
		},
	},
	setup(props, { emit }) {
		const propsInfo = props.info;
		const isOpen = ref(false);
		const isSelectAllChecked = ref(props.isSelectAllChecked);
		const selectedDanfesList = ref<SelectedInvoice[]>([]);
		const selectedAmount = ref(0);
		const selectedDanfesToNotReprocessList = ref<SelectedInvoice[]>([]);
		const formatDate = (date: string) =>
			new Date(date)
				.toLocaleString('pt-BR', {
					day: '2-digit',
					month: '2-digit',
					year: 'numeric',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit',
					hour12: false,
				})
				.replace(',', ' às');
		const status = {
			1: {
				color: 'warning',
				text: 'Em andamento',
			},
			2: {
				color: 'info',
				text: 'Validando',
			},
			3: {
				color: 'error',
				text: 'Erro',
			},
			4: {
				color: 'success',
				text: 'Concluído',
			},
		};
		const reprocessableBatchesInvoices = ref<ReprocessableBatchesInvoice[]>([]);
		const proportions = [0.52, 0.71, 2.45, 2, 3, 1.5, 0.5];
		const flexBasisStyles = generateFlexBasisStyles(proportions);

		function generateFlexBasisStyles(sizes: number[]) {
			const total = sizes.reduce((sum, size) => sum + size, 0);
			return sizes.map(size => `flex-basis: ${(size / total) * 100}%;`);
		}

		function updateSelectedList(
			selectedInvoicesEvent: SelectedInvoicesEvent,
			oldSelectedInvoices: SelectedInvoicesEvent
		) {
			const totalInvoices = +props.info.invoices;

			if (isSelectAllChecked.value) {
				selectedAmount.value =
					totalInvoices - selectedInvoicesEvent.danfesToNotReprocess.length;
			} else {
				selectedAmount.value = selectedInvoicesEvent.danfes.length;
			}

			const oldAmount = oldSelectedInvoices.isAllSelected
				? totalInvoices - oldSelectedInvoices.danfesToNotReprocess.length
				: oldSelectedInvoices.danfes.length;

			const difference = selectedAmount.value - oldAmount;

			selectedDanfesList.value = selectedInvoicesEvent.danfes;
			selectedDanfesToNotReprocessList.value = selectedInvoicesEvent.danfesToNotReprocess;

			emit('update:selected', {
				isAllSelected: isSelectAllChecked.value,
				id: props.info.id,
				danfes: isSelectAllChecked.value ? [] : selectedInvoicesEvent.danfes,
				danfesToNotReprocess: isSelectAllChecked.value
					? selectedInvoicesEvent.danfesToNotReprocess
					: [],
				incrementInvoicesBy: difference,
				totalInvoices: totalInvoices,
				selectedAmount: selectedAmount.value,
				reprocessEntireBatch: isSelectAllChecked.value,
			});
		}

		function reprocessInvoice(event: { batchId: number; danfe: string }) {
			selectedDanfesList.value = [];
			selectedDanfesToNotReprocessList.value = [];

			nextTick(() => {
				const totalInvoices = +props.info.invoices;
				const incrementValue = -selectedAmount.value;

				isSelectAllChecked.value = false;
				selectedAmount.value = selectedDanfesList.value.length;

				emit('update:selected', {
					isAllSelected: false,
					id: props.info.id,
					danfes: selectedDanfesList.value,
					danfesToNotReprocess: [],
					incrementInvoicesBy: incrementValue,
					totalInvoices: totalInvoices,
					selectedAmount: selectedAmount.value,
					reprocessEntireBatch: false,
				});
				emit('reprocessInvoice', event);
			});
		}

		watch(
			() => isSelectAllChecked.value,
			newValue => {
				if (!newValue) {
					selectedDanfesList.value = [];
					selectedAmount.value = 0;
					selectedDanfesToNotReprocessList.value = [];
				}

				const totalInvoices = +props.info.invoices;
				const incrementValue = newValue
					? totalInvoices - selectedDanfesToNotReprocessList.value.length
					: -selectedAmount.value;

				selectedAmount.value = newValue
					? totalInvoices - selectedDanfesToNotReprocessList.value.length
					: 0;

				emit('update:selected', {
					isAllSelected: newValue,
					id: props.info.id,
					danfes: newValue ? [] : selectedDanfesList.value,
					danfesToNotReprocess: [],
					incrementInvoicesBy: incrementValue,
					totalInvoices: totalInvoices,
					selectedAmount: selectedAmount.value,
					reprocessEntireBatch: newValue,
				});
			}
		);

		watch(
			() => props.shouldUpdateState,
			(value: boolean) => {
				if (value) {
					isSelectAllChecked.value = false;
					selectedDanfesList.value = [];
					selectedDanfesToNotReprocessList.value = [];
				}
			}
		);

		watch(
			() => selectedAmount.value,
			(value: number) => {
				if (value === 0) {
					isSelectAllChecked.value = false;
				} else if (value === +props.info.invoices) {
					isSelectAllChecked.value = true;
				}
			}
		);

		return {
			propsInfo,
			isOpen,
			isSelectAllChecked,
			selectedDanfesList,
			selectedDanfesToNotReprocessList,
			status,
			selectedAmount,
			reprocessableBatchesInvoices,
			flexBasisStyles,
			formatDate,
			updateSelectedList,
			reprocessInvoice,
		};
	},
};
</script>
