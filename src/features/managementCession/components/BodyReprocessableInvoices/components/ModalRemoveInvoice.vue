<template>
	<farm-modal v-model="show" :offsetTop="48" :offsetBottom="68" size="sm" @onClose="close">
		<template v-slot:header>
			<farm-dialog-header title="Remover Recebível" @onClose="close" />
		</template>
		<template v-slot:content>
			<farm-caption>
				Deseja realmente remover o recebível da lista de reprocessamento? <br />Ela deverá
				ser submetido a outra importação para ser processado novamente.
			</farm-caption>

			<farm-caption class="mt-2">
				Escreva no campo abaixo <b>REMOVER</b> para confirmar a remoção da recebível.
			</farm-caption>
			<farm-promptusertoconfirm v-model="inputModel" match="REMOVER" title="" />
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Sim"
				closeLabel="Cancelar"
				:isConfirmDisabled="!inputModel"
				@onConfirm="confirm"
				@onClose="close"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name: 'modal-cancel-cession',
	props: {
		show: {
			type: Boolean,
			required: true,
		},
	},
	setup(props, { emit }) {
		const inputModel = ref(false);

		function close() {
			emit('close');
		}

		function confirm() {
			emit('confirm');
		}

		return {
			inputModel,
			confirm,
			close,
		};
	},
});
</script>
