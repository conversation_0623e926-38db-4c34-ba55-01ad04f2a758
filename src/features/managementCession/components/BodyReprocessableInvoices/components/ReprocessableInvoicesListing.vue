<template>
	<div>
		<farm-card-content background="base">
			<ReprocessableInvoicesRow
				v-for="(invoice, index) in reprocessableInvoices"
				:batch-id="batchId"
				:product-id="productId"
				:invoice="invoice"
				:key="uniqueId(invoice.danfe)"
				:is-selected="
					(seletectedInvoices.find(selected => selected.danfe === invoice.danfe) || {})
						.selected
				"
				:is-last-invoice="isLastInvoice(index)"
				@reprocessInvoice="reprocessInvoice"
				@removeInvoice="removeInvoice"
				@update:selected="updateSelected"
			/>
		</farm-card-content>
		<farm-card-content background="base" class="px-8 py-0">
			<farm-row extra-decrease v-if="reprocessableInvoices.length">
				<farm-box>
					<farm-datatable-paginator
						:page="pagination.page"
						:totalPages="pagination.totalPages"
						@onChangePage="changePage"
						@onChangeLimitPerPage="changePageLimit"
					/>
				</farm-box>
			</farm-row>
		</farm-card-content>
	</div>
</template>

<script lang="ts">
import { computed, ref, watch } from 'vue';

import {
	ReprocessableBatchesInvoice,
	useFetchReprocessableBatchesInvoices,
} from '@/features/_composables/useFetchReprocessableBatchesInvoices';

import ReprocessableInvoicesRow from './ReprocessableInvoicesListingRow.vue';

export type SelectedInvoice = {
	selected: boolean;
	danfe: string;
};

export default {
	name: 'ReprocessableInvoicesListing',
	components: {
		ReprocessableInvoicesRow,
	},
	props: {
		isVisible: {
			type: Boolean as () => boolean,
			required: true,
		},
		productId: {
			type: String as () => string,
			required: true,
		},
		batchId: {
			type: Number as () => number,
			required: true,
		},
		shouldUpdateState: {
			type: Boolean as () => boolean,
			required: false,
			default: false,
		},
		nfeSearch: {
			type: String as () => string,
			required: false,
			default: null,
		},
		isSelectAllChecked: {
			type: Boolean as () => boolean,
			required: false,
		},
	},
	setup(props, { emit }) {
		const uniqueId = (id: string) =>
			`${id}-${Date.now().toString(36) + Math.random().toString(36).slice(2, 7)}`;
		const reprocessableInvoices = ref<ReprocessableBatchesInvoice[]>([]);
		const { data, fetchReprocessableBatchesInvoices } = useFetchReprocessableBatchesInvoices();
		const isLastInvoice = (index: number) => index === reprocessableInvoices.value.length - 1;
		const isVisibleComputed = computed(() => props.isVisible);
		const pagination = ref({ page: 1, size: 0, totalPages: null, limit: 10, totalItems: 0 });
		const seletectedInvoices = ref<SelectedInvoice[]>([]);
		const selectedInvoicesToNotReprocess = ref<SelectedInvoice[]>([]);

		async function loadReprocessableInvoices() {
			await fetchReprocessableBatchesInvoices({
				productId: props.productId,
				batchId: props.batchId,
				filters: {
					limit: pagination.value.limit,
					page: pagination.value.page - 1,
					order: 'ASC',
					...(props.nfeSearch && { search: props.nfeSearch }),
				},
			});

			pagination.value.totalPages = data.value.totalPages;
			pagination.value.totalItems = data.value.totalItems;

			reprocessableInvoices.value = data.value.content;

			if (props.isSelectAllChecked) {
				setSelectedInvoicesIfSelectAllChecked();
			}
		}

		async function changePage(page: number) {
			pagination.value.page = page;

			await loadReprocessableInvoices();
		}

		function changePageLimit(limit: number) {
			pagination.value.limit = limit;
			pagination.value.page = 1;

			loadReprocessableInvoices();
		}

		function reprocessInvoice(event: { batchId: number; danfe: string }) {
			loadReprocessableInvoices();
			emit('reprocessInvoice', event);
		}

		function removeInvoice(event: { batchId: number; danfe: string }) {
			loadReprocessableInvoices();
			updateSelected({ danfe: event.danfe, selected: false });
			emit('removeInvoice', event);
		}

		function updateSelected(selectionEvent: SelectedInvoice) {
			const oldSelected = [...seletectedInvoices.value];
			const oldNotReprocess = [...selectedInvoicesToNotReprocess.value];

			if (selectionEvent.selected) {
				const index = selectedInvoicesToNotReprocess.value.findIndex(
					invoice => invoice.danfe === selectionEvent.danfe
				);
				if (index > -1) {
					selectedInvoicesToNotReprocess.value.splice(index, 1);
				}
				seletectedInvoices.value.push(selectionEvent);
			} else {
				const index = seletectedInvoices.value.findIndex(
					invoice => invoice.danfe === selectionEvent.danfe
				);
				if (index > -1) {
					seletectedInvoices.value.splice(index, 1);
				}
				selectedInvoicesToNotReprocess.value.push(selectionEvent);
			}

			const invoices = Array.from(
				new Map(seletectedInvoices.value.map(item => [item.danfe, item])).values()
			);
			const invoicesToNotReprocess = Array.from(
				new Map(
					selectedInvoicesToNotReprocess.value.map(item => [item.danfe, item])
				).values()
			);

			emit(
				'update:selected',
				{
					danfes: invoices,
					danfesToNotReprocess: invoicesToNotReprocess,
					isAllSelected: props.isSelectAllChecked,
				},
				{
					danfes: oldSelected,
					danfesToNotReprocess: oldNotReprocess,
					isAllSelected: props.isSelectAllChecked,
				}
			);
		}

		function setSelectedInvoicesIfSelectAllChecked() {
			if (props.isSelectAllChecked) {
				const newInvoices = reprocessableInvoices.value
					.filter(
						invoice =>
							!selectedInvoicesToNotReprocess.value.some(
								notReprocessInvoice => notReprocessInvoice.danfe === invoice.danfe
							)
					)
					.map(invoice => ({
						selected: true,
						danfe: invoice.danfe,
					}));

				seletectedInvoices.value = Array.from(
					new Map(
						[...seletectedInvoices.value, ...newInvoices].map(item => [
							item.danfe,
							item,
						])
					).values()
				);
			} else {
				seletectedInvoices.value = [];
			}
		}

		watch(isVisibleComputed, (value: boolean) => {
			if (value) loadReprocessableInvoices();
		});

		watch(
			() => props.shouldUpdateState,
			(value: boolean) => {
				if (value) {
					seletectedInvoices.value = [];
					loadReprocessableInvoices();
				}
			}
		);

		watch(
			() => props.nfeSearch,
			() => {
				loadReprocessableInvoices();
			}
		);

		watch(
			() => props.isSelectAllChecked,
			value => {
				if (!value) {
					selectedInvoicesToNotReprocess.value = [];
					seletectedInvoices.value = [];
				}
				setSelectedInvoicesIfSelectAllChecked();
			}
		);

		return {
			reprocessableInvoices,
			pagination,
			changePage,
			changePageLimit,
			uniqueId,
			isLastInvoice,
			reprocessInvoice,
			removeInvoice,
			updateSelected,
			seletectedInvoices,
			selectedInvoicesToNotReprocess,
		};
	},
};
</script>
