<template>
	<div class="header-form">
		<farm-idcaption
			tooltipColor="gray"
			v-for="(item, index) in data"
			:key="`header-form-${item.id}-${index}`"
			:icon="item.icon"
			:hide-copy-btn="hideCopyBtn"
			:copyText="item.copyText"
			:class="{
				'header-form-item': true,
				'with-line': index !== data.length - 1,
			}"
			:successMessage="messageSucess"
		>
			<template v-slot:title v-if="item.title">
				{{ item.title }}
			</template>
			<template v-slot:subtitle>
				{{ item.subtitle }}: {{ item.value || 'Carregando...' }}
			</template>
		</farm-idcaption>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { HeaderFormTypes } from './types';

export default defineComponent({
	props: {
		data: {
			type: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>] as PropType<HeaderFormTypes>,
			required: true,
		},
		messageSucess: {
			type: String,
			default: 'Raiz copiada para área de transferência!',
		},
		hideCopyBtn: {
			type: Boolean,
			default: false,
		},
	},
});
</script>

<style lang="scss" scoped>
@import './HeaderFormList';
</style>
