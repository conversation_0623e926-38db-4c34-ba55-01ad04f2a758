<template>
	<Cards>
		<template slot="header">
			<farm-row align="center">
				<farm-col cols="8">
					<CardTitleHeader :value="data.name" class="mb-1" />
				</farm-col>
				<farm-col cols="4" align="end">
					<farm-btn icon @click="() => redirectToHistory(data.productId)">
						<farm-icon size="lg">chevron-right</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row>
				<farm-col cols="6">
					<CardTextBody label="ID" :value="data.productId" />
				</farm-col>
				<farm-col cols="6">
					<CardTextBody label="Total de Recebíveis" :value="data.quantity" />
				</farm-col>
			</farm-row>
		</template>
	</Cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import CardListTextHeader from '@/components/CardListTextHeader';
import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import Cards from '@/components/Cards';
import { useRouter } from '@/composible';

export default defineComponent({
	name: 'receivables-history-card',
	components: {
		Cards,
		CardTextBody,
		CardTitleHeader,
		CardListTextHeader,
	},
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	setup() {
		const router = useRouter();
		function redirectToHistory(productId) {
			router.push(`/admin/wallet/gestao/${productId}/history`);
		}
		return { redirectToHistory };
	},
});
</script>
