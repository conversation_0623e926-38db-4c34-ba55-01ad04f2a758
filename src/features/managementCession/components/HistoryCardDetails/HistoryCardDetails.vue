<template>
	<farm-row extraDecrease style="background-color: #f5f5f5" class="px-2 py-4">
		<farm-col md="1">
			<farm-caption variation="regular" color="gray">Número</farm-caption>

			<farm-bodytext :type="2" bold color="gray" variation="medium">{{
				data.number
			}}</farm-bodytext>
		</farm-col>
		<farm-col md="2">
			<farm-caption variation="regular" color="gray">Sacado</farm-caption>

			<farm-bodytext :type="2" bold color="gray" variation="medium" ellipsis>{{
				data.draweeName
			}}</farm-bodytext>
		</farm-col>
		<farm-col md="2">
			<farm-caption variation="regular" color="gray">CPF/CNPJ</farm-caption>

			<farm-bodytext :type="2" bold color="gray" variation="medium">{{
				data.draweeDocument
			}}</farm-bodytext>
		</farm-col>
		<farm-col md="2">
			<farm-caption variation="regular" color="gray">Data de Emissão</farm-caption>

			<farm-bodytext :type="2" bold color="gray" variation="medium">{{
				defaultDateFormat(data.emisionDate)
			}}</farm-bodytext>
		</farm-col>

		<farm-col md="2">
			<farm-caption variation="regular" color="gray">Data de Vencimento</farm-caption>
			<farm-bodytext :type="2" bold color="gray" variation="medium">{{
				defaultDateFormat(data.expirationDate)
			}}</farm-bodytext>
		</farm-col>

		<farm-col md="2">
			<farm-caption variation="regular" color="gray">Valor Nominal</farm-caption>
			<farm-bodytext :type="2" bold color="gray" variation="medium">
				{{ formatMoney(data.value) }}
			</farm-bodytext>
		</farm-col>

		<farm-col cols="1">
			<farm-btn icon @click="onClick"><farm-icon size="md"> history</farm-icon></farm-btn>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { defaultDateFormat, brl as formatMoney } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	name: 'history-card-details',
	props: {
		data: {
			type: Object,
			required: true,
		},
	},

	setup(props, { emit }) {
		function onClick() {
			emit('onClick', props.data);
		}
		return { defaultDateFormat, formatMoney, onClick };
	},
});
</script>
