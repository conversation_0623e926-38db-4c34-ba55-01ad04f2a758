<template>
	<farm-container>
		<tabs-form
			:tabList="tabsList"
			:valueDefault="valueDefault"
			@onUpdateCurrentTab="onUpdateCurrentTab"
		/>
		<tab-cancel v-if="isTabCancel" />
		<tab-receivables-history v-if="isTabReceivablesHistory" />
		<tab-reprocess-receivables v-if="isTabReprocessReceivables" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';

import TabsForm from '@/components/TabsForm';
import { tabsManagementCession } from '@/features/managementCession/configurations';
import {
	CANCEL,
	RECEIVABLES_HISTORY,
	REPROCESS_RECEIVABLES,
} from '@/features/managementCession/constants';

import TabCancel from '../TabCancel';
import TabReceivablesHistory from '../TabReceivablesHistory';
import TabReprocessReceivables from '../TabReprocessReceivables';

export default defineComponent({
	name: 'body-home',
	components: {
		TabCancel,
		TabsForm,
		TabReceivablesHistory,
		TabReprocessReceivables,
	},
	setup() {
		const tabsList = ref([]);
		const currentTab = ref(CANCEL);
		const valueDefault = ref(CANCEL);
		const isTabCancel = computed(() => currentTab.value === CANCEL);
		const isTabReceivablesHistory = computed(() => currentTab.value === RECEIVABLES_HISTORY);

		const isTabReprocessReceivables = computed(
			() => currentTab.value === REPROCESS_RECEIVABLES
		);

		function onChangeTab(): void {
			tabsList.value = [...tabsManagementCession];
		}

		function onUpdateCurrentTab(value: string): void {
			currentTab.value = value;
		}

		function load(): void {
			tabsList.value = [...tabsManagementCession];
		}

		onMounted(() => {
			load();
		});

		return {
			isTabCancel,
			isTabReceivablesHistory,
			isTabReprocessReceivables,
			tabsList,
			currentTab,
			valueDefault,
			onChangeTab,
			onUpdateCurrentTab,
		};
	},
});
</script>
