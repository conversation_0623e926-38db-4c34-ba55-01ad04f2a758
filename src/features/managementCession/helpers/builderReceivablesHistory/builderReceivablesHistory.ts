export function builderReceivablesHistory(response) {
	let content = null;
	if (response && response.content) {
		content = response.content.map(item => {
			return {
				id: `${item.productName}-${item}`,
				productId: item.productId,
				quantity: item.quantity,
				name: item.productName,
			};
		});
	}

	return {
		content: content ? content : [],
		total: response.pageable.totalPages,
		pagination: {
			pageNumber: response.pageable.pageNumber || 0,
			pageSize: response.pageable.pageSize || 0,
			sort: null,
			totalElements: response.pageable.totalElements || 0,
			totalPages: response.pageable.totalPages || 0,
		},
	};
}
