import { formatDateAndHours } from '@/helpers/formatCards/formatCards';

import { logItemMapper } from '../../configurations/logMapper';

export function builderReceivablesHistoryLog(response) {
	return response.map(item => ({
		icon: logItemMapper[item.idReceivableEventStatus.toString()].icon,
		message: item.description,
		userName: item.user,
		formattedDate: formatDateAndHours(item.createdAt),
		status: logItemMapper[item.idReceivableEventStatus.toString()].status,
	}));
}
