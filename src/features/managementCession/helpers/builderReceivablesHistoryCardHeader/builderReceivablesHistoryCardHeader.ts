export const builderReceivablesHistoryCardHeader = data => {
	const filtered = data.content?.map(x => {
		return {
			productId: x.productId || x.id,
			productName: x.productName ?? x.productName,
			providerDocument: x.providerDocument || x.draweeDocument,
			providerName: x.providerName || x.draweeName,
			quantity: x.quantity || x.totalInstallments,
		};
	});

	const pagination = data.pageable || { pageNumber: 0, pageSize: 10 };
	const content = filtered || [];
	return {
		content,
		pagination,
	};
};
