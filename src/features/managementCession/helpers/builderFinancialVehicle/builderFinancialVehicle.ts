
function formatDate(data): string {
	if(!data){
		return 'N/A';
	}
	return `${data.updatedAt.split(' ')[0]}`;
}

function formatHours(data): string {
	if(!data){
		return 'N/A';
	}
	const time = data.updatedAt.split(' ')[1];
	const hours = time.split(':')[0];
	const minutes = time.split(':')[1];
	return `${hours}:${minutes}`;
}

export function builderFinancialVehicle(response) {
	const { content } = response.data;
	const newData = content.map((item)=>{
		return {
			id: item.id,
			name: item.name,
			value: item.minimumValue
		};
	});

	return {
		content: newData,
		total: response.data.totalPages,
		pagination:{
			pageNumber: response.data.page,
			pageSize: response.data.size,
			sort: null,
			totalElements: response.data.totalItems,
			totalPages: response.data.totalPages,
		},
		meta: {
			updatedAt: formatDate(response.data.meta),
			updatedBy: response.data.meta.updatedBy,
			updatedHours: formatHours(response.data.meta)
		}
	};

}
