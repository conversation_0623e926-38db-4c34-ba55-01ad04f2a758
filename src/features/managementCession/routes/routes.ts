import ManagementCessionHistoryHome from '../views/History';
import ManagementCessionHome from '../views/Home';
import ManagementCessionReprocessableInvoices from '../views/ReprocessableInvoices';

export const routes = [
	{
		path: 'gestao',
		name: 'ManagementCessionHome',
		component: ManagementCessionHome,
		meta: {
			title: 'Gest<PERSON>',
			icon: 'cog',
			roleKey: 'wallet.gestao',
		},
	},
	{
		path: 'gestao/:productId/history',
		name: 'ManagementReceivableHistoryHome',
		component: ManagementCessionHistoryHome,
		meta: {
			title: 'Histórico de Recebíveis',
			icon: 'history',
			roleKey: 'wallet.gestao',
		},
	},
	{
		path: 'gestao/:productId/reprocessable-invoices',
		name: 'ManagementReceivableReprocessableInvoices',
		component: ManagementCessionReprocessableInvoices,
		meta: {
			title: 'Reprocessar Recebíveis',
			icon: 'history',
			roleKey: 'wallet.gestao',
		},
	},
];
