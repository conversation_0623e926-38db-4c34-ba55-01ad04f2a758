
export function builderProviders(response) {
	const { content } = response.data;
	const newData = content.map((item) => {
		return {
			id: item.id,
			productName: item.name,
			document: item.document,
			amountToPay: item.value,
			type: item.type,
			accountType: item.accountType,
			bank: item.bank,
			agency: item.agency,
			accountNumber: item.account,
			key: item.key,
			pixType: item.pixType,
			percentage: item.percentage || 0
		};
	});
	return newData;
}
