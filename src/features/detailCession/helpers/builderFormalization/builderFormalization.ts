import { v4 as uuidv4 } from 'uuid';
function addIdSignatories(data){
	const newSignatories= data.map((item) => {
		return {
			id: uuidv4(),
			name: item.name,
			email: item.email,
			phoneNumber: item.phoneNumber,
			signatureLink: item.signatureLink,
			farmStatusCode: item.farmStatusCode,
		};
	});
	return newSignatories;
}

function addIdDocument(data){
	const newDocument = data.map((item) => {
		return {
			id: uuidv4(),
			documentName: item.documentName,
			link: item.link,
			signatories:addIdSignatories(item.signatories),
			downloadLink: item.downloadLink
		};
	});
	return newDocument;
}

export function builderFormalization(response) {
	const data = response.data;
	const newData = data.map((item) => {
		return {
			id: uuidv4(),
			title: item.title,
			emissionDate: item.emissionDate,
			dueDate: item.dueDate,
			documents: addIdDocument(item.documents),
			numberOfSignatories: item.numberOfSignatories,
			numberOfSignedSignatories: item.numberOfSignedSignatories
		};
	});
	return newData;
}
