import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

const status ={
	1: 'info',
	2: 'success',
	3: 'error'
};

export function builderLogger(response) {
	const { content } = response.data;
	const newData = content.map((item) => {
		return {
			id: item.id,
			message: item.title,
			extraMessage: item.description,
			formattedDate: defaultDateFormat(item.createdAt),
			status: status[item.statusId],
		};
	});
	return newData;
}
