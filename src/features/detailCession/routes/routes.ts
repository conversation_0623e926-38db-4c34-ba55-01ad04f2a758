
import StepFourCessionCompleted from "../views/StepFourCessionCompleted";
import StepOneAnalyzingCession from "../views/StepOneAnalyzingCession";
import StepThreeCessionInDisbursement from "../views/StepThreeCessionInDisbursement";
import StepTwoWaitingFormalization from "../views/StepTwoWaitingFormalization";

export const routes = [
	{
		path: 'detalhe_cessao/analisando_cessao/:id',
		name: 'StepOneAnalyzingCession',
		component: StepOneAnalyzingCession,
		meta: {
			title: 'Detalhes da Cessão',
			icon: 'clipboard-text',
			roleKey: 'wallet.cessao',
		},
	},
	{
		path: 'detalhe_cessao/aguardando_formalizacao/:id',
		name: 'StepTwoWaitingFormalization',
		component: StepTwoWaitingFormalization,
		meta: {
			title: 'Detalhes da Cessão',
			icon: 'clipboard-text',
			roleKey: 'wallet.cessao',
		},
	},
	{
		path: 'detalhe_cessao/cessao_em_desembolso/:id',
		name: 'StepThreeCessionInDisbursement',
		component: StepThreeCessionInDisbursement,
		meta: {
			title: 'Detalhes da Cessão',
			icon: 'clipboard-text',
			roleKey: 'wallet.cessao',
		},
	},
	{
		path: 'detalhe_cessao/cessao_concluida/:id',
		name: 'StepFourCessionCompleted',
		component: StepFourCessionCompleted,
		meta: {
			title: 'Detalhes da Cessão',
			icon: 'clipboard-text',
			roleKey: 'wallet.cessao',
		},
	},

];
