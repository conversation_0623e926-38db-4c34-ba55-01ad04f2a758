<template>
	<farm-card class="mb-4">
		<farm-card-content>
			<div class="formalization-card-flex">
				<div class="formalization-card-information">
					<div>
						<farm-bodytext variation="bold" :type="1">
							{{ data.title }}
						</farm-bodytext>
					</div>
					<div>
						<card-list-text-header noSpacing :data="listDates" />
					</div>
				</div>
				<div class="formalization-card-count">
					<farm-bodytext variation="bold" :type="2">
						<b>{{ data.numberOfSignedSignatories || 0 }}</b> de
						<b>{{ data.numberOfSignatories || 0 }}</b> Assinado(s)
					</farm-bodytext>
				</div>
			</div>
		</farm-card-content>
		<farm-line no-spacing />
		<farm-card-content>
			<formalization-documents-list
				:data="data.documents"
				:isHiddenOptions="isHiddenOptions"
			/>
		</farm-card-content>
	</farm-card>
</template>
<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import { defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import CardListTextHeader from '@/components/CardListTextHeader';

import FormalizationDocumentsList from '../FormalizationDocumentsList';

export default defineComponent({
	name: 'formalization-card',
	components: {
		CardListTextHeader,
		FormalizationDocumentsList,
	},
	props: {
		data: {
			type: Object,
			require: true,
		},
		isHiddenOptions: {
			type: Boolean,
			default: false,
		},
	},
	setup(props) {
		const { data, isHiddenOptions } = toRefs(props);

		const listDates = [
			{
				id: null,
				label: 'Data de Emissão',
				value: defaultDateFormat(data.value.emissionDate) || null,
				copyText: '',
			},
			{
				id: null,
				label: 'Data de Vencimento',
				value: defaultDateFormat(data.value.dueDate) || null,
				copyText: '',
			},
		];

		return {
			listDates,
			isHiddenOptions,
		};
	},
});
</script>
<style lang="scss" scoped>
@import 'FormalizationCard';
</style>
