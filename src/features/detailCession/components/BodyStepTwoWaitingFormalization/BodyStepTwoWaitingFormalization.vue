<template>
	<farm-container>
		<header-cession
			class="mb-4"
			title="Detalhes da Cessão"
			:currentStep="2"
			:data="headerSteps"
			:is-refused="isRefused || isCancel"
		/>
		<farm-row class="mb-4" v-if="!isError">
			<farm-col cols="12" v-if="isUpdatedCession && !isCancel && !isRefused">
				<feedback-alert color="info">
					Sua cessão sofreu algumas <b>alterações</b> por conta da análise dos recebíveis.
					<b>Verifique</b> as informações abaixo.
				</feedback-alert>
			</farm-col>
			<farm-row v-if="isRefused">
				<farm-col cols="12">
					<feedback-alert color="error">
						Sua cessão foi recusada
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-col cols="12" v-if="isCancel">
				<feedback-alert color="error">
					Sua cessão foi cancelada pelo usuário <b> {{ dataCancel.name }} </b> no dia
					<b>{{ dataCancel.date }}</b
					>.
				</feedback-alert>
			</farm-col>
			<farm-col cols="12" v-if="!isCancel && !isRefused">
				<feedback-alert>
					Estamos aguardando a confirmação de formalização para seguirmos com a cessão.
				</feedback-alert>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<information-cession-list
					:dateDisbursement="headerData.dateDisbursement"
					:limit="headerData.limit"
					:valueDisbursement="headerData.valueDisbursement"
					:productName="headerData.productName"
					:typeOperation="headerData.typeOperation"
					@onClickDisbursement="onOpenModalDetailsCession"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="!isError">
			<farm-line />
		</farm-row>
		<farm-row class="mt-6" v-if="!isError">
			<farm-col cols="12">
				<tabs-content
					v-if="showTabs"
					isFormalization
					:isHiddenPayment="isRefused || isCancel"
					:typePayment="typePayment"
					:financialVehicleId="financialVehicleId"
					:approved="headerData.approved"
					:notApproved="headerData.notApproved"
				/>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-cession @onClickFooter="onClickFooter" />
		<modal-detail-cession
			v-model="isModalDetailCession"
			v-if="isModalDetailCession"
			:data="dataModalDetailCession"
			:receivables="false"
			@onClose="onCloseModalDetailCession"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import FeedbackAlert from '@/components/FeedbackAlert';
import InformationCessionList from '@/components/InformationCessionList';
import ModalDetailCession from '@/components/ModalDetailCession';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';
import useRoute from '@/composible/useRoute';

import { useDetailCession } from '../../composables/useDetailCession';
import { useRedirect } from '../../composables/useRedirect';
import Footer from '../Footer';
import Header from '../Header';
import TabsContent from '../TabsContent';

export default defineComponent({
	components:{
		'header-cession': Header,
		'footer-cession': Footer,
		InformationCessionList,
		ModalDetailCession,
		TabsContent,
		FeedbackAlert
	},
	setup() {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const {
			isOpenModal: isModalDetailCession,
			onCloseModal: onCloseModalDetailCession,
			onOpenModal: openModalDetailCession
		} = useModal();
		const { redirectHome } = useRedirect();
		const {
			detailCession,
			getDetailCession,
			isLoadingDetailCession,
			isErrorDetailCession
		} = useDetailCession();

		const cessionId = route.params.id;
		const CANCEL_STATUS = 8;
		const REFUSED_STATUS = 7;

		const isLoading = computed(() => {
			return isLoadingDetailCession.value;
		});
		const isError = computed(() => {
			return isErrorDetailCession.value;
		});

		const showTabs = ref(false);
		const isCancel = ref(false);
		const dataCancel = ref({
			name: '-',
			date: '-',
		});
		const headerSteps = ref({
			id:'-',
			createdAt:'-',
			updatedAt:'-'
		});
		const headerData = ref({
			dateDisbursement: '-',
			limit:'-',
			valueDisbursement: '-',
			productName: '-',
			typeOperation: '-',
			freeValue: '-',
			nominalValue: '-',
			approved:  0,
			notApproved:  0,
		});
		const isRefused = ref(false);
		const isUpdatedCession = ref(false);
		const typePayment = ref(0);
		const financialVehicleId = ref(0);
		const dataModalDetailCession = ref({
			dateDisbursement: null,
			limit: 0,
			valueDisbursement: 0,
			productName: '-',
			typeOperation: '-',
			freeValue: 0,
			nominalValue: 0,
		});

		function triggerEventAnalytics(description: string): void {
			const dataTrigger = {
				event: 'detail_cession',
				payload:{
					step: 2,
					description,
				}
			};
			trigger(dataTrigger);
		}

		function onOpenModalDetailsCession(): void {
			triggerEventAnalytics('clicou no detalhe da cessão');
			openModalDetailCession();
		}

		function onClickFooter(): void {
			triggerEventAnalytics('clicou no botão voltar');
			redirectHome();
		}

		function updatedPage(data): void {
			headerSteps.value ={
				id: data.id,
				createdAt: defaultDateFormat(data.createdAt),
				updatedAt: defaultDateFormat(data.updatedAt)
			};
			headerData.value = {
				dateDisbursement: defaultDateFormat(data.dateDisbursement),
				limit: brl(data.limit),
				valueDisbursement: brl(data.valueDisbursement),
				productName: data.productName,
				typeOperation: data.typeOperation,
				approved: data.approved,
				notApproved: data.notApproved,
			};
			dataModalDetailCession.value = {
				dateDisbursement: data.dateDisbursement,
				limit: data.limit,
				valueDisbursement: data.valueDisbursement,
				productName: data.productName,
				typeOperation: data.typeOperation,
				freeValue: data.freeValue,
				nominalValue: data.nominalValue,
			};
			if(data.statusId === REFUSED_STATUS) {
				isRefused.value = true;
				headerData.value.valueDisbursement = 'R$0,00';
				dataModalDetailCession.value.valueDisbursement = 0;
			}
			if(data.statusId === CANCEL_STATUS) {
				isCancel.value = true;
				dataCancel.value = {
					name: data.deletedBy,
					date: defaultDateFormat(data.deletedAt) || 'N/A',
				};
			}
			typePayment.value = data.typeId;
			financialVehicleId.value = data.financialVehicleId;
			isUpdatedCession.value = data.isUpdated;
			showTabs.value = true;
		}

		function load(): void {
			showTabs.value = false;
			getDetailCession(cessionId, updatedPage);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			typePayment,
			financialVehicleId,
			isUpdatedCession,
			isRefused,
			isLoading,
			isError,
			isCancel,
			isModalDetailCession,
			cessionId,
			dataCancel,
			detailCession,
			headerSteps,
			headerData,
			showTabs,
			dataModalDetailCession,
			onCloseModalDetailCession,
			onOpenModalDetailsCession,
			onClickFooter,
			onReload
		};
	}
});
</script>
