<template>
	<farm-box>
		<farm-row extra-decrease>
			<farm-line />
		</farm-row>
		<farm-row class="mt-6">
			<farm-col cols="12"  align="right">
				<farm-btn
					title="Fechar"
					class="farm-btn--responsive"
					@click="onClickClose"
				>
					Voltar
				</farm-btn>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	setup(_, { emit }) {


		function onClickClose(): void {
			emit('onClickFooter');
		}

		return {
			onClickClose
		};
	},
});
</script>
