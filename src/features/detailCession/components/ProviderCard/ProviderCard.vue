<template>
	<farm-card class="selected-supplier">
		<farm-card-content class="header" gutter="md">
			<farm-row align="center" class="mb-0">
				<farm-col cols="12" class="mb-2">
					<farm-bodytext :type="1" ellipsis variation="bold">
						{{ item.productName }}
					</farm-bodytext>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-caption color-variation="lighten"> CNPJ</farm-caption>
					<farm-bodytext :type="1" ellipsis variation="bold">
						{{ item.document }}
					</farm-bodytext>
				</farm-col>
				<farm-col cols="12" md="4">
					<farm-caption color-variation="lighten">Valor a Receber</farm-caption>
					<farm-bodytext :type="1" variation="bold">
						{{formatCurrency(item.amountToPay)}}
					</farm-bodytext>
				</farm-col>
				<farm-col cols="12" md="4" >
					<farm-caption color-variation="lighten">Porcentagem do Desembolso</farm-caption>
					<farm-bodytext :type="1" variation="bold">
						{{item.percentage}}%
					</farm-bodytext>
				</farm-col>
			</farm-row>
		</farm-card-content>
		<farm-line noSpacing />
		<farm-card-content background="base" gutter="md">
			<farm-row class="mb-3">
				<template v-if="item.type === 'account'">
					<farm-col cols="12" md="3">
						<farm-caption color-variation="lighten"> Tipo</farm-caption>
						<farm-bodytext type="2" variation="bold">
							{{ item.accountType }}
						</farm-bodytext>
					</farm-col>
					<farm-col cols="12" md="3">
						<farm-caption color-variation="lighten"> Banco</farm-caption>
						<farm-bodytext ellipsis type="2" variation="bold">
							{{ item.bank }}
						</farm-bodytext>
					</farm-col>
					<farm-col cols="12" md="3">
						<farm-caption color-variation="lighten"> Agência</farm-caption>
						<farm-bodytext type="2" variation="bold">
							{{ item.agency }}
						</farm-bodytext>
					</farm-col>
					<farm-col cols="12" md="3">
						<farm-caption color-variation="lighten"> Número da Conta</farm-caption>
						<farm-bodytext type="2" variation="bold">
							{{ item.accountNumber }}
						</farm-bodytext>
					</farm-col>
				</template>
				<template v-if="item.type !== 'account'">
					<farm-col cols="12" md="3">
						<farm-caption color-variation="lighten"> Tipo</farm-caption>
						<farm-bodytext type="2" variation="bold">
							Pix
						</farm-bodytext>
					</farm-col>
					<farm-col cols="12" md="4">
						<farm-caption color-variation="lighten"> Tipo de Chave</farm-caption>
						<farm-bodytext type="2" variation="bold">
							{{ item.pixType }}
						</farm-bodytext>
					</farm-col>
					<farm-col cols="12" md="4">
						<farm-caption color-variation="lighten"> Chave</farm-caption>
						<farm-bodytext ellipsis type="2" variation="bold">
							{{ item.key }}
						</farm-bodytext>
					</farm-col>
				</template>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue';

import { brl as formatCurrency } from '@farm-investimentos/front-mfe-libs-ts';

import { currency as currencyMask } from '@/helpers/masks';

export default defineComponent({
	name: "provider-card",
	props: {
		item: {
			required: true,
			type: Object,
		},
		valueDisbursement: {
			type: [String, Number],
			require: true,
		},
	},
	setup(props) {

		const { valueDisbursement, item } = toRefs(props);

		const approved = ref(item.value.amountToPay?.toString().replace('.', ','));

		const rules = {
			required: value => (value !== 'R$' && !!value) || 'Campo obrigatório',
		};

		return {
			approved,
			currencyMask,
			formatCurrency,
			rules,
			valueDisbursement
		};
	},
});
</script>

<style lang="scss" scoped>
@import 'ProviderCard';
</style>
