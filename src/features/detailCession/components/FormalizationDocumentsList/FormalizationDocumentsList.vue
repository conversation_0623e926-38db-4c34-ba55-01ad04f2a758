<template>
	<farm-box>
		<div v-for="(item, index) in data" :key="item.id">
			<formalization-documents-card :data="item" :isHiddenOptions="isHiddenOptions" />
			<farm-line noSpacing class="mt-3 mb-4" v-if="!((data.length - 1) === index)"/>
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import FormalizationDocumentsCard from '../FormalizationDocumentsCard';

export default defineComponent({
	name:'formalization-documents-list',
	components: {
		FormalizationDocumentsCard
	},
	props: {
		data: {
			type: Array,
			require: true
		},
		isHiddenOptions: {
			type: Boolean,
			default: false
		}
	}
});
</script>
