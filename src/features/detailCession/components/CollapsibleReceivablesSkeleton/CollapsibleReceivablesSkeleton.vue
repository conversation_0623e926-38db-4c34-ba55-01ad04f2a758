<template>
	<farm-card>
		<farm-card-content gutter="md">
			<farm-row align="center">
				<farm-col cols="6">
					<div class="d-flex align-center">
						<skeleton width="20px" height="20px" />
						<skeleton width="180px" height="20px" left="10px"/>
					</div>
				</farm-col>
				<farm-col cols="6">
					<div class="d-flex align-center justify-end">
						<skeleton width="110px" height="36px" />
						<skeleton width="20px" height="20px" left="10px"/>
					</div>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Skeleton from '@/components/Skeleton';

export default defineComponent({
	name:'collapsible-receivables-skeleton',
	components:{
		Skeleton
	}
});
</script>
