<template>
	<farm-box>
		<farm-row  y-grid-gutters>
			<farm-col cols="12" md="6" v-for="(_, index) in data" :key="`providers-list-skeleton-${index}`">
				<cards>
					<template slot="header">
						<farm-row class="mb-4">
							<farm-col cols="12">
								<skeleton width="420px" height="16px" bottom="8px" />
							</farm-col>
						</farm-row>
						<farm-row>
							<farm-col cols="4">
								<skeleton width="100px" height="16px" bottom="8px" />
								<skeleton width="180px" height="16px" />
							</farm-col>
							<farm-col cols="4">
								<skeleton width="100px" height="16px" bottom="8px" />
								<skeleton width="180px" height="16px" />
							</farm-col>
							<farm-col cols="4">
								<skeleton width="100px" height="16px" bottom="8px" />
								<skeleton width="180px" height="16px" />
							</farm-col>
						</farm-row>
					</template>
					<template slot="body">
						<farm-row>
							<farm-col cols="3">
								<skeleton width="60px" height="16px" bottom="8px" />
								<skeleton width="100px" height="16px" />
							</farm-col>
							<farm-col cols="3">
								<skeleton width="60px" height="16px" bottom="8px" />
								<skeleton width="100px" height="16px" />
							</farm-col>
							<farm-col cols="3">
								<skeleton width="60px" height="16px" bottom="8px" />
								<skeleton width="100px" height="16px" />
							</farm-col>
							<farm-col cols="3">
								<skeleton width="60px" height="16px" bottom="8px" />
								<skeleton width="100px" height="16px" />
							</farm-col>
						</farm-row>
					</template>
				</cards>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import Skeleton from '@/components/Skeleton';

export default defineComponent({
	name:'provider-list-skeleton',
	components:{
		Cards,
		Skeleton
	},
	props: {
		numberItems: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const data = new Array(props.numberItems);
		return {
			data
		};
	}
});
</script>
