<template>
	<farm-box>
		<farm-heading :type="6" class="mb-4">
			Logs de Atualização
		</farm-heading>
		<div v-if="!isDataEmpty">
			<farm-logger :items="data" />
		</div>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

export default defineComponent({
	name: "logger",
	props: {
		isRefused: {
			type: Boolean,
			default: false,
		},
		data:{
			type: Array,
			require: true
		},

	},
	setup(props) {
		const { data } = toRefs(props);

		const isDataEmpty = computed(() => data.value.length === 0);
		return {
			data,
			isDataEmpty
		};
	},
});
</script>
