<template>
	<farm-box>
		<farm-row>
			<farm-col md="7" align-self="center">
				<farm-heading :type="6" variation="bold" color-variation="darken">
					{{ title }}
				</farm-heading>
				<farm-col no-gutters>
					<farm-row>
						<farm-col align-self="center" class="header--data">
							<farm-idcaption copy-text="">
								<template v-slot:subtitle>ID: <strong>{{data.id}}</strong></template>
							</farm-idcaption>

							<farm-idcaption copy-text="">
								<template v-slot:subtitle>Criação: <strong>{{data.createdAt}}</strong></template>
							</farm-idcaption>

							<farm-idcaption copy-text="">
								<template v-slot:subtitle>Atualização: <strong>{{data.updatedAt}}</strong></template>
							</farm-idcaption>
						</farm-col>
					</farm-row>
				</farm-col>
			</farm-col>
			<farm-col md="5">
				<farm-stepper-header
					:steps="steps"
					:currentStep="currentStep"
					:error-current-step-status="isRefused"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease>
			<farm-line />
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		title: {
			type: String,
			default: 'N/A',
		},
		steps: {
			type: Array,
			default: () => [
				{ label: 'Analisando Cessão' },
				{ label: 'Aguardando Formalização' },
				{ label: 'Cessão em Desembolso' },
				{ label: 'Cessão Concluída' },
			],
		},
		currentStep: {
			type: Number,
			default: 1,
		},
		isRefused: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			require:true
		},
	},
});
</script>
<style lang="scss" scoped>
@import 'Header';
</style>
