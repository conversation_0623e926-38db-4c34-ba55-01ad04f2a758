<template>
	<farm-row>
		<farm-col>
			<tabs-form
				:tabList="tabsList"
				:valueDefault="valueDefault"
				@onUpdateCurrentTab="onUpdateCurrentTab"
			/>
			<tab-receivables
				v-if="isTabRecebeivables"
				:approved="approved"
				:notApproved="notApproved"
			/>
			<tab-payment
				v-if="isTabPayment && financialVehicleId !==0"
				:typePayment="typePayment"
				:financialVehicleId="financialVehicleId"
			/>
			<tab-formalization
				v-if="isTabFormalization"
				:isHiddenOptions="isCompleted || isHiddenPayment"
				:isCompleted="isCompleted"
			/>
			<tabs-updates v-if="isTabUpdate" />
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref, toRefs } from 'vue';

import TabsForm from '@/components/TabsForm';
import { useAnalytics } from '@/composible/useAnalytics';

import { tabsDetailsCession, tabsDetailsCessionFormalization } from '../../configurations';
import { RECEIVABLES, PAYMENT, FORMALIZATION, UPDATE } from '../../constants';
import TabFormalization from '../TabFormalization';
import TabPayment from '../TabPayment';
import TabReceivables from '../TabReceivables';
import TabsUpdates from '../TabsUpdates';

export default defineComponent({
	name: "tabs-content",
	components: {
		TabsForm,
		TabsUpdates,
		TabPayment,
		TabFormalization,
		TabReceivables
	},
	props: {
		isFormalization: {
			type: Boolean,
			default: false
		},
		approved: {
			type: Number,
			required: true,
		},
		notApproved: {
			type: Number,
			required: true,
		},
		typePayment: {
			type: Number,
			required: true,
		},
		financialVehicleId: {
			type: Number,
			required: true,
		},
		isHiddenPayment: {
			type: Boolean,
			default: false
		},
		isCompleted: {
			type: Boolean,
			default: false
		},
		isDisbursement: {
			type: Boolean,
			default: false
		},
	},
	setup(props) {
		const { trigger } = useAnalytics();
		const {
			isFormalization,
			typePayment,
			financialVehicleId,
			notApproved,
			approved,
			isHiddenPayment,
			isCompleted,
			isDisbursement
		} = toRefs(props);

		const tabsList = ref([]);
		const currentTab = ref(RECEIVABLES);
		const valueDefault = ref(RECEIVABLES);

		const isTabRecebeivables = computed(() => currentTab.value === RECEIVABLES);
		const isTabPayment = computed(() => currentTab.value === PAYMENT);
		const isTabFormalization = computed(() => currentTab.value === FORMALIZATION);
		const isTabUpdate = computed(() => currentTab.value === UPDATE);

		let currentActive = '';

		function generateDataAnalyticsEvent(value: string): string {
			return `super_cession_details_status_formalization_${value}`;
		}

		function getDataAnalytics(value) {
			const nameEvent = generateDataAnalyticsEvent(value);
			return {
				event: nameEvent,
				payload: {
					description: 'clicou na aba de ' + value,
				},
			};
		}

		function onUpdateCurrentTab(value): void {
			if (currentActive !== value) {
				currentActive = value;
				const dataTrigger = getDataAnalytics(value);
				trigger(dataTrigger);
			}
			currentTab.value = value;
		}

		function getListTabs(data, hasFormalization) {
			if(hasFormalization || isCompleted.value || isDisbursement.value){
				return [
					data[0],
					data[2],
					data[3]
				];
			}
			return [
				data[0],
				data[2]
			];
		}

		function initValueTab(): void {
			if(isFormalization.value || isCompleted.value || isDisbursement.value){
				if(isHiddenPayment.value){
					tabsList.value = getListTabs([...tabsDetailsCessionFormalization], isFormalization.value);
					return;
				}
				tabsList.value = [...tabsDetailsCessionFormalization];
				return;
			}
			if(isHiddenPayment.value){
				tabsList.value = getListTabs([...tabsDetailsCession], isFormalization.value);
				return;
			}
			tabsList.value = [...tabsDetailsCession];
		}

		onMounted(() => {
			initValueTab();
		});

		return {
			isTabRecebeivables,
			isTabPayment,
			isTabFormalization,
			isTabUpdate,
			isCompleted,
			tabsList,
			currentTab,
			valueDefault,
			typePayment,
			financialVehicleId,
			notApproved,
			approved,
			onUpdateCurrentTab,
		};
	},
});
</script>
