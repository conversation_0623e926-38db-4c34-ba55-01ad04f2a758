<template>
	<cards class="outline">
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="10">
					<card-title-header :value="item.name" ellipsis class="mb-2" />
				</farm-col>
				<farm-col cols="2" class="mb-2 d-flex justify-end">
					<farm-chip class="px-5" v-if="!item.isHeadOffice" color="info" :dense="true">
						Filial
					</farm-chip>
					<farm-chip class="px-3" v-if="item.isHeadOffice" color="success" :dense="true"
						>Matriz</farm-chip
					>
				</farm-col>
				<farm-col cols="7">
					<card-text-body label="CNPJ" :value="item.document" ellipsis />
				</farm-col>
				<farm-col cols="5">
					<farm-bodytext
						:type="2"
						variation="bold"
						color-variation="lighten"
						style="display: inline"
					>
						Valor a Receber
					</farm-bodytext>
					<farm-caption
						v-if="item.cessionPercentage"
						color-variation="lighten"
						style="display: inline"
					>
						({{ item.cessionPercentage }}% do Desembolso)
					</farm-caption>
					<farm-bodytext :type="2" variation="bold" :style="{ color: '#333333' }">
						{{ formatMoney(item.value) }}
					</farm-bodytext>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row v-if="item.withBankDataAssociated && item.type === 'account'">
				<farm-col cols="3">
					<card-text-body label="Tipo" :value="item.accountType" ellipsis />
				</farm-col>
				<farm-col cols="3">
					<div class="fild-data">
						<card-text-body label="Banco" :value="item.bank" ellipsis />
					</div>
				</farm-col>
				<farm-col cols="3">
					<card-text-body label="Agência" :value="item.agency" ellipsis />
				</farm-col>
				<farm-col cols="3">
					<card-text-body label="Número da Conta" :value="item.account" ellipsis />
				</farm-col>
			</farm-row>
			<farm-row v-if="item.withBankDataAssociated && item.type !== 'account'">
				<farm-col cols="4">
					<card-text-body label="Tipo" value="Pix" ellipsis />
				</farm-col>
				<farm-col cols="4">
					<card-text-body label="Tipo de chave" :value="item.pixType" ellipsis />
				</farm-col>
				<farm-col cols="4">
					<card-text-body label="Chave" :value="item.key" ellipsis />
				</farm-col>
			</farm-row>
		</template>
	</cards>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import Cards from '@/components/Cards';

export default defineComponent({
	name: 'originator-card',
	components: {
		Cards,
		CardTitleHeader,
		CardTextBody,
	},
	props: {
		item: {
			required: true,
			type: Object,
		},
	},
	setup(props) {
		const { item } = toRefs(props);

		return {
			item,
			formatMoney: brl,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './OriginatorCard.scss';

.outline {
	border-color: var(--farm-primary-base);
}
</style>
