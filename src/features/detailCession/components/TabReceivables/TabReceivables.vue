<template>
	<farm-box>
		<title-skeleton
			v-if="isLoadingReceivablesApproved && (isFirstRenderApproved && isFirstRenderDenyReason)"
			class="my-4"
		/>
		<animated-fade-in v-if="isFirstRenderApproved === false || isFirstRenderDenyReason === false">
			<farm-row class="mt-4">
				<farm-col cols="12">
					<farm-heading :type="6" class="mb-4"> Validação de Recebíveis </farm-heading>
				</farm-col>
			</farm-row>
		</animated-fade-in>
		<collapsible-receivables-skeleton
			v-if="isLoadingReceivablesApproved && isFirstRenderApproved"
			class="mt-6 mb-4"
		/>
		<animated-fade-in
			v-if="!isFirstRenderApproved && !isErrorReceivablesApproved && isCollapsibleApproved"
		>
			<farm-row class="mt-2">
				<farm-col cols="12">
					<collapsible-cession-receivables-approved
						:approved="approved"
						:data="receivablesApproved"
						:isOpen="false"
						:disabledButton="approved === 0 || !canWrite"
						:pagination="receivablesApprovedPagination"
						:filter="filterReceivablesApproved"
						:headerTable="headers"
						@onRequest="onRequest"
						@onClickDownload="onClickDownloadReceivablesApproved"
						@onStatusOpen="onStatusOpenReceivablesApproved"
					/>
				</farm-col>
			</farm-row>
		</animated-fade-in>
		<div v-if="isErrorReceivablesApproved" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReloadApproved" />
		</div>
		<collapsible-receivables-skeleton class="my-4" v-if="isLoadingDenyReason && isFirstRenderDenyReason" />
		<animated-fade-in v-if="!isFirstRenderDenyReason && !isErrorDenyReason && denyReasonV2">
			<farm-row class="mt-4">
				<farm-col cols="12">
					<collapsible-cession-deny-reasons
						:notApproved="notApproved"
						:data="denyReasonV2"
						:pagination="denyReasonPagination"
						:isOpen="false"
						:disabledButton="notApproved === 0 || !canWrite"
						@onClickDownload="onClickDownloadDenyReasons"
						@onStatusOpen="onStatusOpenDenyReasons"
						@onRequest="onRequestDenyReasons"
					/>
				</farm-col>
			</farm-row>
		</animated-fade-in>
		<LoadingInline
			v-if="isLoadingReceivablesApproved && typeLoadingApproved === 'overlay'"
			:mode="typeLoadingApproved"
		/>
		<LoadingInline
			v-if="isLoadingDenyReason && typeLoadingDenyReason === 'overlay'"
			:mode="typeLoadingDenyReason"
		/>
		<div v-if="isErrorDenyReason" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReloadDenyReasons" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, onMounted, computed } from 'vue';

import AnimatedFadeIn from '@/components/AnimatedFadeIn';
import CollapsibleCessionDenyReasons from '@/components/CollapsibleCessionDenyReasons';
import CollapsibleCessionReceivablesApproved from '@/components/CollapsibleCessionReceivablesApproved';
import LoadingInline from '@/components/LoadingInline';
import { useAnalytics } from '@/composible/useAnalytics';
import useRoute from '@/composible/useRoute';

import { useDenyReasonsV2 } from '../../composables/useDenyReasonsV2';
import { useExportExcel } from '../../composables/useExportExcel';
import { useReceivablesApproved } from '../../composables/useReceivablesApproved';
import { headers } from '../../configurations/headers';
import CollapsibleReceivablesSkeleton from '../CollapsibleReceivablesSkeleton';
import TitleSkeleton from '../TitleSkeleton';

export default defineComponent({
	name: 'tab-receivables',
	components: {
		AnimatedFadeIn,
		CollapsibleCessionDenyReasons,
		CollapsibleCessionReceivablesApproved,
		TitleSkeleton,
		CollapsibleReceivablesSkeleton,
		LoadingInline
	},
	props: {
		approved: {
			type: Number,
			required: true,
		},
		notApproved: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const { notApproved, approved } = toRefs(props);

		const route = useRoute();
		const { trigger } = useAnalytics();
		const { downloadExcel } = useExportExcel();
		const {
			denyReasonPagination,
			denyReasonV2,
			getDenyReasonV2,
			isLoadingDenyReason
		} = useDenyReasonsV2();
		const {
			getReceivablesApproved,
			receivablesApproved,
			receivablesApprovedPagination,
			isLoadingReceivablesApproved,
		} = useReceivablesApproved();

		const filterReceivablesApproved = ref({
			page: 0,
			limit: 10,
		});
		const typeLoadingDenyReason = ref('');
		const typeLoadingApproved = ref('');
		const isFirstRenderDenyReason = ref(true);
		const isFirstRenderApproved = ref(true);
		const isErrorDenyReason = ref(false);
		const isErrorReceivablesApproved = ref(false);

		const cessionId = route.params.id;

		const isCollapsibleApproved = computed(() => {
			if(isErrorReceivablesApproved.value){
				return false;
			}
			return true;
		});
		const isCollapsibleDenyReason = computed(() => {
			if(isErrorDenyReason.value){
				return false;
			}
			if(typeLoadingDenyReason.value === ""){
				if(!isLoadingDenyReason.value){
					return true;
				}
				return false;
			}
			return true;
		});

		function triggerEventAnalytics(description: string): void {
			const dataTrigger = {
				event: 'detail_cession',
				payload: {
					step: 2,
					description,
				},
			};
			trigger(dataTrigger);
		}

		function onStatusOpenReceivablesApproved(open: boolean): void {
			const action = open ? 'expandir' : 'esconder';
			triggerEventAnalytics(`clicou para ${action} os recebiveis aprovados`);
		}

		function onStatusOpenDenyReasons(open: boolean): void {
			const action = open ? 'expandir' : 'esconder';
			triggerEventAnalytics(`clicou para ${action} os rmotivos de recusa`);
		}

		function onRequest(dataFilter): void {
			typeLoadingApproved.value = 'overlay';
			filterReceivablesApproved.value = { ...dataFilter };
			getReceivablesApproved(cessionId, filterReceivablesApproved.value);
		}

		function onClickDownloadDenyReasons(): void {
			downloadExcel({ id: cessionId, status: 1 });
			triggerEventAnalytics('clicou no botão exportar motivos de recusa');
		}

		function onClickDownloadReceivablesApproved(): void {
			downloadExcel({ id: cessionId, status: 2 });
			triggerEventAnalytics('clicou no botão exportar recebiveis aprovados');
		}

		function onReloadDenyReasons(): void {
			const params = {
				page:0,
				limit:10,
				'id_status':1
			};
			getDenyReasonV2(cessionId, params);
		}

		function onReloadApproved(): void {
			getReceivablesApproved(cessionId, filterReceivablesApproved.value, updatedPageApproved);
		}

		function onRequestDenyReasons(data): void {
			typeLoadingDenyReason.value = 'overlay';
			const params = {
				...data,
				'id_status':1
			};
			getDenyReasonV2(cessionId, params);
		}

		function updatedPageApproved(hasError): void {
			if (hasError) {
				isErrorReceivablesApproved.value = true;
				return;
			}
			isErrorReceivablesApproved.value = false;
			if(isFirstRenderApproved.value) {
				isFirstRenderApproved.value = false;
			}
		}

		function updatedPageDenyReason(hasError): void {
			if (hasError) {
				isErrorDenyReason.value = true;
				return;
			}
			isErrorDenyReason.value = false;
			if(isFirstRenderDenyReason.value) {
				isFirstRenderDenyReason.value = false;
			}
		}

		function load(): void {
			typeLoadingDenyReason.value = '';
			typeLoadingApproved.value = '';
			onReloadApproved();
			const params = {
				page:0,
				limit:10,
				'id_status':1
			};
			getDenyReasonV2(cessionId, params, updatedPageDenyReason);
		}

		onMounted(() => {
			load();
		});

		return {
			isLoadingDenyReason,
			isLoadingReceivablesApproved,
			isErrorDenyReason,
			isErrorReceivablesApproved,
			isCollapsibleApproved,
			isCollapsibleDenyReason,
			isFirstRenderDenyReason,
			isFirstRenderApproved,
			notApproved,
			approved,
			denyReasonV2,
			denyReasonPagination,
			receivablesApproved,
			filterReceivablesApproved,
			receivablesApprovedPagination,
			headers,
			typeLoadingDenyReason,
			typeLoadingApproved,
			onReloadDenyReasons,
			onReloadApproved,
			onClickDownloadDenyReasons,
			onClickDownloadReceivablesApproved,
			onRequest,
			onStatusOpenReceivablesApproved,
			onStatusOpenDenyReasons,
			onRequestDenyReasons
		};
	},
});
</script>
