<template>
	<farm-box>
		<title-skeleton class="my-4" v-if="isLoading" />
		<animated-fade-in v-if="!isError && !isLoading && isProvider">
			<div class="mt-4">
				<farm-row>
					<farm-col cols="12">
						<farm-heading :type="6" class="mb-4">
							Fornecedor(es) Selecionado(s)
						</farm-heading>
					</farm-col>
				</farm-row>
				<provider-list v-if="!isError" :data="providers" />
			</div>
		</animated-fade-in>
		<animated-fade-in v-if="!isError && !isLoading && !isProvider">
			<div >
				<farm-row>
					<farm-col cols="12">
						<farm-heading :type="6" class="mb-4"> Cedentes Favorecidos </farm-heading>
					</farm-col>
				</farm-row>
				<originator-list v-if="!isError" :data="originators" />
			</div>
		</animated-fade-in>
		<provider-list-skeleton :numberItems="2" v-if="isLoading" />
		<div v-if="isError && !isLoading" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, onMounted, toRefs, ref } from 'vue';

import AnimatedFadeIn from '@/components/AnimatedFadeIn';
import useRoute from '@/composible/useRoute';

import { useOriginators } from '../../composables/useOriginators/useOriginators';
import { useProviders } from '../../composables/useProviders/useProviders';
import OriginatorList from '../OriginatorList';
import ProviderList from '../ProviderList';
import ProviderListSkeleton from '../ProviderListSkeleton';
import TitleSkeleton from '../TitleSkeleton';

export default defineComponent({
	name:'tab-payment',
	components: {
		'provider-list': ProviderList,
		'originator-list': OriginatorList,
		AnimatedFadeIn,
		TitleSkeleton,
		ProviderListSkeleton
	},
	props: {
		typePayment: {
			type: Number,
			required: true,
		},
		financialVehicleId: {
			type: Number,
			required: true,
		}
	},
	setup(props) {
		const { typePayment, financialVehicleId } = toRefs(props);

		const route = useRoute();
		const {
			getProvidersCession,
			providers,
		} = useProviders();
		const {
			getOriginatorsCession,
			originators
		} = useOriginators();

		const isLoading = ref(true);
		const isError = ref(false);
		const isProvider = ref(null);

		const cessionId = route.params.id;

		function updatedPayment(isErrorApis: boolean): void {
			if(isErrorApis){
				isError.value = true;
				isLoading.value = false;
				return;
			}
			isLoading.value = false;
			isError.value = false;
		}

		function getProvider(): void {
			getProvidersCession({
				id: cessionId,
				financialVehicleId: financialVehicleId.value,
			}, updatedPayment);
		}

		function getOriginator(): void {
			getOriginatorsCession({ id: cessionId }, updatedPayment);
		}

		function onReload(): void {
			isLoading.value = true;
			load();
		}

		function load() {
			if (typePayment.value !== 2) {
				isProvider.value = true;
				getProvider();
				return;
			}
			isProvider.value = false;
			getOriginator();
		}

		onMounted(() => {
			isLoading.value = true;
			load();
		});

		return {
			isLoading,
			isError,
			isProvider,
			providers,
			originators,
			onReload
		};
	},
});
</script>
