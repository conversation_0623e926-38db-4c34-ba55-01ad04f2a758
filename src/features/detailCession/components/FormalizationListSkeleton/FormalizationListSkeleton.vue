<template>
	<farm-box>
		<farm-row  y-grid-gutters>
			<farm-col cols="12" v-for="(_, index) in data" :key="`formalization-list-skeleton-${index}`">
				<cards backgroundContent="none">
					<template slot="header">
						<farm-row class="mb-2">
							<farm-col cols="12">
								<skeleton width="34%" height="16px"  />
							</farm-col>
						</farm-row>
						<farm-row>
							<farm-col cols="2">
								<skeleton width="100%" height="16px" />
							</farm-col>
							<farm-col cols="2">
								<skeleton width="100%" height="16px" />
							</farm-col>
						</farm-row>
					</template>
					<template slot="body">
						<farm-row>
							<farm-col cols="12" class="mb-4">
								<skeleton width="65%" height="16px" />
							</farm-col>
							<farm-col cols="12" >
								<skeleton width="45%" height="16px" />
							</farm-col>
						</farm-row>
					</template>
				</cards>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import Skeleton from '@/components/Skeleton';

export default defineComponent({
	name:'provider-list-skeleton',
	components:{
		Cards,
		Skeleton
	},
	props: {
		numberItems: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const data = new Array(props.numberItems);
		return {
			data
		};
	}
});
</script>
