<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters >
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<provider-card :item="item" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import ProviderCard from '../ProviderCard';

export default defineComponent({
	name: "provider-list",
	components:{
		ProviderCard
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props) {

		const { data } = toRefs(props);

		const isDataEmpty = computed(() => data.value.length === 0);

		return {
			isDataEmpty,
			data
		};
	},
});

</script>
