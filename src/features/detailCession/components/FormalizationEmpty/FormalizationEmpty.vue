<template>
	<farm-box>
		<farm-row justify="center">
			<img src="@/assets/img-formalizacao.svg" alt="imagem referente a formalização" />
		</farm-row>
		<farm-row justify="center">
			<farm-bodytext
				variation="bold"
				:type="1">
					Estamos preparando a formalização da sua cessão.
			</farm-bodytext>
		</farm-row>
		<farm-row justify="center" class="mt-2">
			<farm-caption variation="regular" color="gray">
				Esse processo pode levar algumas horas.
			</farm-caption>
		</farm-row>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
export default defineComponent({
	name:'formalization-empty'
});
</script>
