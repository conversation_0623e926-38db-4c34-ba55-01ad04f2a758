<template>
	<farm-box>
		<div v-for="item in data" :key="item.id">
			<formalization-card :data="item" :isHiddenOptions="isHiddenOptions" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import FormalizationCard from '../FormalizationCard';

export default defineComponent({
	name:'formalization-list',
	components: {
		FormalizationCard
	},
	props: {
		data: {
			type: Array,
			require: true
		},
		isHiddenOptions: {
			type: Boolean,
			default: false
		}
	}
});
</script>
