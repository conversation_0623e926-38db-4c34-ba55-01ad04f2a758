<template>
	<farm-container>
		<header-cession
			class="mb-4"
			title="Detalhes da Cessão"
			:currentStep="5"
			:data="headerSteps"
		/>
		<farm-row v-if="!isError">
			<farm-col cols="12">
				<information-cession-list
					:dateDisbursement="headerData.dateDisbursement"
					:limit="headerData.limit"
					:valueDisbursement="headerData.valueDisbursement"
					:productName="headerData.productName"
					:typeOperation="headerData.typeOperation"
					@onClickDisbursement="onOpenModalDetailsCession"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="!isError">
			<farm-line />
		</farm-row>
		<farm-row class="mt-6" v-if="!isError">
			<farm-col cols="12">
				<tabs-content
					isCompleted
					:typePayment="typePayment"
					:financialVehicleId="financialVehicleId"
					:approved="headerData.approved"
					:notApproved="headerData.notApproved"
				/>
			</farm-col>
		</farm-row>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-cession @onClickFooter="onClickFooter" />
		<modal-detail-cession
			v-model="isModalDetailCession"
			v-if="isModalDetailCession"
			:data="detailCession"
			:receivables="false"
			@onClose="onCloseModalDetailCession"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import InformationCessionList from '@/components/InformationCessionList';
import ModalDetailCession from '@/components/ModalDetailCession';
import TableReceivablesApproved from '@/components/TableReceivablesApproved';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';
import useRoute from '@/composible/useRoute';

import { useDetailCession } from '../../composables/useDetailCession';
import { useRedirect } from '../../composables/useRedirect';
import Footer from '../Footer';
import Header from '../Header';
import TabsContent from '../TabsContent';

export default defineComponent({
	components: {
		'header-cession': Header,
		'footer-cession': Footer,
		InformationCessionList,
		ModalDetailCession,
		TableReceivablesApproved,
		TabsContent
	},
	setup() {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const {
			isOpenModal: isModalDetailCession,
			onCloseModal: onCloseModalDetailCession,
			onOpenModal: openModalDetailCession,
		} = useModal();
		const {
			detailCession,
			getDetailCession,
			isLoadingDetailCession,
			isErrorDetailCession
		} = useDetailCession();
		const { redirectHome } = useRedirect();

		const cessionId = route.params.id;

		const isLoading = computed(() => {
			return isLoadingDetailCession.value;
		});

		const isError = computed(() => {
			return isErrorDetailCession.value;
		});

		const typePayment = ref(0);
		const financialVehicleId = ref(0);
		const headerSteps = ref({
			id: '-',
			createdAt: '-',
			updatedAt: '-',
		});
		const headerData = ref({
			dateDisbursement: '-',
			limit: '-',
			valueDisbursement: '-',
			productName: '-',
			typeOperation: '-',
			freeValue: '-',
			nominalValue: '-',
			approved: 0,
			notApproved: 0,
		});

		function triggerEventAnalytics(description: string): void{
			const dataTrigger = {
				event: 'detail_cession',
				payload:{
					step: 2,
					description,
				}
			};
			trigger(dataTrigger);
		}

		function onOpenModalDetailsCession(): void {
			triggerEventAnalytics('clicou no detalhe da cessão');
			openModalDetailCession();
		}

		function onClickFooter(): void {
			triggerEventAnalytics('clicou no botão voltar');
			redirectHome();
		}

		function updatedPage(data): void {
			headerSteps.value = {
				id: data.id,
				createdAt: defaultDateFormat(data.createdAt),
				updatedAt: defaultDateFormat(data.updatedAt),
			};
			headerData.value = {
				dateDisbursement: defaultDateFormat(data.dateDisbursement),
				limit: brl(data.limit),
				valueDisbursement: brl(data.valueDisbursement),
				productName: data.productName,
				typeOperation: data.typeOperation,
				approved: data.approved,
				notApproved: data.notApproved,
			};
			typePayment.value = data.typeId;
			financialVehicleId.value = data.financialVehicleId;
		}

		function load(): void {
			getDetailCession(cessionId, updatedPage);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			detailCession,
			isModalDetailCession,
			headerData,
			headerSteps,
			typePayment,
			financialVehicleId,
			onCloseModalDetailCession,
			onOpenModalDetailsCession,
			onClickFooter,
			onReload
		};
	},
});
</script>
