<template>
	<farm-box>
		<title-skeleton class="my-4" v-if="isLoadingFormalization" />
		<formalization-list-skeleton :numberItems="2" v-if="isLoadingFormalization" />
		<animated-fade-in
			v-if="!isErrorFormalization && !isLoadingFormalization && isCompleted && formalization.length === 0"
		>
			<formalization-status-completed-empty />
		</animated-fade-in>
		<animated-fade-in
			v-if="!isCompleted && !isErrorFormalization && !isLoadingFormalization && formalization.length === 0"
		>
			<formalization-empty />
		</animated-fade-in>
		<animated-fade-in
			v-if="!isErrorFormalization && !isLoadingFormalization && formalization.length > 0"
		>
			<formalization-list :data="formalization" :isHiddenOptions="isHiddenOptions" />
		</animated-fade-in>
		<div v-if="isErrorFormalization" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, onMounted, toRefs } from 'vue';

import AnimatedFadeIn from '@/components/AnimatedFadeIn';
import useRoute from '@/composible/useRoute';

import { useFormalization } from '../../composables/useFormalization';
import FormalizationEmpty from '../FormalizationEmpty';
import FormalizationList from '../FormalizationList';
import FormalizationListSkeleton from '../FormalizationListSkeleton';
import FormalizationStatusCompletedEmpty from '../FormalizationStatusCompletedEmpty';
import TitleSkeleton from '../TitleSkeleton';

export default defineComponent({
	name:'tab-formalization',
	components: {
		FormalizationEmpty,
		FormalizationList,
		FormalizationListSkeleton,
		FormalizationStatusCompletedEmpty,
		AnimatedFadeIn,
		TitleSkeleton
	},
	props:{
		isHiddenOptions: {
			type: Boolean,
			default: false
		},
		isCompleted: {
			type: Boolean,
			default: false
		}
	},
	setup(props) {
		const { isHiddenOptions, isCompleted } = toRefs(props);

		const route = useRoute();
		const {
			formalization,
			getFormalization,
			isErrorFormalization,
			isLoadingFormalization
		} = useFormalization();

		const cessionId = route.params.id;

		function onReload(): void {
			load();
		}

		function load(): void {
			getFormalization(cessionId, () => {});
		}

		onMounted(() => {
			load();
		});

		return {
			isCompleted,
			isErrorFormalization,
			isLoadingFormalization,
			isHiddenOptions,
			formalization,
			onReload
		};
	},
});
</script>
