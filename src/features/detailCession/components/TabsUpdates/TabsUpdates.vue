<template>
	<farm-box>
		<title-skeleton class="my-4" v-if="isLoading" />
		<logger-skeleton :numberItems="6" v-if="isLoading" />
		<animated-fade-in v-if="!isError && !isLoading">
			<farm-row class="mt-4">
				<farm-col cols="12">
					<logger :isRefused="false" :data="loggers" />
				</farm-col>
			</farm-row>
		</animated-fade-in>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

import AnimatedFadeIn from '@/components/AnimatedFadeIn';
import FeedbackAlert from '@/components/FeedbackAlert';
import useRoute from '@/composible/useRoute';

import { useLogger } from '../../composables/useLogger';
import Logger from '../Logger';
import LoggerSkeleton from '../LoggerSkeleton';
import TitleSkeleton from '../TitleSkeleton';

export default defineComponent({
	name: "tabs-updates",
	components: {
		AnimatedFadeIn,
		Logger,
		FeedbackAlert,
		TitleSkeleton,
		LoggerSkeleton
	},
	setup() {
		const route = useRoute();
		const { getLoggerCession, loggers } = useLogger();

		const isLoading = ref(true);
		const isError = ref(false);

		const cessionId = route.params.id;

		function updatedLogger(_, isErrorApis): void{
			if(isErrorApis) {
				isLoading.value = false;
				isError.value = true;
				return;
			}
			isLoading.value = false;
			isError.value = false;
		}

		function load(): void {
			getLoggerCession(cessionId, updatedLogger);
		}

		function onReload(): void {
			isLoading.value = true;
			load();
		}

		onMounted(() => {
			isLoading.value = true;
			load();
		});

		return {
			loggers,
			isLoading,
			isError,
			onReload,
		};
	},
});
</script>
