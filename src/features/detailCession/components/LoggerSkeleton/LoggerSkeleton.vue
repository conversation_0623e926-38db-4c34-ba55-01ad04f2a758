<template>
	<farm-box>
		<farm-row  y-grid-gutters>
			<farm-col cols="12" v-for="(_, index) in data" :key="`providers-list-skeleton-${index}`">
				<farm-row class="mb-4">
					<farm-col cols="12">
						<div class="d-flex align-center">
							<skeleton width="36px" height="36px" circle right="10px" />
							<div>
								<skeleton width="220px" height="16px" bottom="10px"/>
								<skeleton width="180px" height="16px" bottom="10px"/>
								<skeleton width="120px" height="16px" />
							</div>
						</div>
					</farm-col>
				</farm-row>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import Cards from '@/components/Cards';
import Skeleton from '@/components/Skeleton';

export default defineComponent({
	name:'Logger-skeleton',
	components:{
		Cards,
		Skeleton
	},
	props: {
		numberItems: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const data = new Array(props.numberItems);
		return {
			data
		};
	}
});
</script>
