

import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import { getOriginators as getOriginatorsService } from '@/features/detailCession/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseOriginators = {
	isLoadingOriginators: {
		value: boolean
	};
	isErrorOriginators: {
		value: boolean
	};
	originators: Array<any>;
	getOriginatorsCession: Function;
};

export function useOriginators(): UseOriginators {
	const originators = ref([]);
	let callFunc: Function | null = null;

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getOriginatorsService(params),
		onSuccess: (response) => {
			originators.value = response.data.content;
			if(callFunc) callFunc(false);
		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				originators.value = [];
				if(callFunc) callFunc(false);
				return;
			}
			originators.value = [];
			if(callFunc) callFunc(true);
		},
	});

	const isLoadingOriginators = computed(() => {
		return isLoading.value;
	});

	const isErrorOriginators = computed(() => {
		return isError.value;
	});

	function getOriginatorsCession({ id }, callback?: Function): void {
		mutate({productId, id});
		callFunc = callback || null;
	}

	return {
		isLoadingOriginators,
		isErrorOriginators,
		originators,
		getOriginatorsCession,
	};
}
