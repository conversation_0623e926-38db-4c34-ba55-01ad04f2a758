import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { builderFormalization } from '@/features/detailCession/helpers/builderFormalization';
import { getFormalization as getFormalizationService } from '@/features/detailCession/services';

type UseFormalization = {
	isLoadingFormalization: {
		value: boolean
	};
	isErrorFormalization: {
		value: boolean
	};
	formalization: Array<any>;
	getFormalization: Function;
};
export function useFormalization(): UseFormalization {
	const formalization = ref([]);
	let callFunc: Function | null = null;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getFormalizationService(params),
		onSuccess: (response) => {
			formalization.value = builderFormalization(response);
			if(callFunc) callFunc(formalization.value, false);
		},
		onError: () => {
			if(callFunc) callFunc([], true);
		},
	});

	const isLoadingFormalization= computed(() => {
		return isLoading.value;
	});

	const isErrorFormalization = computed(() => {
		return isError.value;
	});

	function getFormalization(id, callback?: Function): void {
		mutate({id});
		callFunc = callback || null;
	}

	return {
		isLoadingFormalization,
		isErrorFormalization,
		formalization,
		getFormalization,
	};
}
