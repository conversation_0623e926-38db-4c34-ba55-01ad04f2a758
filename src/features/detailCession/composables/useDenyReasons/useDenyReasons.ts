

import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import { builderDenyReasons } from '@/features/operationCession/helpers/builderDenyReasons';
import { getDenyReasons as getDenyReasonsService } from '@/features/operationCession/services';
import { DenyReasons } from '@/features/operationCession/types';

type UseDenyReasons = {
	denyReasons: Array<DenyReasons>;
	isLoadingDenyReasons: {
		value: boolean
	};
	isErrorDenyReasons: {
		value: boolean
	};
	getDenyReasons: Function;
}

export function useDenyReasons(): UseDenyReasons {
	const denyReasons = ref([]);

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getDenyReasonsService(params),
		onSuccess: (response) => {
			denyReasons.value = builderDenyReasons(response.data);
		},
		onError: () => {
			denyReasons.value = [];
		},
	});

	const isLoadingDenyReasons = computed(() => {
		return isLoading.value;
	});

	const isErrorDenyReasons = computed(() => {
		return isError.value;
	});

	function getDenyReasons(id): void {
		mutate({productId, id});
	}

	return {
		denyReasons,
		isLoadingDenyReasons,
		isErrorDenyReasons,
		getDenyReasons
	};
}

