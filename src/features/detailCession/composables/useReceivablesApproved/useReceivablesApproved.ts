

import { ref, computed } from "vue";

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import { builderReceivablesApproved } from '@/features/operationCession/helpers/builderReceivablesApproved';
import {
	getReceivablesApproved as getReceivablesApprovedService,
 } from '@/features/operationCession/services';
import { DenyReasons } from '@/features/operationCession/types';

type UseReceivablesApproved = {
	receivablesApproved: Array<DenyReasons>;
	receivablesApprovedPagination: any;
	isLoadingReceivablesApproved: {
		value: boolean
	};
	isErrorReceivablesApproved: {
		value: boolean
	};
	getReceivablesApproved: Function;
}

export function useReceivablesApproved(): UseReceivablesApproved {
	let callFunc: Function | null = null;
	const receivablesApproved = ref([]);
	const receivablesApprovedPagination = ref({});

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getReceivablesApprovedService(params),
		onSuccess: (response) => {
			const { content, pagination } = builderReceivablesApproved(response.data);
			receivablesApproved.value = content;
			receivablesApprovedPagination.value = pagination;
			if(callFunc) callFunc(false);
		},
		onError: () => {
			receivablesApproved.value = [];
			receivablesApprovedPagination.value = {};
			if(callFunc) callFunc(true);
		},
	});

	const isLoadingReceivablesApproved = computed(() => {
		return isLoading.value;
	});

	const isErrorReceivablesApproved = computed(() => {
		return isError.value;
	});

	function getReceivablesApproved(id, filters, callback?: Function): void {
		const params = queryString(filters, {});
		mutate({productId, id, filters: params});
		callFunc = callback;
	}

	return {
		receivablesApprovedPagination,
		receivablesApproved,
		isLoadingReceivablesApproved,
		isErrorReceivablesApproved,
		getReceivablesApproved
	};
}

