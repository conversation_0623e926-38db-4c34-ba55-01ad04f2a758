

import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import { builderProviders } from '@/features/detailCession/helpers/builderProviders';
import { getProvidersCession as getProvidersCessionService } from '@/features/detailCession/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseProviders = {
	isLoadingProviders: {
		value: boolean
	};
	isErrorProviders: {
		value: boolean
	};
	providers: Array<any>;
	getProvidersCession: Function;
};

export function useProviders(): UseProviders {
	const providers = ref([]);
	let callFunc: Function | null = null;

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getProvidersCessionService(params),
		onSuccess: (data) => {
			providers.value = builderProviders(data);
			if (callFunc) callFunc(false);
		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				providers.value = [];
				if (callFunc) callFunc(false);
				return;
			}
			providers.value = [];
			if (callFunc) callFunc(true);
		},
	});

	const isLoadingProviders = computed(() => {
		return isLoading.value;
	});

	const isErrorProviders = computed(() => {
		return isError.value;
	});

	function getProvidersCession({ id, financialVehicleId }, callback?: Function): void {
		mutate({ productId, id, financialVehicleId });
		callFunc = callback || null;
	}

	return {
		isLoadingProviders,
		isErrorProviders,
		providers,
		getProvidersCession,
	};
}
