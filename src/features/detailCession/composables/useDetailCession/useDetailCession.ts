import { ref, computed } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderDetailCession } from '@/features/detailCession/helpers/builderDetailCession';
import { getDetailCession as getDetailCessionService } from '@/features/detailCession/services';

type UseDetailCession = {
	isLoadingDetailCession: {
		value: boolean;
	};
	isErrorDetailCession: {
		value: boolean;
	};
	detailCession: Array<any>;
	getDetailCession: Function;
};

export function useDetailCession(): UseDetailCession {
	const detailCession = ref([]);
	let callFunc: Function | null = null;

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getDetailCessionService(params),
		onSuccess: response => {
			detailCession.value = builderDetailCession(response);
			if (callFunc) callFunc(detailCession.value);
		},
	});

	const isLoadingDetailCession = computed(() => {
		return isLoading.value;
	});

	const isErrorDetailCession = computed(() => {
		return isError.value;
	});

	function getDetailCession(id, callback: Function): void {
		mutate({ productId, id });
		callFunc = callback;
	}

	return {
		isLoadingDetailCession,
		isErrorDetailCession,
		detailCession,
		getDetailCession,
	};
}
