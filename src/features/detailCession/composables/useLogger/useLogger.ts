import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import { builderLogger } from '@/features/detailCession/helpers/builderLogger';
import { getLogCession as getLogCessionService } from '@/features/detailCession/services';

type UseLogger = {
	isLoadingLogger: {
		value: boolean
	};
	isErrorLogger: {
		value: boolean
	};
	loggers: Array<any>;
	getLoggerCession: Function;
};

export function useLogger(): UseLogger {
	const loggers = ref([]);
	let callFunc: Function | null = null;

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getLogCessionService(params),
		onSuccess: (response) => {
			loggers.value = builderLogger(response);
			if(callFunc) callFunc(loggers.value, false);
		},
		onError: () => {
			loggers.value = [];
			if(callFunc) callFunc(loggers.value, true);
		},
	});

	const isLoadingLogger = computed(() => {
		return isLoading.value;
	});

	const isErrorLogger = computed(() => {
		return isError.value;
	});

	function getLoggerCession(id: number, callback?: Function): void {
		mutate({productId, id});
		callFunc = callback || null;
	}

	return {
		isLoadingLogger,
		isErrorLogger,
		loggers,
		getLoggerCession,
	};
}
