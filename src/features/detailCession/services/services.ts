import { environment } from '@farm-investimentos/front-mfe-libs-ts';

import { client } from '@/configurations/services/superCession';

const domainJava = environment.apiSuperCessaoV2;

const EXTRATIMEOUT = 2000 * 360 * 360;

export const getDetailCession = ({ id }) => {
	const url = `/api/v1/operation/${id}`;
	return client.get(`${url}`);
};

export const getProvidersCession = ({ id, financialVehicleId }) => {
	const url = `/api/v1/operation/${id}/providers?page=0&limit=10&order=DESC&financialVehicleId=${financialVehicleId}&onlyInOperation=true&withBankData=true`;
	return client.get(`${url}`);
};

export const getLogCession = ({ id }) => {
	const url = `/api/v1/operation/${id}/log`;
	return client.get(`${url}`);
};

export const getOriginators = ({ id }) => {
	const url = `/operation/${id}/originators?page=0&limit=1000&order=DESC`;
	return client.get(`${domainJava}${url}`);
};

export const getFormalization = ({ id }) => {
	const url = `/api/v1/formalization?cessionId=${id}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const sendEmailFormalization = ({ payload }) => {
	const url = `/api/v1/formalization/resend-contract-email`;
	return client.post(`${url}`, payload);
};
