import { TabTypes } from '@/types';

import { RECEIVABLES, PAYMENT, UPDATE, FORMALIZATION } from '../../constants';

export const tabsDetailsCession: Array<TabTypes> = [
	{
		name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
		path: RECEIVABLES,
	},
	{
		name: '<PERSON><PERSON><PERSON>',
		path: PAYMENT,
	},
	{
		name: 'Atualizações',
		path: UPDATE,
	},
];

export const tabsDetailsCessionFormalization: Array<TabTypes> = [
	tabsDetailsCession[0],
	tabsDetailsCession[1],
	{
		name: 'Formalização',
		path: FORMALIZATION,
	},
	tabsDetailsCession[2]
];
