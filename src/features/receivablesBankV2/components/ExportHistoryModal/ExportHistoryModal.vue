<template>
	<farm-modal class="mt-n16" v-model="show" :offsetBottom="68" size="md" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Exportar recebíveis" @onClose="onClose" />
		</template>

		<template v-slot:content>
			<farm-line class="mt-8 mb-4" />
			<farm-row>
				<farm-col>
					<farm-idcaption icon="calendar" copyText="">
						<template v-slot:title>
							<farm-heading :type="6"> Selecionar Período </farm-heading>
						</template>
						<template v-slot:subtitle>
							Defina um intervalo de datas para realizar a exportação dos recebíveis.
						</template>
					</farm-idcaption>
				</farm-col>
			</farm-row>

			<farm-row class="mt-4">
				<farm-col md="12">
					<farm-label for="form-period-history" required class="mb-0"
						>Selecione o Período</farm-label
					>
				</farm-col>
				<farm-col cols="12" md="12">
					<farm-select
						id="form-period-history"
						class="mt-1"
						item-text="text"
						item-value="value"
						v-model="periodModel"
						:items="items"
						hint="Apenas recebíveis dentro de importações concluídas podem ser exportados."
						:persistentHint="true"
					/>
				</farm-col>
			</farm-row>

			<farm-row v-if="periodModel === 'custom'">
				<farm-col md="12">
					<farm-label for="form-period-history" required class="mb-0"
						>Período Personalizado (Início/fim)</farm-label
					>
				</farm-col>
				<farm-col cols="12" md="12">
					<RangeDatePicker inputId="input-custom-id-0" v-model="dateRange" />
				</farm-col>
			</farm-row>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Exportar"
				closeLabel="Cancelar"
				@onConfirm="onConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { ref } from 'vue';

export default {
	name: 'ExportHistoryModal',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	emits: ['close', 'confirm'],

	setup(props, { emit }) {
		const dateRange = ref<string[]>();
		const items = [
			{
				text: '7 dias',
				value: '7_days',
			},
			{
				text: '15 dias',
				value: '15_days',
			},
			{
				text: '30 dias',
				value: '30_days',
			},
			{
				text: 'Personalizado',
				value: 'custom',
			},
		];
		const periodModel = ref(null);

		function onClose(): void {
			emit('close');
		}

		function onConfirm(): void {
			emit('confirm', {
				period: periodModel.value,
				dateRange: dateRange.value,
			});
		}

		return {
			dateRange,
			items,
			periodModel,
			onConfirm,
			onClose,
		};
	},
};
</script>

<style lang="scss" scoped></style>
