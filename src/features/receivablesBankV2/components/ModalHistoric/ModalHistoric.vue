<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" :size="size">
		<template v-slot:header>
			<farm-dialog-header :title="titleModal" @onClose="onCloseModal" />
		</template>
		<template v-slot:content>
			<information-historic-status
				v-if="!isError && data.statusId === 4"
				class="mb-4"
				:status="data.statusId"
			>
				<farm-row class="mb-4">
					<farm-col cols="12" md="4"
						><span class="font-weight-bold"
							>A importação foi concluída com sucesso!</span
						></farm-col
					>
				</farm-row>
				<farm-row>
					<farm-col cols="12" md="4">
						<label for="search-receivable" class="text-sm font-semibold">
							Buscar Recebível
						</label>
						<farm-tooltip>
							<farm-caption variation="semiBold" color="white">
								Buscar Recebível
							</farm-caption>
							<farm-caption variation="regular" color="white">
								Realize sua busca pela Chave de Acesso da<br />
								NF-e
							</farm-caption>
							<template v-slot:activator>
								<farm-icon size="sm" color="gray" class="mx-1"
									>help-circle</farm-icon
								>
							</template>
						</farm-tooltip>
						<farm-textfield-v2
							class="mt-3 mb-n5"
							id="search-receivable"
							v-model="searchReceivable"
							@input="updateSearchReceivable"
							:disabled="isLoading"
						/>
					</farm-col>
				</farm-row>
				<collapsible-receivables-approved-bank
					v-if="!isLoading"
					class="mt-3"
					:data="historicBatchDetail"
					:search="searchReceivable"
				/>
				<collapsible-deny-reasons-bank
					v-if="!isLoading"
					class="mt-3"
					:data="historicBatchDetail"
					:search="searchReceivable"
				/>
			</information-historic-status>
			<information-historic-status
				v-if="!isError && data.statusId === 3"
				class="mb-4"
				:status="data.statusId"
			>
				Ocorreu um erro ao realizar a importação! Entre em contato com o responsável da sua
				conta para mais informações.
			</information-historic-status>
			<information-historic-status
				v-if="!isError && data.statusId === 1"
				class="mb-4"
				:status="data.statusId"
			>
				A importação está em andamento e assim que for concluida, será possível visualizar o
				resultado final.
			</information-historic-status>
			<div v-if="!isError && data.statusId === 2">
				<information-historic-status
					v-if="data.statusId === 2"
					class="mb-4"
					:status="data.statusId"
				>
					Após a validação seus recebíveis serão disponibilizados no banco de recebíveis.
					Após a conclusão desse processo, você poderá visualizar os resultados mais
					detalhados da validação.
				</information-historic-status>
				<processing-receivables v-if="!isLoading" :data="historicBatchDetail" />
			</div>
			<farm-loader mode="overlay" v-if="isLoading" />
			<div v-if="isError" class="mt-10 mb-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Fechar"
				:hasCancel="false"
				@onConfirm="onCloseModal"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref, onMounted, computed } from 'vue';

import { useDebounce } from '@/composible/useDebounce';

import { useHistoricBatchDetail } from '../../composables/useHistoricBatchDetail';
import CollapsibleDenyReasons from '../CollapsibleDenyReasons';
import CollapsibleReceivablesApproved from '../CollapsibleReceivablesApproved';
import InformationHistoricStatus from '../InformationHistoricStatus';
import ProcessingReceivables from '../ProcessingReceivables';

export default defineComponent({
	name: 'modal-historic',
	components: {
		InformationHistoricStatus,
		'collapsible-deny-reasons-bank': CollapsibleDenyReasons,
		'collapsible-receivables-approved-bank': CollapsibleReceivablesApproved,
		ProcessingReceivables,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		data: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data } = toRefs(props);

		const { getHistoricBatchDetail, historicBatchDetail, isLoadingHistoricBatchDetail } =
			useHistoricBatchDetail();

		const searchReceivable = ref('');
		const size = ref('md');
		const titleModal = ref('');
		const filterReceivablesApproved = ref({
			page: 0,
			limit: 10,
		});
		const isError = ref(false);

		const isLoading = computed(() => {
			return isLoadingHistoricBatchDetail.value;
		});
		const updateSearchReceivable = useDebounce((value: string) => {
			const id = data.value.id;
			getHistoricBatchDetail(id, updatedPage, searchReceivable.value, {
				page: 0,
				limit: 10,
			});
			searchReceivable.value = value;
		}, 500);

		function getModalTitle(status: number): string {
			const titles = {
				1: 'Importação',
				2: 'Validação de Recebível',
				3: 'Importação',
				4: 'Importação de Recebíveis',
			};
			return titles[status];
		}

		function onCloseModal(): void {
			emit('onClose');
		}

		function checkSizeModal(): void {
			if (data.value.statusId === 4 || data.value.statusId === 2) {
				size.value = 'default';
			}
		}

		function updatedTitleModal(): void {
			titleModal.value = getModalTitle(data.value.statusId);
		}

		function updatedPage(hasError: boolean): void {
			if (hasError) {
				isError.value = true;
				return;
			}
			isError.value = false;
		}

		function load(): void {
			const id = data.value.id;
			getHistoricBatchDetail(id, updatedPage);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			checkSizeModal();
			updatedTitleModal();
			load();
		});

		return {
			isLoading,
			isError,
			titleModal,
			size,
			data,
			historicBatchDetail,
			filterReceivablesApproved,
			searchReceivable,
			onCloseModal,
			onReload,
			updateSearchReceivable,
		};
	},
});
</script>
