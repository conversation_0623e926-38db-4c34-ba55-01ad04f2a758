<template>
	<farm-row
		class="d-flex space-between"
		:style="{margin: '0px -24px' }"
	>
		<farm-col cols="12" md="9" class="d-flex space-between">
			<farm-col cols="4">
				<farm-label for="form-filter-emissionDate"> Data de Envio (Início/fim)</farm-label>
				<farm-input-rangedatepicker
					ref="datepickerEmissionDate"
					inputId="form-filter-emission-date"
					v-model="emissionDate"
					@input="onInputDatepickerEmissionDate"
				/>
			</farm-col>
			<farm-col cols="4">
				<farm-label for="form-filtro-status"> Status </farm-label>
				<farm-select
					id="form-filtro-status"
					item-text="text"
					item-value="id"
					v-model="modelStatus"
					:items="dataStatus"
				/>
			</farm-col>
			<farm-col cols="4">
				<farm-label for="form-filtro-type"> Tipo </farm-label>
				<farm-select
					id="form-filtro-type"
					item-text="text"
					item-value="id"
					v-model="modelTypes"
					:items="dataTypes"
				/>
			</farm-col>
		</farm-col>

		<farm-col cols="12" class="mb-4 ml-1">
			<div class="d-flex">
				<farm-btn-confirm title="Buscar" outlined @click="onClickFilter">
					Aplicar Filtros
				</farm-btn-confirm>
				<farm-btn plain depressed title="Limpar Filtros" @click="onClearFilters">
					Limpar Filtro
				</farm-btn>
			</div>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name: 'historic-filter',
	props: {
		dataStatus: {
			type: Array,
			default: () => [],
		},
		dataTypes: {
			type: Array,
			default: () => [],
		},
	},
	setup(_, { emit }) {
		const modelOrder = ref('createdAt_DESC');
		const modelStatus = ref('');
		const modelTypes = ref('');
		const emissionDate = ref([]);

		function checkValueDatepicker(value: string) {
			if (value.length === 2) {
				const startDate = new Date(value[0]);
				const endDate = new Date(value[1]);
				if (startDate > endDate) {
					return [value[1], value[0]];
				}
			}
			return value;
		}

		function onInputDatepickerEmissionDate(value: string): void {
			emissionDate.value = checkValueDatepicker(value);
		}

		function createDataOrderBy() {
			return {
				order: modelOrder.value.split('_')[1],
				orderby: modelOrder.value.split('_')[0],
			};
		}

		function formatDate(date: string) {
			if (!date) return null;
			return new Date(date).toISOString().split('T')[0];
		}

		function onClickFilter(): void {
			const sort = createDataOrderBy();
			const data = {
				status: modelStatus.value,
				type: modelTypes.value,
				createdAtStart: emissionDate.value[0] ? formatDate(emissionDate.value[0]) : null,
				createdAtEnd: emissionDate.value[1] ? formatDate(emissionDate.value[1]) : null,
				...sort,
			};
			emit('onRequestFilter', data);
		}

		function onClearFilters(): void {
			modelStatus.value = '';
			modelTypes.value = '';
			emissionDate.value = [];

			const sort = createDataOrderBy();
			emit('onRequestFilter', {
				status: '',
				type: '',
				createdAtStart: null,
				createdAtEnd: null,
				...sort,
			});
		}

		return {
			emissionDate,
			onInputDatepickerEmissionDate,
			modelStatus,
			modelTypes,
			onClickFilter,
			onClearFilters,
		};
	},
});
</script>
