<template>
	<GeneralInfoModal
		v-model="value"
		:data="receivablesCurrent"
		@onClose="onClose"
	/>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import GeneralInfoModal from '@/components/GeneralInfoModal/GeneralInfoModal.vue';

export default defineComponent({
	name: 'modal-detail-receivables-bank',
	components: {
		GeneralInfoModal,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		receivablesCurrent: {
			type: Object,
			required: true,
		},
	},
	emits: ['update:value', 'onClose'],
	setup(props, { emit }) {
		function onClose() {
			emit('onClose');
		}
		return {
			onClose,
		};
	},
});
</script>

<style scoped lang="scss">
@import './ModalDetailReceivablesBank.scss';
</style>
