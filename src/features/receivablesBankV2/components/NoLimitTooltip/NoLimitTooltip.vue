<template>
  <v-tooltip top>
    <template v-slot:activator="{ on, attrs }">
      <div
        class="d-inline-flex rounded-circle pa-1"
        style="background-color: rgba(255, 0, 0, 0.1)"
        v-bind="attrs"
        v-on="on"
      >
        <img
          alt="imagem referente a sacado sem limite"
          class="page-error-img"
          src="@/assets/icons/money_off.svg"
          :style="{ width: '16px'}"
        />
      </div>
    </template>
    <span class="custom-tooltip">
      <div class="mb-2 mt-2 mr-2 ml-2">
        <v-icon small color="#FFF">mdi-currency-usd-off</v-icon>
        <strong> Sacado sem Limite Disponível</strong>
      </div>
      <div class="mb-2 mt-2 mr-2 ml-2">
        Este sacado não possui limite disponível para realizar uma cessão.<br />
        Realize uma nova solicitação clicando no botão:<br />
        Visualizar Limite dos Sacados → Solicitar Limite em Lote.
      </div>
    </span>
  </v-tooltip>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'no-limit-tooltip',
});
</script>

<style lang="scss" scoped>
.rounded-circle {
  margin-left: 0.45rem;
  cursor: pointer;
}
</style> 