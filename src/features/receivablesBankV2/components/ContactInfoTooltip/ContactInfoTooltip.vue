<template>
	<farm-tooltip position="top-right" fluid>
		<template #title>
			<div>
				<farm-icon class="icon-title mr-2" size="sm" color="white">alert</farm-icon>
				<strong> Sacado sem Celular e/ou E-mail</strong>
			</div>
		</template>
		<span>
			Este sacado não possui Celular e/ou E-mail cadastrado em nossa base. Para atualizar as
			informações, entre em contato com o responsável pela sua conta na Farmtech.
		</span>
		<template #activator>
			<span class="icon-container">
				<img
					alt="imagem referente a erro na simulação ou cessão"
					src="@/assets/icons/phone-disabled.svg"
					:style="{ width: '16px', height: '16px' }"
				/>
			</span>
		</template>
	</farm-tooltip>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'contact-info-tooltip',
});
</script>

<style lang="scss" scoped>
.icon-title {
	transform: scale(1.2);
}

.icon-container {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	background-color: #ffebee;
	cursor: pointer;
	line-height: 24px;
	font-weight: 700;
	margin-left: 0.45rem;
}
</style>
