<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				v-if="!isError"
				hide-default-footer
				class="elevation-0 ml-2 table-receivables-approved-bank"
				id="table-receivables-approved-bank"
				item-key="key"
				:items="data"
				:headers="headers"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<FilterEmptyState
						receivables
						subtitle="Nenhum recebível foi validado."
						class="mt-8 mb-4"
					/>
				</template>
				<template v-slot:header="{ props }" v-if="showCustomHeader">
					<farm-datatable-header
						:headers="props.headers"
						:sortClick="sortClicked"
						:selectedIndex="2"
						@onClickSort="onSort"
					/>
				</template>
				<template v-slot:[`item.expirationDate`]="{ item }">
					{{ formatDate(item.expirationDate) }}
				</template>
				<template v-slot:[`item.emissionDate`]="{ item }">
					{{ formatDate(item.emissionDate) }}
				</template>
				<template v-slot:[`item.value`]="{ item }">
					{{ formatMoney(item.value) }}
				</template>
				<template v-slot:[`item.infos`]="{ item }">
					<farm-icon
						class="cursor-pointer mr-3 ml-n3"
						color="gray"
						size="md"
						@click="openInfoModal(item.number)"
					>
						open-in-new
					</farm-icon>
				</template>
				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="currentPage"
						:totalPages="pagination.totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangeLimitPerPage"
					/>
				</template>
			</v-data-table>
			<div v-else>
				<FilterEmptyState
					subtitle="Tente filtrar novamente sua pesquisa."
					class="mt-8 mb-4"
				/>
			</div>
		</farm-box>

		<GeneralInfoModal :value="showInfoModal" :data="modalInfo" @onClose="closeInfoModal" />
	</farm-row>
</template>

<script lang="ts">
import { computed, defineComponent, getCurrentInstance, ref, toRefs } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import FilterEmptyState from '@/components/FilterEmptyState';
import GeneralInfoModal from '@/components/GeneralInfoModal/GeneralInfoModal.vue';


import { headers } from './configurations';

export default defineComponent({
	name: 'table-receivables-approved-bank',
	components: {
		FilterEmptyState,
		GeneralInfoModal,
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
		pagination: {
			type: Object,
			required: true,
		},
		filter: {
			type: Object,
			required: true,
		},
		isError: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const showInfoModal = ref(false);
		const selectedInfoRow = ref<number>(null);
		const modalInfo = ref({});

		const { filter } = toRefs(props);

		const internalInstance: any = getCurrentInstance();

		const breakpoint = computed(() => {
			return internalInstance.proxy.$vuetify.breakpoint.name;
		});
		const showCustomHeader = computed(() => {
			return breakpoint !== 'xs';
		});

		const sortClicked = ref([]);
		const currentPage = ref(1);
		const headerProps = ref({
			sortByText: 'Ordenar por',
		});

		function onChangePage(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			currentPage.value = pageActive + 1;
			emit('onRequest', { page: pageActive, limit: filter.value.limit });
		}

		function onChangeLimitPerPage(limit: number): void {
			currentPage.value = 1;
			emit('onRequest', { page: 0, limit: limit });
		}

		function onSort(data): void {
			const requestData = {
				order: data.descending,
				orderby: data.field,
				page: filter.value.page,
				limit: filter.value.limit,
			};
			emit('onRequest', requestData);
		}

		function closeInfoModal() {
			showInfoModal.value = false;
		}

		function openInfoModal(id: number) {
			selectedInfoRow.value = id;
			modalInfo.value = props.data.find((item: any) => item.number === id);
			showInfoModal.value = true;
		}

		return {
			filter,
			currentPage,
			showCustomHeader,
			headerProps,
			sortClicked,
			headers,
			selectedInfoRow,
			modalInfo,
			showInfoModal,
			formatDate: defaultDateFormat,
			formatMoney: brl,
			onChangePage,
			onChangeLimitPerPage,
			onSort,
			openInfoModal,
			closeInfoModal,
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-receivables-approved-bank', 1, (0));
</style>
<style lang="scss" scoped>
@import './TableReceivablesApprovedBank';
</style>
