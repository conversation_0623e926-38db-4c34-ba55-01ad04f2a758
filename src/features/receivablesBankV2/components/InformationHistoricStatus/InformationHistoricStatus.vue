<template>
	<farm-row>
		<farm-col cols="3">
			<historic-status class="px-9" dense :status="status" />
		</farm-col>
		<farm-col cols="12" class="mt-3">
			<farm-typography size="sm" color="black" colorVariation="darken">
				<slot></slot>
			</farm-typography>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import HistoricStatus from '../HistoricStatus';

export default defineComponent({
	name: 'information-historic-status',
	components: {
		HistoricStatus,
	},
	props: {
		status: {
			type: Number,
			required: true,
		},
	},
});
</script>
