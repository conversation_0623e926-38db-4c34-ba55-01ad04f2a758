<template>
	<farm-card>
		<farm-card-content gutter="md">
			<farm-row align="center">
				<farm-col cols="12" md="8">
					<div class="header-card">
						<div class="content-icon">
							<farm-icon size="18px" color="black"> cancel </farm-icon>
						</div>
						<farm-typography class="mx-1" color="black">
							Recebíveis Não Validados (
							<farm-typography class="mx-1" color="error" tag="span">
								{{ totals }}
							</farm-typography>
							)
						</farm-typography>
					</div>
				</farm-col>
				<farm-col cols="12" md="4" align="end">
					<farm-btn
						class="mr-2"
						outlined
						:disabled="isDisabled"
						@click.stop="onExportDenyReasons"
					>
						<farm-icon size="md">download-outline</farm-icon> Exportar
					</farm-btn>
					<farm-btn icon @click="onToggleDetails" class="originator-card-button">
						<farm-icon size="md">{{ showDetails ? 'menu-up' : 'menu-down' }}</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-card-content>
		<collapse-transition :duration="300" v-show="showDetails">
			<farm-card-content gutter="md">
				<table-deny-reasons-xml
					v-if="data.typeId !== 2"
					:data="denyReasons"
					:pagination="denyReasonsPagination"
					:filter="filter"
					:is-error="denyReasonsError.error"
					@onRequest="onRequest"
				/>
				<table-deny-reasons-excel
					v-if="data.typeId === 2"
					:data="denyReasons"
					:pagination="denyReasonsPagination"
					:filter="filter"
					:is-error="denyReasonsError.error"
					@onRequest="onRequest"
				/>
			</farm-card-content>
		</collapse-transition>
		<farm-loader v-if="isLoading" mode="overlay" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref, onMounted, computed } from 'vue';

import FilterEmptyState from '@/components/FilterEmptyState';

import { useDenyReasons } from '../../composables/useDenyReasons';
import { useExportDenyReasons } from '../../composables/useExportDenyReasons';
import TableDenyReasonsExcel from '../TableDenyReasonsExcel';
import TableDenyReasonsXml from '../TableDenyReasonsXml';

export default defineComponent({
	name: 'collapsible-deny-reasons-bank',
	components: {
		TableDenyReasonsXml,
		TableDenyReasonsExcel,
		FilterEmptyState,
	},
	props: {
		data: {
			type: Object,
			require: true,
		},
		search: {
			type: String,
			default: '',
		},
	},
	setup(props) {
		const { data } = toRefs(props);
		const { exportDenyReasons } = useExportDenyReasons();
		const {
			isLoadingDenyReasons,
			getDenyReasons,
			denyReasonsError,
			denyReasons,
			denyReasonsPagination,
		} = useDenyReasons(data.value.typeId);
		denyReasons.value = [];

		const totals = ref(0);
		const showDetails = ref(false);
		const isDisabled = ref(true);
		const isError = ref(false);
		const firstRender = ref(true);
		const emptyStateSubtitle = computed(() => {
			return denyReasonsError.value.error
				? 'Tente filtrar novamente sua pesquisa.'
				: 'Nenhum recebível foi validado.';
		});
		const filter = ref({
			page: 0,
			limit: 10,
		});

		const isLoading = computed(() => {
			return isLoadingDenyReasons.value;
		});

		function onRequest(value): void {
			const params = {
				...value,
			};
			if (value.search) {
				params.search = value.search;
			}
			filter.value = {
				...filter.value,
				page: value.page || 0,
				limit: value.limit || 10,
			};
			getDenyReasons(data.value.id, params, updatedPage);
		}

		function onToggleDetails(): void {
			if (isError.value) {
				isError.value = false;
			}
			if (!showDetails.value) {
				load();
				firstRender.value = true;
				return;
			}
			showDetails.value = !showDetails.value;
		}

		function updatedPage(hasError): void {
			if (hasError) {
				isError.value = true;
				return;
			}
			isError.value = false;
			if (firstRender.value) {
				showDetails.value = !showDetails.value;
				firstRender.value = false;
			}
		}

		function onExportDenyReasons(): void {
			const id = data.value.id;
			const status = data.value.statusId;
			exportDenyReasons(id, status);
		}

		function init(): void {
			totals.value = data.value.notApproved;
			isDisabled.value = data.value.notApproved === 0;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const params = {
				page: 0,
				limit: 10,
			};
			filter.value = { ...params };
			getDenyReasons(data.value.id, { ...params, search: props.search }, updatedPage);
		}

		onMounted(() => {
			init();
		});

		return {
			isLoading,
			isError,
			denyReasons,
			totals,
			showDetails,
			isDisabled,
			data,
			denyReasonsPagination,
			denyReasonsError,
			filter,
			emptyStateSubtitle,
			onToggleDetails,
			onExportDenyReasons,
			onReload,
			onRequest,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CollapsibleDenyReasons';
</style>
