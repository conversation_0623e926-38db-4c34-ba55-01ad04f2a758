import { ref, Ref } from 'vue';

import { downloadFileByRange } from '@/features/receivablesBankV2/services';

type ServiceState = 'IDLE' | 'LOADING' | 'SUCCESS' | 'ERROR';

export type ProductHeader = {
	id: number;
	name: string;
	type: string;
};

export function useDownloadFileByRange() {
	const state: Ref<ServiceState> = ref('IDLE');
	const data = ref<ProductHeader | null>(null);
	const error = ref<string | null>(null);

	const sendToDownloadFileByRange = async ({ productId, startAt, endAt }) => {
		error.value = null;

		try {
			return downloadFileByRange({ productId, startAt, endAt });
		} catch (err: any) {
			error.value = err?.response?.data?.message || 'An unexpected error occurred.';
			state.value = 'ERROR';
		}
	};

	return {
		state,
		data,
		error,
		sendToDownloadFileByRange,
	};
}
