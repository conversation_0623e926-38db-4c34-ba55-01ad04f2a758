<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<historic-card :data="item" :types="types" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<FilterEmptyState class="mt-8" :receivables="!hasError" :subtitle="subtitle" />
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import FilterEmptyState from '@/components/FilterEmptyState/FilterEmptyState.vue';

import HistoricCard from '../HistoricCard';

export default defineComponent({
	name: 'historic-list',
	components: {
		HistoricCard,
		FilterEmptyState,
	},
	props: {
		data: {
			type: Array as () => { id: string }[],
			default: () => [],
		},
		types: {
			type: Array as () => { id: string; type: string }[],
			default: () => [],
		},
		hasError: {
			type: Boolean,
			default: false,
		},
	},
	setup(props) {
		const { data } = toRefs(props);
		const subtitle = computed(() => {
			return !props.hasError
				? 'Tente realizar uma nova importação de recebíveis.'
				: 'Tente filtrar novamente sua pesquisa.';
		});

		const isDataEmpty = computed(() => {
			return data.value.length === 0;
		});

		return {
			isDataEmpty,
			subtitle,
		};
	},
});
</script>
