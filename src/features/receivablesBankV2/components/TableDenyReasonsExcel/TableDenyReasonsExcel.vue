<template>
	<farm-row extra-decrease>
		<farm-box>
			<v-data-table
				v-if="!isError"
				hide-default-footer
				class="elevation-0 ml-2 table-deny-reasons-excel"
				id="table-deny-reasons-excel"
				item-key="key"
				:items="data"
				:headers="headers"
				:server-items-length="data.length"
				:hide-default-header="showCustomHeader"
				:header-props="headerProps"
			>
				<template slot="no-data">
					<FilterEmptyState receivables class="my-4" />
				</template>
				<template v-slot:header="{ props }" v-if="showCustomHeader">
					<farm-datatable-header
						:headers="props.headers"
						:sortClick="sortClicked"
						:selectedIndex="2"
						@onClickSort="onSort"
					/>
				</template>
				<template v-slot:footer>
					<farm-datatable-paginator
						v-if="data.length > 0"
						class="mt-6 mb-n6"
						:page="currentPage"
						:totalPages="pagination.totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangeLimitPerPage"
					/>
				</template>
			</v-data-table>
			<div v-else>
				<FilterEmptyState
					subtitle="Tente filtrar novamente sua pesquisa."
					class="mt-8 mb-4"
				/>
			</div>
		</farm-box>
	</farm-row>
</template>

<script lang="ts">
import { computed, defineComponent, getCurrentInstance, ref, toRefs } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import FilterEmptyState from '@/components/FilterEmptyState/FilterEmptyState.vue';

import { headers } from './configurations';

export default defineComponent({
	name: 'table-deny-reasons-excel',
	components: {
		FilterEmptyState,
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
		pagination: {
			type: Object,
			required: true,
		},
		filter: {
			type: Object,
			required: true,
		},
		isError: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		const { filter } = toRefs(props);

		const internalInstance: any = getCurrentInstance();

		const breakpoint = computed(() => {
			return internalInstance.proxy.$vuetify.breakpoint.name;
		});
		const showCustomHeader = computed(() => {
			return breakpoint !== 'xs';
		});

		const sortClicked = ref([]);
		const itemSelected = ref(null);
		const currentPage = ref(1);
		const headerProps = ref({
			sortByText: 'Ordenar por',
		});

		function onChangePage(page: number): void {
			const pageActive = page === 1 ? 0 : page - 1;
			currentPage.value = pageActive + 1;
			emit('onRequest', { page: pageActive, limit: filter.value.limit });
		}

		function onChangeLimitPerPage(limit: number): void {
			currentPage.value = 1;
			emit('onRequest', { page: 0, limit: limit });
		}

		function onSort(data): void {
			const requestData = {
				order: data.descending,
				orderby: data.field,
				page: filter.value.page,
				limit: filter.value.limit,
			};
			emit('onRequest', requestData);
		}

		return {
			currentPage,
			showCustomHeader,
			headerProps,
			sortClicked,
			itemSelected,
			headers,
			formatDate: defaultDateFormat,
			formatMoney: brl,
			onChangePage,
			onChangeLimitPerPage,
			onSort,
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.table-deny-reasons-excel', 1, (0));
</style>
<style lang="scss" scoped>
@import './TableDenyReasonsExcel';
</style>
