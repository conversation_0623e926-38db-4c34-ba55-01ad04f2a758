<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Status </farm-label>
					<farm-select
						id="form-filter-status"
						v-model="status"
						:items="statusItems"
						item-text="label"
						item-value="id"
					/>
					<div class="mt-2">
						<farm-label for="form-filter-switch1"> Exibir Apenas Sacados Sem Limite? </farm-label>
						<div class="d-flex">
							<farm-switcher
								class="mt-1"
								id="form-filter-switch1"
								v-model="noLimitDrawees"
								block
							></farm-switcher>
							<farm-bodytext
								class="ml-3 mt-1"
								color="neutral"
								colorVariation="darken"
								variation="regular"
								:type="2"
							>
								{{ noLimitDrawees ? 'Sim': 'Não' }}
							</farm-bodytext>
						</div>
					</div>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-emissionDate">
						Data de Emissão (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerIssueDateDate"
						inputId="form-filter-emissionDate"
						v-model="emissionDateRange"
						@input="onInputEmissionDateRange"
					/>
					<div class="mt-2">
						<farm-label for="form-filter-switch2"> Exibir Apenas Sacados Sem Contato? </farm-label>
						<div class="d-flex">
							<farm-switcher
								class="mt-1"
								id="form-filter-switch2"
								v-model="noContactDrawees"
								block
							></farm-switcher>
							<farm-bodytext
								class="ml-3 mt-1"
								color="neutral"
								colorVariation="darken"
								variation="regular"
								:type="2"
							>
								{{ noContactDrawees ? 'Sim': 'Não' }}
							</farm-bodytext>
						</div>
					</div>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-expiredAtRange">
						Data de Vencimento (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerExpirationDate"
						inputId="form-filter-expiredAtRange"
						v-model="expiredAtRange"
						@input="onInputRangedatepicker"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Valor Nominal </farm-label>
					<farm-select
						id="form-filter-status"
						v-model="nominalValue"
						:items="nominalItems"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
			</farm-row>
			<farm-row class="mt-6">
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="onFilterConfirm"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';

import { receivableStatus, nominalValueData } from '../../constants';

export default defineComponent({
	name:'home-filter',
	setup(_, { emit }) {

		const { trigger } = useAnalytics();

		const status = ref('');
		const statusItems = ref(receivableStatus);
		const emissionDateRange = ref([]);
		const expiredAtRange = ref([]);
		const nominalValue = ref('');
		const nominalItems = ref(nominalValueData);
		const noLimitDrawees = ref(false);
		const noContactDrawees = ref(false);

		const filters = ref({
			status: status.value,
			value: null,
			expirationDateStart: null,
			expirationDateEnd: null,
			emissionDateStart: null,
			emissionDateEnd: null,
			noLimitDrawees: false,
			noContactDrawees: false
		});

		function onFilterClear(): void {
			filters.value = {
				status: null,
				value: null,
				expirationDateStart: null,
				expirationDateEnd: null,
				emissionDateStart: null,
				emissionDateEnd: null,
				nominalValue: null,
				noLimitDrawees: false,
				noContactDrawees: false
			};
			status.value = null;
			nominalValue.value = null;
			expiredAtRange.value = [];
			emissionDateRange.value = [];
			noLimitDrawees.value = false;
			noContactDrawees.value = false;
			const dataTrigger = {
				event: 'receivables_bank',
				payload:{
					description:'clicou na opção de limpar filtro',
				}
			};
			trigger(dataTrigger);
			emit('onApply', { ...filters.value });
			emit('onFiltersApplied',false);
		}

		function onFilterConfirm(): void {
			if(expiredAtRange.value.length > 0){
				filters.value.expirationDateStart = expiredAtRange.value[0];
				filters.value.expirationDateEnd = expiredAtRange.value[1];
			}
			if(emissionDateRange.value.length > 0){
				filters.value.emissionDateStart = emissionDateRange.value[0];
				filters.value.emissionDateEnd = emissionDateRange.value[1];
			}
			const dataTrigger = {
				event: 'receivables_bank',
				payload: {
					description:'clicou na opção de aplicar filtro',
					filters: {
						status: status.value || "N/A",
						dateExpired: (filters.value.expirationDateStart || "N/A") +' até '+ (filters.value.expirationDateEnd || "N/A"),
						issueDate: (filters.value.emissionDateStart || "N/A") +' até '+ (filters.value.emissionDateEnd || "N/A"),
						nominalValue: nominalValue.value || "N/A",
						noLimitDrawees: noLimitDrawees.value ? "Sim" : "Não",
						noContactDrawees: noContactDrawees.value ? "Sim" : "Não"
					}
				}
			};
			trigger(dataTrigger);
			const payload = {
				...filters.value,
				value: nominalValue.value || null,
				status: status.value || null,
				noLimitDrawees: noLimitDrawees.value,
				noContactDrawees: noContactDrawees.value
			};
			emit('onApply', payload);
			emit('onFiltersApplied',true);
		}

		function getValueDates(value: Array<string>): Array<string>{
			const startDate = new Date(value[0]);
			const endDate = new Date(value[1]);
			if (startDate > endDate) {
				return [value[1], value[0]];
			}
			return value;
		}

		function onInputRangedatepicker(value: Array<string>): void {
			expiredAtRange.value = value.length === 2 ? getValueDates(value): value;
		}

		function onInputEmissionDateRange(value: Array<string>): void {
			emissionDateRange.value = value.length === 2 ? getValueDates(value): value;
		}

		return {
			status,
			statusItems,
			emissionDateRange,
			expiredAtRange,
			nominalValue,
			nominalItems,
			noLimitDrawees,
			noContactDrawees,
			onFilterClear,
			onFilterConfirm,
			onInputEmissionDateRange,
			onInputRangedatepicker
		};
	},
});
</script>
