<template>
	<farm-card>
		<farm-card-content gutter="md">
			<farm-row align="center">
				<farm-col cols="12" md="8">
					<div class="header-card">
						<div class="content-icon">
							<farm-icon size="18px" color="black"> check-circle-outline </farm-icon>
						</div>
						<farm-typography class="mx-1" color="black">
							Recebíveis Validados (
							<farm-typography class="mx-1" color="success" tag="span">
								{{ totals }}
							</farm-typography>
							)
						</farm-typography>
					</div>
				</farm-col>
				<farm-col cols="12" md="4" align="end">
					<farm-btn
						class="mr-2"
						outlined
						:disabled="isDisabled"
						@click.stop="onExportReceivablesApproved"
					>
						<farm-icon size="md">download-outline</farm-icon> Exportar
					</farm-btn>
					<farm-btn icon @click="onToggleDetails" class="originator-card-button">
						<farm-icon size="md">{{ showDetails ? 'menu-up' : 'menu-down' }}</farm-icon>
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-card-content>
		<collapse-transition :duration="300" v-show="showDetails">
			<div>
				<farm-card-content gutter="md">
					<farm-row align="center">
						<farm-col cols="12" align="end">
							<farm-typography class="mb-4" color="black">
								<b>Valor Nominal Total:</b>
								{{ formatMoney(data.totalNominalValue) }}
							</farm-typography>
						</farm-col>
					</farm-row>
					<table-receivables-approved-bank
						v-if="!isError"
						:data="receivablesApproved"
						:pagination="receivablesApprovedPagination"
						:filter="filter"
						:isError="isErrorReceivablesApproved"
						@onRequest="onRequest"
					/>
				</farm-card-content>
			</div>
		</collapse-transition>
		<farm-loader v-if="isLoading" mode="overlay" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent, toRefs, ref, onMounted, computed } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import { useExportReceivablesApproved } from '../../composables/useExportReceivablesApproved';
import { useReceivablesApproved } from '../../composables/useReceivablesApproved';
import TableReceivablesApprovedBank from '../TableReceivablesApprovedBank';

export default defineComponent({
	name: 'collapsible-receivables-approved-bank',
	components: {
		TableReceivablesApprovedBank,
	},
	props: {
		data: {
			type: Object,
			require: true,
		},
		search: {
			type: String,
			default: '',
		},
	},
	setup(props) {
		const { exportReceivablesApproved } = useExportReceivablesApproved();
		const {
			getReceivablesApproved,
			isErrorReceivablesApproved,
			isLoadingReceivablesApproved,
			receivablesApproved,
			receivablesApprovedPagination,
		} = useReceivablesApproved();
		const { data } = toRefs(props);
		const totals = ref(0);
		const showDetails = ref(false);
		const isDisabled = ref(true);
		const isError = ref(false);
		const firstRender = ref(true);
		const filter = ref({
			page: 0,
			limit: 10,
		});

		const isLoading = computed(() => {
			return isLoadingReceivablesApproved.value;
		});

		function onToggleDetails(): void {
			if (isError.value) {
				isError.value = false;
			}
			if (!showDetails.value) {
				load();
				firstRender.value = true;
				return;
			}
			showDetails.value = !showDetails.value;
		}

		function onExportReceivablesApproved(): void {
			const id = data.value.id;
			const status = data.value.statusId;
			exportReceivablesApproved(id, status);
		}

		function onRequest(value): void {
			const params = {
				...value,
			};
			getReceivablesApproved(data.value.id, params, updatedPage);
		}

		function formatMoney(value: number): string {
			if (value === null) {
				return brl(0);
			}
			return brl(value);
		}

		function updatedPage(hasError): void {
			if (hasError) {
				isError.value = true;
				return;
			}
			isError.value = false;
			if (firstRender.value) {
				showDetails.value = !showDetails.value;
				firstRender.value = false;
			}
		}

		function init(): void {
			totals.value = data.value.approved;
			isDisabled.value = data.value.approved === 0;
		}

		function onReload(): void {
			load();
		}

		function load(): void {
			const params = {
				page: 0,
				limit: 10,
			};
			filter.value = { ...params };

			getReceivablesApproved(data.value.id, { ...params, search: props.search }, updatedPage);
		}

		onMounted(() => {
			init();
		});

		return {
			isLoading,
			isError,
			receivablesApproved,
			filter,
			totals,
			isDisabled,
			showDetails,
			data,
			receivablesApprovedPagination,
			isErrorReceivablesApproved,
			formatMoney,
			onToggleDetails,
			onExportReceivablesApproved,
			onRequest,
			onReload,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CollapsibleReceivablesApproved';
</style>
