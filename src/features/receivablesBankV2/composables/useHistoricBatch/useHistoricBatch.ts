import { computed, Ref, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderHistoricBatch } from '@/features/receivablesBankV2/helpers/builderHistoricBatch';
import { getHistoricBatch as getHistoricBatchService } from '@/features/receivablesBankV2/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseHistoricBatch = {
	historicBatch: any;
	historicBatchPagination: any;
	isLoadingHistoricBatch: Ref<boolean>;
	historicBatchError: Ref<boolean>;
	getHistoricBatch: Function;
	totalHistoricBatch: Ref<number>;
};

type MutationParams = {
	productId: string;
	filters: string;
};
export function useHistoricBatch(): UseHistoricBatch {
	let callFunc: Function | null = null;
	const historicBatch = ref([]);
	const historicBatchPagination = ref(null);
	const productId = useSelectedProductId().value;
	const totalHistoricBatch = ref(0);
	const historicBatchError = ref(false);

	const { isLoading, mutate } = useMutation({
		mutationFn: (params: MutationParams) => getHistoricBatchService(params),
		onSuccess: response => {
			const data = builderHistoricBatch(response);
			historicBatch.value = data.content;
			historicBatchPagination.value = data.pagination;
			totalHistoricBatch.value = data.pagination.totalElements;
			if (callFunc) callFunc(false);
		},
		onError: error => {
			historicBatch.value = [];

			if (isHttpRequestError(error, 404)) {
				historicBatchError.value = true;
				historicBatchPagination.value = {
					pageNumber: 0,
					pageSize: 0,
					sort: null,
					totalElements: 0,
					totalPages: 0,
				};
				if (callFunc) callFunc(false);
				return;
			}
			historicBatchPagination.value = null;
			if (callFunc) callFunc(true);
		},
	});

	function getHistoricBatch(filters, callback: Function): void {
		const params = {
			...filters,
			page: filters.page || 0,
			limit: filters.limit || 10,
		};
		mutate({ productId, filters: queryString(params, {}) });
		callFunc = callback;
	}

	const isLoadingHistoricBatch = computed(() => {
		return isLoading.value;
	});

	return {
		totalHistoricBatch,
		historicBatch,
		historicBatchPagination,
		isLoadingHistoricBatch,
		historicBatchError,
		getHistoricBatch,
	};
}
