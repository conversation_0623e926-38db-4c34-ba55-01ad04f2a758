import { computed, ref } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { builderTypesHistoricFilter } from '@/features/receivablesBankV2/helpers/builderTypesHistoricFilter';
import { getTypesHistoric as getTypesHistoricService } from '@/features/receivablesBankV2/services';

type UseTypeFilter = {
	types: {
		id: string;
		type: string;
	}[];
	isLoadingTypesFilter: {
		value: boolean
	};
	isErrorTypesFilter: {
		value: boolean
	};
	getTypesFilter: Function;
}
export function useTypeFilter(): UseTypeFilter {
	const types = ref([]);

	const { isError, isLoading, mutate } = useMutation({
		mutationFn: () => getTypesHistoricService(),
		onSuccess: (response) => {
			types.value = builderTypesHistoricFilter(response);
		},
		onError: () => {
			types.value = [];
		},
	});

	function getTypesFilter(): void {
		mutate();
	}

	const isLoadingTypesFilter = computed(() => {
		return isLoading.value;
	});

	const isErrorTypesFilter = computed(() => {
		return isError.value;
	});

	return {
		types,
		isLoadingTypesFilter,
		isErrorTypesFilter,
		getTypesFilter
	};
}
