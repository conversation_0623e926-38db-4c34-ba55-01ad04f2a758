import { ref, computed, Ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderDenyReasonsExcel } from '@/features/receivablesBankV2/helpers/builderDenyReasonsExcel';
import { builderDenyReasonsXml } from '@/features/receivablesBankV2/helpers/builderDenyReasonsXml';
import { getDenyReasons as getDenyReasonsService } from '@/features/receivablesBankV2/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseDenyReasons = {
	denyReasons: Ref<any[]>;
	denyReasonsPagination: any;
	isLoadingDenyReasons: {
		value: boolean;
	};
	isErrorDenyReasons: {
		value: boolean;
	};
	getDenyReasons: Function;
	denyReasonsError: Ref<{ error: boolean; type: number }>;
};

type MutationParams = {
	productId: string;
	id: number;
	filters: string;
};

export function useDenyReasons(type: number): UseDenyReasons {
	let callFunc: Function | null = null;
	const denyReasons = ref([]);
	const denyReasonsPagination = ref({});
	const productId = useSelectedProductId().value;
	const denyReasonsError = ref({ error: false, type: 0 });

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params: MutationParams) => {
			return getDenyReasonsService(params);
		},
		onSuccess: response => {
			const { content, pagination } =
				type === 2 ? builderDenyReasonsExcel(response) : builderDenyReasonsXml(response);
			denyReasons.value = content;
			denyReasonsPagination.value = pagination;
			if (callFunc) callFunc(false);
		},
		onError: error => {
			denyReasons.value = [];
			if (isHttpRequestError(error, 404)) {
				denyReasonsPagination.value = {
					pageNumber: 0,
					pageSize: 0,
					sort: null,
					totalElements: 0,
					totalPages: 0,
				};
				denyReasonsError.value = { error: true, type: 404 };
				if (callFunc) callFunc(false);
				return;
			}
			denyReasonsPagination.value = null;
			if (callFunc) callFunc(true);
		},
	});

	const isLoadingDenyReasons = computed(() => {
		return isLoading.value;
	});

	const isErrorDenyReasons = computed(() => {
		return isError.value;
	});

	function getDenyReasons(id: number, filters: Record<string, any>, callback: Function): void {
		const params = queryString(filters, {});

		mutate({ productId, id, filters: params });
		callFunc = callback;
	}

	return {
		denyReasons,
		denyReasonsPagination,
		isLoadingDenyReasons,
		isErrorDenyReasons,
		denyReasonsError,
		getDenyReasons,
	};
}
