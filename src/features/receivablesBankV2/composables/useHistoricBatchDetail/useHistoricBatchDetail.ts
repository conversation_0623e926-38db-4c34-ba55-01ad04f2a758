import { computed, Ref, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';
import { AxiosResponse } from 'axios';

import { useSelectedProductId } from '@/composible';
import { getHistoricBatchById as getHistoricBatchByIdService } from '@/features/receivablesBankV2/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseHistoricBatchDetail = {
	historicBatchDetail: any;
	isLoadingHistoricBatchDetail: Ref<boolean>;
	getHistoricBatchDetail: Function;
	historicBatchError: Ref<{ error: boolean; type: string | null }>;
};

type MutationParams = {
	productId: string;
	id: string;
	search?: string;
};

export function useHistoricBatchDetail(): UseHistoricBatchDetail {
	let callFunc: Function | null = null;
	const historicBatchDetail = ref({});
	const productId = useSelectedProductId().value;
	const historicBatchError = ref({ error: false, type: null });

	const { isLoading, mutate } = useMutation({
		mutationFn: (params: MutationParams) => getHistoricBatchByIdService(params),
		onSuccess: (response: AxiosResponse) => {
			historicBatchDetail.value = response.data.content;
			if (callFunc) callFunc(false);
		},
		onError: error => {
			historicBatchDetail.value = [];
			if (isHttpRequestError(error, 404)) {
				historicBatchError.value = { error: true, type: 'notFound' };
				if (callFunc) callFunc(false);
				return;
			}
			if (callFunc) callFunc(true);
		},
	});

	function getHistoricBatchDetail(id: string, callback: Function, search: string): void {
		mutate({ productId, id, search });
		callFunc = callback;
	}

	const isLoadingHistoricBatchDetail = computed(() => {
		return isLoading.value;
	});

	return {
		historicBatchDetail,
		historicBatchError,
		isLoadingHistoricBatchDetail,
		getHistoricBatchDetail,
	};
}
