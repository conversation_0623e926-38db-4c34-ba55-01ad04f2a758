
import { ref } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';
import useRouter from '@/composible/useRouter';
import { importButtonOptions } from '@/features/receivablesBankV2/configurations/importButton';

export type ImportButtonOptionsType = {
	link: string;
	title:  string;
	icon: string;
}

type UseImportButton = {
	buttonImport:{
		link: string;
		title: string
		icon:string
	};
	onClickButtonImport: Function
}

export function useImportButton(): UseImportButton{
	const router = useRouter();
	const { trigger } = useAnalytics();

	const buttonImport = ref(importButtonOptions);

	function onClickButtonImport(url: string): void {
		router.push(`/admin/wallet/banco_de_recebiveis/${url}`);
		const dataTrigger ={
			event: 'receivables_bank',
			payload:{
				description:'clicou na opção de histórico de recebível'
			}
		};
		if(url === '/importacao_de_recebiveis?path=importar'){
			dataTrigger.payload.description = 'clicou na opção de import de recebível';
		}
		trigger(dataTrigger);
	}

	return {
		buttonImport,
		onClickButtonImport
	};
}
