import { brl } from '@farm-investimentos/front-mfe-libs-ts';
import { v4 as uuidv4 } from 'uuid';

import { headerConfigurations } from '../../configurations/headerReceivablesBank';


function parseHeader(meta){
	const data = [
		meta.totalReceivedInvoices || '-',
		meta.totalAvailableReceivedInvoices || '-',
		meta.totalNominalValue  ? parseFloat(meta.totalNominalValue ) : 0,
	];
	const newData = headerConfigurations.map((item, index) =>{
		return{
			...item,
			subtitle: index === 2 ? brl(data[index]) : data[index]
		};
	});
	return newData;
}

export function builderOriginatorsReceivablesBank(response) {
	const { content, meta } = response.data;
	const newData = content.map((item) => {
		return {
			id: item.raiz,
			name: item.name,
			document: item.raiz,
			totalAvailableReceivedInvoices: item.totalAvailableReceivedInvoices,
			totalNominalValue: item.totalNominalValue,
			totalReceivedInvoices: item.totalReceivedInvoices,
			receivedInvoices: item.receivedInvoices
		};
	});
	return {
		content: newData,
		pagination:{
			pageNumber: response.data.page,
			pageSize : response.data.size,
			sort: null,
			totalElements: response.data.totalItems,
			totalPages: response.data.totalPages,
		},
		totals: parseHeader(meta),
		totalFilter: parseInt(meta.totalFilter,10) || 0
	};
}
