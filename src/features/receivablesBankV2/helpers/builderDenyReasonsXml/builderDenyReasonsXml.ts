import { v4 as uuidv4 } from 'uuid';

export function builderDenyReasonsXml(response) {
	const { content } = response.data;
	const newData = content.map((item) => {
		return {
			key: uuidv4(),
			draweeName: item.draweeName,
			draweeDocument: item.draweeDocument,
			number: item.number,
			expirationDate: item.expirationDate,
			emissionDate: item.emissionDate,
			reason: item.reason,
			value: item.value,
			hasContactInfo: item.hasContactInfo,
			cfopCode: item.cfopCode,
			providerName: item.providerName,
			providerDocument: item.providerDocument,
			danfe: item.danfe,
			draweePhone: item.draweePhone,
			draweeEmail: item.draweeEmail,
		};
	});
	return {
		content: newData,
		pagination:{
			pageNumber: response.data.page,
			pageSize : response.data.size,
			sort: null,
			totalElements: response.data.totalItems,
			totalPages: response.data.totalPages,
		}
	};
}
