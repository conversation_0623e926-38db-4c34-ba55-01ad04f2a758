import { environment, file as downloadFileHandler } from '@farm-investimentos/front-mfe-libs-ts';

import { client } from '@/configurations/services/superCession';

const EXTRATIMEOUT = 2000 * 360 * 360;

export const getOriginatorsReceivablesBank = ({ productId, filters }) => {
	const url = `/api/v1/product/${productId}/originators?${filters}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const getReceivablesBank = ({ productId, filters, document }) => {
	const url = `/api/v1/product/${productId}/originators/${document}/received-invoices?${filters}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const uploadReceivedXML = ({ productId, payload }) => {
	const url = `/api/v1/product/${productId}/received-invoices-batch-v2`;
	return client.post(url, payload, {
		timeout: EXTRATIMEOUT,
	});
};

export const uploadReceivedExcel = ({ productId, payload }) => {
	const url = `/api/v1/product/${productId}/received-invoices-batch-xlsx`;
	return client.post(url, payload, {
		timeout: EXTRATIMEOUT,
	});
};

export const downloadExampleSheet = () => {
	const baseURL = environment.apiSuperCessao;
	const url = `/api/v1/product/download-example-sheet?`;
	return downloadFileHandler(`${baseURL}${url}`);
};

export const getStatusHistoric = () => {
	const url = `/api/v1/product/batch-status`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const getTypesHistoric = () => {
	const url = `/api/v1/product/batch-types`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const getHistoricBatch = ({ productId, filters }) => {
	const url = `/api/v1/product/${productId}/received-invoices-batch?${filters}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const getHistoricBatchById = ({
	productId,
	id,
	search,
}: {
	productId: string;
	id: string;
	search?: string;
}) => {
	const url = `/api/v1/product/${productId}/received-invoices-batch/${id}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
		params: {
			search,
		},
	});
};

export const downloadHistoricFile = ({ productId, id }) => {
	const baseURL = environment.apiSuperCessao;
	const url = `/api/v1/product/${productId}/received-invoices-batch/${id}/download-zip?`;
	downloadFileHandler(`${baseURL}${url}`);
};

export const downloadFileByRange = ({ productId, startAt, endAt }) => {
	const baseURL = environment.apiSuperCessao;
	const url = `/api/v1/product/${productId}/received-invoices/export-by-range?startAt=${startAt}&endAt=${endAt}`;
	downloadFileHandler(`${baseURL}${url}`);
};

export const exportApprovedReceivables = ({ productId, id }) => {
	const baseURL = environment.apiSuperCessao;
	const url = `/api/v1/product/${productId}/received-invoices-batch/${id}/export-entries?`;
	downloadFileHandler(`${baseURL}${url}`);
};

export const exportReasonsForRefusal = ({ productId, id }) => {
	const baseURL = environment.apiSuperCessao;
	const url = `/api/v1/product/${productId}/received-invoices-batch/${id}/export-failed-entries?`;
	downloadFileHandler(`${baseURL}${url}`);
};

export const getDenyReasons = ({ productId, filters, id }) => {
	const url = `/api/v1/product/${productId}/received-invoices-batch/${id}/not-approved-invoices?${filters}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const getReceivablesApproved = ({ productId, filters, id }) => {
	const url = `/api/v1/product/${productId}/received-invoices-batch/${id}/approved-invoices?${filters}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const getDraweeFromOriginator = ({ productId, filters, document }) => {
	const url = `/api/v1/product/${productId}/originators/${document}?${filters}`;
	return client.get(`${url}`, {
		timeout: EXTRATIMEOUT,
	});
};

export const removeReceivablesBank = ({ productId, payload }) => {
	const url = `/api/v1/product/${productId}/received-invoices`;
	return client.delete(`${url}`, {
		timeout: EXTRATIMEOUT,
		data: {
			invoiceId: payload,
		},
	});
};
