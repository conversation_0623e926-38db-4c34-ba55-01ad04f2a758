export {
	getReceivablesBank,
	uploadReceivedXML,
	uploadReceivedExcel,
	getOriginatorsReceivablesBank,
	downloadExampleSheet,
	getStatusHistoric,
	getTypesHistoric,
	getHistoricBatch,
	downloadHistoricFile,
	getHistoricBatchById,
	exportApprovedReceivables,
	exportReasonsForRefusal,
	getDenyReasons,
	getReceivablesApproved,
	getDraweeFromOriginator,
	removeReceivablesBank,
	downloadFileByRange,
} from './services';
