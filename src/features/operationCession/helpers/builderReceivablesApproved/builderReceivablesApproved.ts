import { hasPropsObject } from '@/helpers/hasPropsObject';
export function builderReceivablesApproved(response) {
	const { content, pageable } = response.data;
	const newData = content.map((item, index) => {
		let numberReceivable = item.number;
		const isPartioned = hasPropsObject(item, "partioned");
		if(isPartioned && item.partioned){
			numberReceivable = `${item.number} (${item.part}/${item.totalParts})`;
		}
		return {
			key: `receivables-approved-${item.id}-${index}`,
			id: item.id,
			number: numberReceivable,
			draweeName: item.draweeName,
			draweeDocument: item.draweeDocument,
			expirationDate: item.expirationDate,
			netValue: item.liquidValue,
			invoiceDanfe: item.invoiceDanfe,
			freeValue: item.freeValue,
			nominalValue: item.nominalValue,
			providerName: item.providerName,
			providerDocument: item.providerDocument,
			emissionDate: item.invoiceEmissionDate,
		};
	});

	return {
		content: newData,
		pagination: {
			...pageable,
		},
	};
}
