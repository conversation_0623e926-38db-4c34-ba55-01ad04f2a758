export function builderInformationCession(response) {
	const { content } = response.data;
	if (content.length === 0) {
		return null;
	}
	return {
		hasDpp: content[0].hasDpp,
		id: content[0].id,
		limit: content[0].valueAvailable,
		status: content[0].sectionStatus,
		operationId: content[0].sectionType,
		typeOperation: content[0].sectionTypeName,
		productName: content[0].commercialProdName,
		dateDisbursement: content[0].dateDisbursement,
		valueDisbursement: content[0].valueDisbursement,
		nominalValue: content[0].valueNominal,
		freeValue: content[0].totalFreeValue,
		total: content[0].receivables,
		approved: content[0].approved,
		refused: content[0].refused,
		dateDisbursementIsValid: content[0].dateDisbursementIsValid,
		deletedBy: content[0].deletedBy,
		deleteAt: content[0].deletedAt,
		financialVehicle: content[0].financialVehicle,
		minimumCessionValue: content[0].minValue || 0,
		maxCessionValue: content[0].maxValue,
		failOnCreation: content[0].failOnCreation || false,
		commercialProductId: content[0].commercialProductId,
		valueAvailable: content[0].valueAvailable,
		hasOriginatorWithoutHeadOffice: content[0].hasOriginatorWithoutHeadOffice,
		hasNoOriginator: content[0].hasNoOriginator,
	};
}

export type BuilderInformationCession = ReturnType<typeof builderInformationCession>;
