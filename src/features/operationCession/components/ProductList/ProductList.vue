<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="4" v-for="item in data" :key="item.id">
				<product-card :item="item" />
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

import ProductCard from '../ProductCard';

export default defineComponent({
	name: 'product-list',
	components: {
		ProductCard,
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props) {
		const isDataEmpty = computed(() => props.data.length === 0);
		return {
			isDataEmpty,
		};
	},
});
</script>
