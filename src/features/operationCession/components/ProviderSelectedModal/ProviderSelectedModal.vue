<template>
	<farm-modal
		v-model="value"
		:offsetBottom="64"
		:offsetTop="48"
		:persistent="true"
		@onClose="onClose"
	>
		<template v-slot:header>
			<farm-dialog-header title="Selecionar Fornecedor" @onClose="onClose" />
		</template>

		<template v-slot:content>
			<farm-row align="end" justify="space-between" v-if="!isError">
				<farm-col cols="6">
					<farm-form-mainfilter
						:hasExtraFilters="false"
						label="Buscar Recebível"
						@onInputChange="onInputChangeMainFilter"
					>
						<farm-row no-default-gutters>
							<farm-label>Buscar Fornecedor</farm-label>
						</farm-row>
					</farm-form-mainfilter>
				</farm-col>
				<farm-col cols="2">
					<farm-select
						v-model="currentSortBy"
						item-text="text"
						item-value="value"
						:items="sortByOptions"
						@change="onSortSelect"
					/>
				</farm-col>
			</farm-row>
			<farm-row align="center" justify="space-between" no-default-gutters v-if="false">
				<counted pluralText="selecionados" singularText="selecionado" :value="10" />
				<farm-btn outlined> Desmarcar Selecionados</farm-btn>
			</farm-row>
			<provider-selected-list
				v-if="!isError"
				:data="providers"
				:providerListId="providerListId"
				@onUpdateProviderClicked="onUpdateProviderClicked"
			/>
			<farm-row class="mt-3" justify="space-between" v-if="!isError && isEmpty">
				<farm-col cols="12">
					<farm-datatable-paginator
						:hasGutter="false"
						:page="page"
						:totalPages="pagination.totalPages"
						@onChangePage="onChangePage"
						@onChangeLimitPerPage="onChangePageLimit"
					/>
				</farm-col>
			</farm-row>
			<farm-loader mode="overlay" v-if="isLoading" />

			<div v-if="isError" class="my-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				close-label="Cancelar"
				confirmLabel="Continuar"
				:isConfirmDisabled="isDisabledButton"
				@onClose="onClose"
				@onConfirm="onConfirm"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, toRefs } from 'vue';

import Counted from '@/components/Counted';
import { useRoute } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';
import { usePageable } from '@/composible/usePageable';

import ProviderSelectedList from '../ProviderSelectedList';

import { useProvider } from './composables/useProvider';
import { useProviderJoin } from './composables/useProviderJoin';

export default defineComponent({
	name: 'provider-selected-modal',
	components: {
		Counted,
		ProviderSelectedList,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		financialVehicleId: {
			type: Number,
			required: true,
		},
		providerListId: {
			type: Array,
			required: true,
		},
	},
	setup(props, { emit }) {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const {
			providers,
			providerPagination,
			isLoadingProviders,
			isErrorProviders,
			getProviders,
		} = useProvider();

		const { isLoadingProviderJoin, saveProviders } = useProviderJoin();

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onInputChangeMainFilter,
			onSortSelect,
		} = usePageable(
			{
				calbackFn: params => {
					getProviders({
						id: operationId,
						params: {
							...params,
							financialVehicleId: financialVehicleId.value,
						},
					});
				},
				keyInputSearch: 'search',
				lowercaseSort: true,
				filters: {},
				sort: {
					order: 'ASC',
					orderby: 'name',
				},
			},
			providerPagination
		);

		const { financialVehicleId, providerListId } = toRefs(props);
		const operationId = route.params.id;

		const sortByOptions = ref([
			{ text: 'Alfabético A-Z', value: 'name_ASC' },
			{ text: 'Alfabético Z-A', value: 'name_DESC' },
		]);
		const currentSortBy = ref('name_ASC');
		const providerClicked = ref(null);
		const isDisabledButton = ref(true);
		const currentPage = ref(0);
		const totalPage = ref(0);

		const isLoading = computed(() => isLoadingProviders.value || isLoadingProviderJoin.value);

		const isError = computed(() => isErrorProviders.value);

		const isEmpty = computed(() => {
			return providers.value.length > 0;
		});

		function load(): void {
			const params = {
				page: 0,
				limit: 10,
				order: 'ASC',
				orderby: 'name',
				financialVehicleId: financialVehicleId.value,
			};
			getProviders({
				id: operationId,
				params,
			});
		}

		function onClose(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 4,
					description: `clicou no botão cancelar modal de fornecedores`,
				},
			};
			trigger(dataTrigger);
			emit('onClose');
		}

		function callbackOnConfirm(): void {
			emit('onLoad');
			onClose();
		}

		function onConfirm(): void {
			const payload = [...providerClicked.value];
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 4,
					description: `clicou no botão continuar modal de fornecedores`,
					data: {
						provider: providerClicked.value,
					},
				},
			};
			trigger(dataTrigger);
			saveProviders(
				{
					id: operationId,
					payload,
				},
				callbackOnConfirm
			);
		}

		function onReload(): void {
			load();
		}

		function onUpdateProviderClicked(listOfSelected: Array<Number>): void {
			providerClicked.value = listOfSelected;
			isDisabledButton.value = listOfSelected.length > 0 ? false : true;
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isEmpty,
			isDisabledButton,
			page,
			pagination,
			currentPage,
			totalPage,
			providers,
			providerListId,
			sortByOptions,
			currentSortBy,
			onConfirm,
			onClose,
			onReload,
			onUpdateProviderClicked,
			onChangePage,
			onChangePageLimit,
			onInputChangeMainFilter,
			onSortSelect,
		};
	},
});
</script>
