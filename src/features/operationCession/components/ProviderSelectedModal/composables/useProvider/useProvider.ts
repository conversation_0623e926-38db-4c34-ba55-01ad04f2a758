import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { getProviderInOperation as getProviderInOperationService } from '@/features/operationCession/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseProvider = {
	providerPagination: any;
	providers: {
		value: Array<any>;
	};
	isLoadingProviders: {
		value: boolean;
	};
	isErrorProviders: {
		value: boolean;
	};
	getProviders: Function;
};

export function useProvider(): UseProvider {
	const providers = ref([]);
	const providerPagination = ref(null);
	const notFoundProviders = ref(null);
	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({ id, params }) => {
			const dataParams = queryString(params, {});
			return getProviderInOperationService({
				id,
				params: dataParams,
			});
		},
		onSuccess: response => {
			providers.value = response.data.content;
			providerPagination.value = {
				pageNumber: response.data.page,
				pageSize: response.data.size,
				totalElements: response.data.totalItems,
				totalPages: response.data.totalPages,
			};
			notFoundProviders.value = false;
		},
		onError: e => {
			if (isHttpRequestError(e, 404)) {
				notFoundProviders.value = true;
				providers.value = [];
			}
		},
	});

	const isLoadingProviders = computed(() => {
		return isLoading.value;
	});

	const isErrorProviders = computed(() => {
		return isError.value && !notFoundProviders.value;
	});

	function getProviders({ id, params }): void {
		mutate({ id, params });
	}

	return {
		isLoadingProviders,
		isErrorProviders,
		providers,
		providerPagination,
		getProviders,
	};
}
