
import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	createProviderInOperation as createProviderInOperationService
} from '@/features/operationCession/services';


type UseProviderJoin = {
	isLoadingProviderJoin: {
		value: boolean
	};
	saveProviders: Function;
}

type saveProvidersParams = {
	id: number;
	payload: Array<number>;
}

export function useProviderJoin(): UseProviderJoin {
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id, payload }) => {
			return createProviderInOperationService({
				id,
				productId,
				payload
			});
		},
		onSuccess: () => {
			createNotification();
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
		onError: () => {
			createNotificationError();
		}
	});

	const isLoadingProviderJoin = computed(() => {
		return isLoading.value;
	});

	function createNotification(): void {
		notification(
			RequestStatusEnum.SUCCESS,
			`Fornecedor atualizado para pagamento!`
		);
	}

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			`Erro ao atualizar o fornecedor para pagamento!`
		);
	}

	function saveProviders({ id, payload }: saveProvidersParams, callback: Function): void {
		mutate({ id, payload });
		callFunc = callback;
	}

	return {
		isLoadingProviderJoin,
		saveProviders
	};
}

