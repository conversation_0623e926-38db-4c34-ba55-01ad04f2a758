export type Configuration = {
	label: string;
	colors: {
		color: string;
		variation: string;
	};
	outlined: boolean;
};

const draft: Configuration = {
	label: 'Ras<PERSON><PERSON><PERSON>',
	colors: {
		color: 'warning',
		variation: 'lighten',
	},
	outlined: false,
};

const creation: Configuration = {
	label: 'Em Criação',
	colors: {
		color: 'warning',
		variation: 'base',
	},
	outlined: true,
};

const analysis: Configuration = {
	label: 'Em Análise',
	colors: {
		color: 'info',
		variation: 'base',
	},
	outlined: false,
};

const formalization: Configuration = {
	label: 'Formalização',
	colors: {
		color: 'warning',
		variation: 'base',
	},
	outlined: false,
};

const disbursement: Configuration = {
	label: 'Em Desembolso',
	colors: {
		color: 'info',
		variation: 'lighten',
	},
	outlined: false,
};

const complete: Configuration = {
	label: 'Concluída',
	colors: {
		color: 'primary',
		variation: 'base',
	},
	outlined: false,
};

const overdue: Configuration = {
	label: 'Recusada',
	colors: {
		color: 'error',
		variation: 'base',
	},
	outlined: false,
};

const cancel: Configuration = {
	label: 'Cancelada',
	colors: {
		color: 'error',
		variation: 'lighten',
	},
	outlined: false,
};

const simulation: Configuration = {
	label: 'Em Simulação',
	colors: {
		color: 'info',
		variation: 'darken',
	},
	outlined: true,
};

export type ConfigurationStatus = {
	[1]: Configuration;
	[2]: Configuration;
	[3]: Configuration;
	[4]: Configuration;
	[5]: Configuration;
	[6]: Configuration;
	[7]: Configuration;
	[8]: Configuration;
	[9]: Configuration;
};

export const configurationStatus: ConfigurationStatus = {
	1: draft,
	2: creation,
	3: analysis,
	4: formalization,
	5: disbursement,
	6: complete,
	7: overdue,
	8: cancel,
	9: simulation,
};
