<template>
	<farm-chip :color="color"  :dense="dense" :variation="variation" :outlined="outlined" max-si class="status-chip">
		{{ label || 'N/A' }}
	</farm-chip>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import CessionCard from '../CessionCard';

import { configurationStatus, Configuration } from './configurations';

export default defineComponent({
	name:"cession-status",
	components:{
		CessionCard
	},
	props: {
		status: {
			type: [String, Number],
			require: true,
		},
		dense: {
			type: Boolean,
			default: false,
		},
	},
	setup(props) {
		const configurations: Configuration = configurationStatus[props.status];
		const dense = ref(props.dense);
		const color = ref(configurations.colors.color);
		const variation = ref(configurations.colors.variation);
		const label = ref(configurations.label);
		const outlined = ref(configurations.outlined);
		return {
			dense,
			color,
			variation,
			label,
			outlined
		};
	},
});

</script>

<style lang="scss" scoped>
@import './CessionStatus';
</style>
