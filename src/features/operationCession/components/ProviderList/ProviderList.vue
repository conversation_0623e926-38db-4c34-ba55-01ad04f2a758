<template>
	<div class="my-4">
		<farm-box v-if="isDataEmpty">
			<div class="d-flex justify-center mb-6">
				<img src="@/assets/img-cessao.svg" alt="imagem referente a formalização" />
			</div>
			<div class="d-flex justify-center">
				<farm-btn
					:disabled="isDisabledCard"
					@click.stop="onAddProvider">
						Adicionar Fornecedor(es) para Pagamento
				</farm-btn>
			</div>
		</farm-box>
		<farm-box v-if="!isDataEmpty">
			<farm-row class="my-6">
				<farm-col cols="12">
					<farm-typography bold>Fornecedor(es) Selecionado(s)</farm-typography>
				</farm-col>
			</farm-row>
			<farm-row class="mb-3" justify="space-between" align="center">
				<farm-col cols="12" md="8">
					<div class="d-flex justify-start align-center flex-wrap">
						<div class="mr-2">
							<farm-typography size="12px" tag="p" weight="400"
								>Valor disponível para pagamento:
								<farm-typography size="12px" tag="span" weight="700" color="primary"
									>{{ formattedTotalAvailable }}
								</farm-typography>
							</farm-typography>
						</div>
						<farm-tooltip>
							<farm-caption variation="regular" color="white">
								Os centavos excedentes ficarão alocados ao
								primeiro fornecedor da lista.
							</farm-caption>
							<template v-slot:activator>
							<farm-btn outlined @click="distributionAmount">
								<farm-icon size="md">chart-pie-outline</farm-icon>
									Distribuição automática
								</farm-btn>
							</template>
						</farm-tooltip>
					</div>
				</farm-col>
				<farm-col cols="12" md="4">
					<div class="d-flex justify-end">
						<farm-btn plain @click.stop="onEditProvider">
							<farm-icon>account-plus-outline</farm-icon> Adicionar ou Alterar
							Fornecedores
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
		</farm-box>
		<farm-form v-model="isValidForm">
			<farm-row y-grid-gutters>
				<farm-col v-for="(item, index) in data" cols="6" :key="item.id">
					<ProviderCard
						v-model="inputValues[index]"
						:item="item"
						:isDisabled="isDisabled"
						:totalAvailable="totalAvailable"
						:valueDisbursement="valueDisbursement"
						@input="updateInputValue(index, $event)"
						@onAddDataBank="onAddDataBank(item)"
						@onRemoveProvider="onRemoveProvider(item)"
					/>
				</farm-col>
			</farm-row>
		</farm-form>
		<farm-row v-if="false" class="my-4">
			<farm-col>
				<farm-caption variation="semiBold" :color="false ? 'error' : 'inherit'">
					Valor distribuído para pagamento: {{ formatMoney(0) }}
				</farm-caption>
			</farm-col>
		</farm-row>
		<farm-row v-if="!isDataEmpty">
			<farm-col cols="12">
				<information-important
					@openModalInformation="$emit('openModalInformationImportant')"
				/>
			</farm-col>
		</farm-row>
	</div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted, ref, watch } from 'vue';

import { brl as formatMoney } from '@farm-investimentos/front-mfe-libs-ts';

import { useRoute } from '@/composible';

import InformationImportant from '../InformationImportant';
import ProviderCard from '../ProviderCard';
import { useProviderCard } from '../ProviderCard/composibles/useProviderCard';

export default defineComponent({
	name: 'provider-list',
	components: {
		ProviderCard,
		InformationImportant,
	},
	props: {
		data: {
			type: Array,
			default: () => [],
		},
		isDisabled: {
			type: Boolean,
			default: false,
		},
		isCancel: {
			type: Boolean,
			default: false,
		},
		valueDisbursement: {
			type: Number,
			required: true,
		},
	},
	setup(props, { emit }) {
		const route = useRoute();
		const { updatedAmountToPay } = useProviderCard();

		const inputValues = ref([]);
		const isValidForm = ref(false);
		const totalAvailable = ref(props.valueDisbursement);
		const operationId = route.params.id;
		const isDataEmpty = computed(() => props.data.length === 0);
		const isDisabledCard = computed(() => {
			if (props.isCancel) {
				return true;
			}

			if (!isDataEmpty.value) {
				return true;
			}

			return false;
		});

		const formattedTotalAvailable = computed(() =>
			totalAvailable.value <= 0 ? 'R$0,00' : formatMoney(totalAvailable.value)
		);

		function onAddProvider(): void {
			if (!isDisabledCard.value) {
				emit('onAddProvider');
			}
		}

		function onEditProvider(): void {
			emit('onAddProvider');
		}

		function onRemoveProvider(item): void {
			emit('onRemoveProvider', item);
		}

		function onAddDataBank(item): void {
			emit('onAddDataBank', item);
		}

		function equalInstallments(){
			const providerData = props.data;
			return Math.floor((props.valueDisbursement / providerData.length) * 100) / 100;
		}

		function calcFirstInstallment(valueInstallments, restOfDivision){
			if(restOfDivision === 0){
				return valueInstallments;
			}
			const value = (valueInstallments+restOfDivision).toFixed(2);
			return parseFloat(value);
		}

		function hasRestInstallments(valueInstallments) {
			const providerData = props.data;
			const totalInstallmentDivision = valueInstallments * providerData.length;
			const totalValueDisbursement = parseFloat(props.valueDisbursement);
			const restOfDivision = (totalValueDisbursement - totalInstallmentDivision);
			const hasDivision = restOfDivision > 0 ? true : false;
			const firstInstallment = calcFirstInstallment(valueInstallments, restOfDivision);
			return {
				hasDivision,
				firstInstallment
			};
		}

		function distributionAmount() {
			const providerData = props.data;
			const amountToPay = equalInstallments();
			const {
				firstInstallment,
				hasDivision
			} = hasRestInstallments(amountToPay);

			for (let index = 0; index < providerData.length; index++) {
				const valueInstallment = (hasDivision && index === 0) ? firstInstallment : amountToPay;
				const payload = {
					id: operationId,
					idProvider: providerData[index].id,
					amountToPay: valueInstallment,
				};
				setInputValue(index, formatMoney(valueInstallment));
				updatedAmountToPay(payload);
			}
		}

		function setInputValue(currentIndex, currentvalue) {
			const newValues = inputValues.value.map((value, index) =>
				currentIndex === index ? currentvalue : value
			);
			inputValues.value = newValues;
		}

		function updateInputValue(index, value) {
			inputValues.value[index] = value;
		}

		function onCreateInputValuesPositions(callback) {
			let newValues = [];
			const newData = props.data;
			newValues = new Array(newData.length).fill('R$0,00');

			return callback(newData, newValues);
		}
		function convertBRLtoDecimal(currentValue) {
			currentValue = currentValue.replace('R$', '').trim();
			if (currentValue === '' || currentValue === '0') {
				return 0.0;
			}
			currentValue = currentValue.replace(/\./g, '');
			currentValue = currentValue.replace(',', '.');
			let currentNumber = parseFloat(currentValue);
			if (isNaN(currentNumber)) {
				return 0.0;
			}

			return currentNumber;
		}

		function addValues(arrValues, formatValue) {
			let total = 0;

			arrValues.forEach(valueStr => {
				total += formatValue(valueStr);
			});

			return total;
		}

		watch(
			inputValues,
			newValue => {
				const newTotal = props.valueDisbursement - addValues(newValue, convertBRLtoDecimal);
				totalAvailable.value = convertBRLtoDecimal(formatMoney(newTotal));
				emit('onChangeinputValues', newValue);
			},
			{
				deep: true,
			}
		);

		watch(
			isValidForm,
			newValue => {
				emit('onChangeIsValidForm', newValue);
			},
			{
				deep: true,
			}
		);

		watch(
			totalAvailable,
			newValue => {
				emit('onChangeTotalAvailable', newValue === 0);
			},
			{
				deep: true,
			}
		);

		onMounted(() => {
			onCreateInputValuesPositions(function (newData, newValues) {
				if (Array.isArray(newData) && newData.length > 0) {
					for (let index = 0; index < newData.length; index++) {
						if (newData.length === 1) {
							newValues[index] = formatMoney(props.valueDisbursement) || 'R$0,00';
							updatedAmountToPay({
								id: operationId,
								idProvider: newData[index].id,
								amountToPay: props.valueDisbursement,
							});
						} else {
							newValues[index] = formatMoney(newData[index].value) || 'R$0,00';
						}
					}
					inputValues.value = newValues;
				}
			});
		});

		return {
			isValidForm,
			totalAvailable,
			formattedTotalAvailable,
			inputValues,
			isDisabledCard,
			isDataEmpty,
			formatMoney,
			onAddProvider,
			onRemoveProvider,
			onAddDataBank,
			onEditProvider,
			updateInputValue,
			setInputValue,
			distributionAmount,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './ProviderList.scss';
</style>
