<template>
	<farm-box>
		<farm-row extra-decrease>
			<farm-line />
		</farm-row>
		<farm-row class="mt-6">
			<farm-col cols="12" md="6">
				<farm-btn
					v-if="!hiddenBack"
					title="Voltar"
					class="farm-btn--responsive mb-2 mb-sm-0 ml-0 ml-sm-6"
					plain
					icon
					@click="onClickBack"
				>
					<farm-icon color="primary" size="md">chevron-left</farm-icon>
					Voltar
				</farm-btn>
			</farm-col>
			<farm-col cols="12" md="6" align="right">
				<farm-btn
					v-if="!hiddenClose"
					title="Fechar"
					class="farm-btn--responsive"
					outlined
					@click="onClickClose"
				>
					{{labelCancel}}
				</farm-btn>
				<farm-btn
					v-if="!hiddenConfirm"
					class="farm-btn--responsive ml-0 ml-2 mt-3 mt-sm-0"
					:title="labelConfirm"
					:disabled="isDisabledButtonConfirm"
					@click="onClickConfirm"
				>
					{{ labelConfirm }}
				</farm-btn>
			</farm-col>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
	name:"footer-cession",
	props: {
		hiddenConfirm: {
			type: Boolean,
			default: false,
		},
		hiddenClose: {
			type: Boolean,
			default: false,
		},
		hiddenBack: {
			type: Boolean,
			default: false,
		},
		isDisabledButtonConfirm: {
			type: Boolean,
			default: false,
		},
		labelConfirm: {
			type: String,
			default: 'Continuar',
		},
		labelCancel: {
			type: String,
			default: 'Fechar',
		},
	},
	setup(props, { emit }) {

		const {
			hiddenConfirm,
			hiddenClose,
			hiddenBack,
			isDisabledButtonConfirm,
			labelConfirm,
			labelCancel
		} = toRefs(props);

		function onClickConfirm(): void {
			emit('onClickConfirm');
		}

		function onClickClose(): void {
			emit('onClickClose');
		}

		function onClickBack(): void {
			emit('onClickBack');
		}

		return {
			hiddenConfirm,
			hiddenClose,
			hiddenBack,
			isDisabledButtonConfirm,
			labelConfirm,
			labelCancel,
			onClickConfirm,
			onClickClose,
			onClickBack
		};
	},
});
</script>
