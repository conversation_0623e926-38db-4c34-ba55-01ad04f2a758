<template>
	<farm-container>
		<header-cession title="Escolha qual produto deseja utilizar para sua cessão" class="mb-4" />
		<product-list v-if="!isError" :data="commercialProduct" />
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-cession :hiddenConfirm="true" :hiddenBack="true" @onClickClose="onClickClose" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';
import useRouter from '@/composible/useRouter';

import Footer from '../Footer';
import Header from '../Header';
import ProductList from '../ProductList';

import { useCommercialProduct } from './composables/useCommercialProduct';

export default defineComponent({
	name: 'body-step-one-product',
	components: {
		'header-cession': Header,
		'footer-cession': Footer,
		ProductList,
	},
	setup() {
		const router = useRouter();
		const { trigger } = useAnalytics();
		const {
			commercialProduct,
			getCommercialProduct,
			isErrorCommercialProduct,
			isLoadingCommercialProduct,
		} = useCommercialProduct();

		function onClickClose(): void {
			const dataTrigger = {
				step: 1,
				event: 'operation_cession',
				payload: {
					description: 'clicou no botão fechar',
				},
			};
			trigger(dataTrigger);
			router.push(`/admin/wallet/cessoes`);
		}

		function load(): void {
			getCommercialProduct();
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			commercialProduct,
			isError: isErrorCommercialProduct,
			isLoading: isLoadingCommercialProduct,
			onReload,
			onClickClose,
		};
	},
});
</script>
