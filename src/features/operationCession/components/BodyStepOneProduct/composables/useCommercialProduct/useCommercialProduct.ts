import { ref, computed } from 'vue';

import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderCommercialProduct } from '@/features/operationCession/helpers/builderCommercialProduct';
import { getCommercialProduct as getCommercialProductService } from '@/features/operationCession/services';

type UseCommercialProduct = {
	commercialProduct: Array<any>;
	isLoadingCommercialProduct: boolean;
	isErrorCommercialProduct: boolean;
	getCommercialProduct: Function;
};

export function useCommercialProduct(): UseCommercialProduct {
	const commercialProduct = ref([]);

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getCommercialProductService(params),
		onSuccess: (response) => {
			commercialProduct.value = builderCommercialProduct(response.data);
		},
	});

	const isLoadingCommercialProduct = computed(() => {
		return isLoading.value;
	});

	const isErrorCommercialProduct = computed(() => {
		return isError.value;
	});

	function getCommercialProduct(): void {
		mutate({ productId });
	}

	return {
		commercialProduct,
		isLoadingCommercialProduct,
		isErrorCommercialProduct,
		getCommercialProduct,
	};
}
