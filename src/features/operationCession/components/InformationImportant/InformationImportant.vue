<template>
	<farm-box class="mt-6">
		<div class="d-flex align-center">
			<farm-checkbox class="mr-2" size="sm" v-model="isChecked" :value="true" />

			<farm-bodytext :type="2" variation="regular" size="14px">
				Li e estou de acordo com os
				<farm-typography
					class="button--underline"
					size="14px"
					tag="span"
					weight="400"
					color="primary"
					underline
					@click="openModalInformation"
				>
					&nbsp;termos e informações
				</farm-typography>
				&nbsp;para realizar a cessão de recebíveis.
			</farm-bodytext>
		</div>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { computed } from 'vue';

import { useGetter, useStore } from '@/composible';
export default defineComponent({
	name: 'information-important',
	setup(_, { emit }) {
		const checkboxModel = computed(useGetter('operationCession', 'iAgree'));

		const isChecked = ref(checkboxModel.value);
		const store = useStore();

		function openModalInformation() {
			emit('openModalInformation');
		}

		watch(isChecked, newValue => {
			store.dispatch('operationCession/setIAgree', {
				data: newValue || false,
			});
		});
		return {
			isChecked,
			openModalInformation,
		};
	},
});
</script>
<style lang="scss">
@import './InformationImportant.scss';
</style>
