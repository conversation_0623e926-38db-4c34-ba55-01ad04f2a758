<template>
	<farm-modal :offsetBottom="64" :offsetTop="48" :persistent="true" :value="value">
		<template v-slot:header>
			<farm-dialog-header title="Selecionar Dado Bancário" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-row class="my-4">
				<farm-col cols="12">
					<farm-heading :type="6">{{ providerCurrent.name }}</farm-heading>
				</farm-col>
				<farm-col cols="12">
					<farm-caption :variation="'regular'" color="black" color-variation="40">
						CNPJ: {{ providerCurrent.document }}
					</farm-caption>
				</farm-col>
			</farm-row>
			<data-bank-list
				v-if="!isError"
				:data="dataBank"
				@onUpdateDataBankClicked="onUpdateDataBankClicked"
			/>
			<farm-row class="mb-4" v-if="!isError"></farm-row>
			<farm-loader mode="overlay" v-if="isLoading" />
			<div v-if="isError" class="my-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				close-label="Cancelar"
				confirmLabel="Continuar"
				:isConfirmDisabled="isDisabledButton"
				@onConfirm="onConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, toRefs, onMounted, ref, computed } from 'vue';

import { useRoute } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';

import DataBankList from '../DataBankList';

import { useDataBank } from './composables/useDataBank';
import { useDataBankJoin } from './composables/useDataBankJoin';
import { usePaymanentProvider } from './composables/usePaymanentProvider';

export default defineComponent({
	name: 'data-bank-modal',
	components: {
		DataBankList,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		providerCurrent: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const { dataBank, getDataBank, isLoadingDataBank } = useDataBank();

		const { isLoadingDataBankJoin, saveDataBank } = useDataBankJoin();
		const { isLoadingPayment } = usePaymanentProvider();

		const { providerCurrent } = toRefs(props);
		const dataBankClicked = ref(null);
		const operationId = route.params.id;

		const isLoading = computed(() => {
			if (isLoadingDataBankJoin.value || isLoadingDataBank.value || isLoadingPayment.value) {
				return true;
			}
			return false;
		});
		const isError = computed(() => {
			return false;
		});
		const isDisabledButton = computed(() => {
			if (isError.value) {
				return true;
			}
			if (dataBank && dataBank.value.length === 0) {
				return true;
			}
			if (dataBankClicked.value === null) {
				return true;
			}
			return false;
		});

		function onClose(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 4,
					description: `clicou no botão cancelar modal de dados bancario`,
				},
			};
			trigger(dataTrigger);
			emit('onClose');
		}

		function saveDataBankCallback(): void {
			onClose();
			emit('onLoad');
		}

		function onConfirm(): void {
			const payload = {
				idDataBank: dataBankClicked.value,
				idProvider: providerCurrent.value.id,
			};
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 4,
					description: `clicou no botão continuar modal de dados bancario`,
					data: {
						...payload,
					},
				},
			};
			trigger(dataTrigger);
			saveDataBank(
				{
					id: operationId,
					payload,
				},
				saveDataBankCallback
			);
		}

		function onUpdateDataBankClicked(data): void {
			dataBankClicked.value = data;
		}

		function load(): void {
			getDataBank(operationId, providerCurrent.value.document);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isDisabledButton,
			dataBank,
			providerCurrent,
			onClose,
			onConfirm,
			onUpdateDataBankClicked,
			onReload,
		};
	},
});
</script>
