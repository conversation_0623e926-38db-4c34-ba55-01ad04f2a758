import { computed, ref } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	updatedPayment as updatedPaymentService
} from '@/features/operationCession/services';


type UsePaymanentProvider = {
	isLoadingPayment: {
		value: boolean
	};
	updatedPayment: Function;
}

export function usePaymanentProvider(): UsePaymanentProvider {
	const productId = useSelectedProductId().value;

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id, idProvider, payload }) => {
			return updatedPaymentService({
				id,
				idProvider,
				productId,
				payload
			});
		},
		onSuccess: () => {

		},
	});

	const isLoadingPayment = computed(() => {
		return isLoading.value;
	});


	function updatedPayment({id, idProvider, payload }): void {
		mutate({ id, idProvider, payload });
	}

	return {
		isLoadingPayment,
		updatedPayment
	};
}
