<template>
	<farm-tooltip>
		<farm-caption variation="semiBold" color="white">
			{{ title }}
		</farm-caption>
		<farm-caption variation="regular" color="white">
			{{ text }}
		</farm-caption>
		<template v-slot:activator>
			<slot></slot>
		</template>
	</farm-tooltip>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { tooltip } from '../../configurations/tooltip';
import CessionCard from '../CessionCard';

export default defineComponent({
	name:"cession-tooltip",
	components:{
		CessionCard
	},
	props: {
		status: {
			type: [String, Number],
			require: true,
		},
	},
	setup(props) {
		const title = ref('');
		const text = ref('');
		const data = tooltip.filter((item) => item.id === parseInt(props.status, 10));
		if(data.length > 0) {
			title.value = data[0].title;
			text.value = data[0].text;
		}
		return {
			title,
			text
		};
	},
});
</script>

