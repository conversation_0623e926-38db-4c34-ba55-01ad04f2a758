<template>
	<cards>
		<template slot="header">
			<farm-row class="d-flex space-between align-center">
				<farm-col cols="12">
					<card-title-header :value="item.name" ellipsis class="mb-2" />
				</farm-col>
				<farm-col cols="7">
					<card-text-body label="CNPJ" :value="item.document" ellipsis />
				</farm-col>
				<farm-col cols="5">
					<card-text-body label="Valor a Receber" :value="formatMoney(item.value)" ellipsis />
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row v-if="!item.withBankDataAssociated">
				<farm-col cols="12">
					<div class="data-bank">
						<farm-btn plain @click="onOpenDataBank(item)">
							Selecione Dado Bancário <farm-icon>open-in-new</farm-icon>
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
			<farm-row v-if="item.withBankDataAssociated && item.type === 'account'">
				<farm-col cols="2">
					<card-text-body label="Tipo" :value="item.accountType" ellipsis />
				</farm-col>
				<farm-col cols="5">
					<div class="fild-data">
						<card-text-body label="Banco" :value="item.bank" ellipsis />
					</div>
				</farm-col>
				<farm-col cols="2">
					<card-text-body label="Agência" :value="item.agency" ellipsis />
				</farm-col>
				<farm-col cols="3">
					<card-text-body label="Número da Conta" :value="item.account" ellipsis />
				</farm-col>
				<farm-col cols="12">
					<div class="data-bank mt-4">
						<farm-btn plain @click="onOpenDataBank(item)">
							Alterar Dado Bancário <farm-icon>cached</farm-icon>
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
			<farm-row v-if="item.withBankDataAssociated && item.type !== 'account'">
				<farm-col cols="4">
					<card-text-body label="Tipo" value="Pix" ellipsis />
				</farm-col>
				<farm-col cols="4">
					<card-text-body label="Tipo de chave" :value="item.pixType" ellipsis />
				</farm-col>
				<farm-col cols="4">
					<card-text-body label="Chave" :value="item.key" ellipsis />
				</farm-col>
				<farm-col cols="12">
					<div class="data-bank mt-4">
						<farm-btn plain @click="onOpenDataBank(item)">
							Alterar Dado Bancário <farm-icon>cached</farm-icon>
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
		</template>
	</cards>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import CardTextBody from '@/components/CardTextBody';
import CardTitleHeader from '@/components/CardTitleHeader';
import Cards from '@/components/Cards';

export default defineComponent({
	name:"originator-card",
	components:{
		Cards,
		CardTitleHeader,
		CardTextBody
	},
	props: {
		item: {
			required: true,
			type: Object,
		}
	},
	setup(_,{ emit }) {

		function onOpenDataBank(data): void {
			emit('onOpenModalBank', data);
		}

		return {
			formatMoney: brl,
			onOpenDataBank
		};
	}
});
</script>

<style lang="scss" scoped>
@import './OriginatorCard.scss';
</style>
