<template>
	<farm-container>
		<farm-box>
			<farm-row justify="center">
				<farm-heading v-if="!isDone" :type="5">
					Estamos Criando sua Cessão!
				</farm-heading>
				<farm-heading v-else :type="5"> Cessão Criada com Sucesso!</farm-heading>
			</farm-row>
			<farm-row justify="center">
				<farm-bodytext v-if="!isDone" :type="2" :variation="'bold'">
					Em alguns instantes você será redirecionado.
				</farm-bodytext>
				<farm-bodytext v-else :type="2" :variation="'bold'">
					Fique atento aos próximos passos!
				</farm-bodytext>
			</farm-row>
			<farm-row justify="center">
				<animated-progress
					:jsonAnimation="animationData"
					:maxProgressStartFrame="maxProgressState"
					:progressValue="progress"
				/>
			</farm-row>
			<farm-row justify="center">
				<farm-heading v-if="!isDone" color="primary" type="6">
					{{ progress }}%
				</farm-heading>
				<farm-col v-else class="text-center" cols="6">
					<farm-bodytext :type="2" :variation="'medium'">
						Agora que a sua cessão foi criada,
						<farm-typography color="primary" tag="span">
							analisaremos seus recebíveis
						</farm-typography>
						após isso, sua cessão entrará em processo de formalização.
					</farm-bodytext>
				</farm-col>
			</farm-row>
			<farm-row justify="center">
				<farm-col v-if="!isDone" class="my-6" cols="6">
					<farm-progressbar
						:value="progress"
						backgroundColor="gray"
						valueColor="primary"
					/>
				</farm-col>
				<farm-col  v-if="isDone" class="text-center mt-6" cols="6">
					<farm-btn @click="onRedirectHome"> Voltar para a Tela Inicial</farm-btn>
				</farm-col>
			</farm-row>
		</farm-box>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, watch, ref, onMounted } from 'vue';

import animationData from '@/assets/check.json';
import AnimatedProgress from '@/components/AnimatedProgress';
import useRoute from '@/composible/useRoute';

import { useCreateCession } from './composables/useCreateCession';
import { useRedirect } from './composables/useRedirect';

export default defineComponent({
	name:"body-success",
	components: {
		AnimatedProgress,
	},
	setup() {
		const route = useRoute();
		const {
			createCession,
		} = useCreateCession();

		const {
			redirectHome,
			redirectErrorCreateCession
		} = useRedirect();

		const operationId = route.params.id;
		const progress = ref(0);
		const isDone = ref(false);
		const maxProgressState = 100;

		function start(limit: number): void {
			let callCreate = true;
			setInterval(() => {
				if (progress.value === 50 && callCreate === true) {
					progress.value = 51;
					callCreate = false;
					callCreateCession();
				}
				if (progress.value < limit) {
					progress.value += 1;
				}
			}, 100);
		}

		function onRedirectHome(): void {
			redirectHome();
		}

		function callCreateCession(): void {
			createCession(operationId, (error) => {
				if(error){
					progress.value = 0;
					redirectErrorCreateCession(operationId);
					return;
				}
				start(100);
			});
		}

		watch(progress,
			value => {
				if (value === maxProgressState) {
					isDone.value = true;
				}
			}
		);

		onMounted(() => {
			start(50);
		});

		return {
			progress,
			isDone,
			animationData,
			maxProgressState,
			onRedirectHome
		};
	},
});
</script>
