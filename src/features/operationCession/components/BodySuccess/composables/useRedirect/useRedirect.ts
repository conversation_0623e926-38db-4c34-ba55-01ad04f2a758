import useRouter from '@/composible/useRouter';

type UseRedirect ={
	redirectHome: Function;
	redirectErrorCreateCession: Function;
};

export function useRedirect(): UseRedirect {
	const router = useRouter();

	function redirectHome(): void {
		router.push(`/admin/wallet/cessoes`);
	}

	function redirectErrorCreateCession(id): void {
		router.push(`/admin/wallet/cessoes/erro_ao_criar_a_cessao/${id}`);
	}

	return {
		redirectHome,
		redirectErrorCreateCession
	};
}
