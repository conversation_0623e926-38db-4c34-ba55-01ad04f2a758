

import { computed } from 'vue';

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	createCession as createCessionService
} from '@/features/operationCession/services';

type UseCreateCession = {
	isLoadingCreate: {
		value: boolean
	};
	createCession: Function;
}

export function useCreateCession(): UseCreateCession {

	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id }) => {
			const payload = {
				idPresSection: id
			};
			return createCessionService({
				payload,
				productId
			});
		},
		onSuccess: () => {
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc(false);
				}, 2000);
			}
		},
		onError: () => {
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc(true);
				}, 2000);
			}
		}
	});

	const isLoadingCreate = computed(() => {
		return isLoading.value;
	});

	function createCession(id: number, callback: Function): void {
		mutate({ id });
		callFunc = callback;
	}

	return {
		isLoadingCreate,
		createCession
	};
}
