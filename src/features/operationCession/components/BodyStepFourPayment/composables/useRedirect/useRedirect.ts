import useRoute from '@/composible/useRoute';
import useRouter from '@/composible/useRouter';

type UseRedirect ={
	redirectHome: Function;
	redirectSummary: Function;
	redirectNextStep: Function;
};

export function useRedirect(): UseRedirect {
	const router = useRouter();
	const route = useRoute();

	const operationId = route.params.id;

	function redirectSummary(): void {
		router.push(`/admin/wallet/cessoes/resumo_da_cessao/${operationId}`);
	}

	function redirectHome(): void {
		router.push(`/admin/wallet/cessoes`);
	}

	function redirectNextStep(): void {
		router.push(`/admin/wallet/cessoes/criacao_da_cessao_sucesso/${operationId}`);
	}

	return {
		redirectHome,
		redirectSummary,
		redirectNextStep
	};
}
