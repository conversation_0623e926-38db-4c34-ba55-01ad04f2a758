import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import {
	builderProvider
} from '@/features/operationCession/helpers/builderProvider';
import {
	getProviderInOperation as getProviderInOperationService
} from '@/features/operationCession/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';


type UseProviderInOperation = {
	providers:Array<any>;
	isLoadingProvider: {
		value: boolean
	};
	isErrorProvider: {
		value: boolean
	};
	getProviderInOperation: Function;
}

export function useProviderInOperation(): UseProviderInOperation {
	let callFunc: Function | null = null;
	const providers = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({ id, params }) => {
			const dataParams = queryString(params, {});
			return getProviderInOperationService({
				id,
				params: dataParams
			});
		},
		onSuccess: (response) => {
			providers.value = builderProvider(response);
			if(callFunc) callFunc(providers.value, false);
		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				providers.value = [];
				if(callFunc) callFunc(providers.value, false);
				return;
			}
			if(callFunc) callFunc([], true);
		},
	});

	const isLoadingProvider = computed(() => {
		return isLoading.value;
	});

	const isErrorProvider = computed(() => {
		return isError.value;
	});

	function getProviderInOperation({ id, params }, callback: Function): void {
		mutate({ id, params });
		providers.value =[];
		callFunc = callback;
	}

	return {
		isLoadingProvider,
		isErrorProvider,
		providers,
		getProviderInOperation
	};
}
