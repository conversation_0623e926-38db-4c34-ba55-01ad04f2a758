import { computed, ref } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	removeProviderInOperation as removeProviderInOperationService
} from '@/features/operationCession/services';


type UseProvidersInOperationRemove = {
	isLoadingRemoveProvider: {
		value: boolean
	};
	removeProviderInOperation: Function;
}

export function useProvidersInOperationRemove(): UseProvidersInOperationRemove {
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;
	const nameProvider = ref('');

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id, idProvider }) => {
			return removeProviderInOperationService({
				id,
				idProvider,
				productId
			});
		},
		onSuccess: () => {
			createNotification();
			if(callFunc !== null){
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
		onError:()=>{
			createNotificationError();
		}
	});

	const isLoadingRemoveProvider = computed(() => {
		return isLoading.value;
	});

	function createNotification(): void {
		notification(
			RequestStatusEnum.SUCCESS,
			`Fornecedor <b>${nameProvider.value}</b> removido da cessão com sucesso!`
		);
	}

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			`Erro ao remover o fornecedor <b>${nameProvider.value}</b>`
		);
	}

	function removeProviderInOperation({id, idProvider, name }, callback: Function): void {
		mutate({ id, idProvider });
		callFunc = callback;
		nameProvider.value = name;
	}

	return {
		isLoadingRemoveProvider,
		removeProviderInOperation
	};
}
