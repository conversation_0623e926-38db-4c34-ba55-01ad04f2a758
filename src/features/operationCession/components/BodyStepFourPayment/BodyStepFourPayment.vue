<template>
	<farm-container>
		<header-cession :title="title" :currentStep="4" :errorCurrentStep="isCancel" />
		<farm-box class="mt-2" v-if="!isError">
			<farm-row v-if="isCancel && !isFailCession">
				<farm-col cols="12">
					<feedback-alert color="error">
						<farm-typography tag="p" color="error">
							Sua cessão foi cancelada pelo usuário <b> {{ dataCancel.name }} </b> no
							dia <b>{{ dataCancel.date }}</b
							>.
						</farm-typography>
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="isCancel && isFailCession">
				<farm-col cols="12">
					<feedback-alert color="error">
						Não foi possível validar seus recebíveis, sua cessão
						foi cancelada no dia <b>{{ dataCancel.date }} às {{ dataCancel.hour }}</b>.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isCancel && isDateInvalid">
				<farm-col cols="12">
					<feedback-alert>
						A data selecionada não é mais permitida. Selecione outra data clicando em
						<b>“Voltar”</b> ou descarte esse rascunho clicando em <b>“Cancelar”</b>.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isCancel && isMinValue">
				<farm-col cols="12">
					<feedback-alert color="error">
						O valor da cessão sofreu algumas <b>alterações</b> por conta da análise dos
						recebíveis. É necessário atingir no mínimo {{ labelMinValue }} em recebíveis
						para continuar. Tente alterar ou selecionar mais recebíveis no passo
						anterior.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row extra-decrease>
				<farm-line class="mb-4" />
			</farm-row>
			<farm-row>
				<farm-col cols="12">
					<information-cession-list
						:dateDisbursement="headerData.dateDisbursement"
						:limit="headerData.limit"
						:valueDisbursement="headerData.valueDisbursement"
						:productName="headerData.productName"
						:typeOperation="headerData.typeOperation"
						@onClickDisbursement="onOpenModalDetailsCession"
					/>
				</farm-col>
			</farm-row>
			<farm-row extra-decrease>
				<farm-line />
			</farm-row>
			<farm-row v-if="isCancel">
				<farm-col cols="12">
					<feedback-alert color="info">
						<farm-typography tag="p" color="info">
							Os recebíveis da cessão cancelada estão disponíveis no banco de
							recebíveis para serem usadas novamente.
						</farm-typography>
					</feedback-alert>
				</farm-col>
			</farm-row>
			<div v-if="!isLoading && isProvider && !isCancel">
				<farm-row>
					<farm-col cols="12">
						<feedback-alert color="warning" v-if="isValueInvalid">
							<farm-typography tag="p" color="warning">
								Você excedeu o <b>valor liquido</b> para realização da cessão,
								verifique novamente para prosseguir.
							</farm-typography>
						</feedback-alert>
					</farm-col>
				</farm-row>
				<provider-list
					:data="providerData"
					:valueDisbursement="informationCessions.valueDisbursement || 0"
					:isDisabled="false"
					:isCancel="isCancel"
					@onGetTotalValue="() => {}"
					@onAddProvider="onOpenModalProvider"
					@onAddDataBank="onAddDataBank"
					@onRemoveProvider="onRemoveProvider"
					@openModalInformationImportant="openModalInformationImportant"
					@onChangeinputValues="onChangeinputValues"
					@onChangeIsValidForm="onChangeIsValidForm"
					@onChangeTotalAvailable="onChangeTotalAvailable"
				/>
			</div>
			<div v-if="!isLoading && !isProvider && !isCancel">
				<farm-row>
					<farm-col cols="12">
						<farm-bodytext :type="2" bold class="mt-4 mb-4">
							Cedentes associados a cessão:
						</farm-bodytext>
					</farm-col>
				</farm-row>
				<originator-list
					:id="informationCessions.id"
					@openModalInformationImportant="openModalInformationImportant"
					@onValidButton="onValidButton"
				/>
			</div>
		</farm-box>
		<div v-if="isLoading" style="height: 320px" class="my-10 d-flex justify-center">
			<farm-loader mode="overlay" v-if="isLoading" />
		</div>
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-cession
			:isDisabledButtonConfirm="isDisabledButtonConfirm"
			:hiddenClose="isCancel"
			:hiddenBack="isCancel"
			:labelConfirm="isCancel ? 'Voltar' : 'Criar'"
			labelCancel="Cancelar"
			@onClickClose="onOpenModalCancel"
			@onClickBack="onClickBack"
			@onClickConfirm="onClickConfirmFooter"
		/>
		<data-bank-modal
			v-if="isModalDataBank"
			v-model="isModalDataBank"
			:providerCurrent="providerCurrent"
			@onClose="onCloseModalDataBankWithLoad"
			@onConfirm="onConfirmModaDataBank"
			@onLoad="load"
		/>
		<provider-selected-modal
			v-if="isModalProvider"
			v-model="isModalProvider"
			:financialVehicleId="financialVehicleId"
			:providerListId="providerListId"
			@onClose="onCloseModalProvider"
			@onOpenModalDataBank="onAddDataBank"
			@onLoad="load"
		/>
		<provider-remove-modal
			v-if="isModalProviderRemove"
			v-model="isModalProviderRemove"
			:providerCurrent="providerCurrent"
			@onClose="onCloseModalProviderRemove"
			@onConfirm="onConfirmModalProviderRemove"
		/>
		<modal-detail-cession
			v-model="isModalDetailCession"
			v-if="isModalDetailCession"
			:data="informationCessions"
			:receivables="true"
			@onClose="onCloseModalDetailCession"
			@onOpenModalReceivablesApproved="onOpenModalReceivablesApproved"
			@onOpenModalDenyReasons="onOpenModalDenyReasons"
		/>
		<modal-information-important
			v-model="isModalInformationImportant"
			v-if="isModalInformationImportant"
			@onClose="onCloseModalInformationImportant"
		/>
		<modal-cancel-cession
			v-model="isModalCancelCession"
			v-if="isModalCancelCession"
			@onClose="onCloseModalCancelCession"
			@onConfirm="onConfirmModalCancelCession"
		/>
		<modal-deny-reasons
			v-if="isModalDenyReasons"
			v-model="isModalDenyReasons"
			:cessionCurrent="cessionCurrent"
			@onCloseModal="onCloseModalDenyReasons"
		/>
		<modal-receivables-approved
			v-if="isModalReceivablesApproved"
			v-model="isModalReceivablesApproved"
			:cessionCurrent="cessionCurrent"
			@onCloseModal="onCloseModalReceivablesApproved"
		/>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';

import {
	brl,
	defaultDateFormat,
	notification,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';

import FeedbackAlert from '@/components/FeedbackAlert';
import InformationCessionList from '@/components/InformationCessionList';
import ModalCancelCession from '@/components/ModalCancelCession';
import ModalDetailCession from '@/components/ModalDetailCession';
import { useGetter } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';
import useRoute from '@/composible/useRoute';
import { useProductCache } from '@/features/operationCession/composables/useProductCache';

import { useCancelCession } from '../../composables/useCancelCession';
import { useInformationCession } from '../../composables/useInformationCession';
import { builderHoursCession } from '../../helpers/builderHoursCession';
import DataBankModal from '../DataBankModal';
import Footer from '../Footer';
import Header from '../Header';
import ModalDenyReasons from '../ModalDenyReasons';
import ModalInformationImportant from '../ModalInformationImportant';
import ModalReceivablesApproved from '../ModalReceivablesApproved';
import OriginatorList from '../OriginatorList';
import ProviderList from '../ProviderList';
import ProviderRemoveModal from '../ProviderRemoveModal';
import ProviderSelectedModal from '../ProviderSelectedModal';

import { useProviderInOperation } from './composables/useProviderInOperation';
import { useProvidersInOperationRemove } from './composables/useProvidersInOperationRemove';
import { useRedirect } from './composables/useRedirect';

export default defineComponent({
	name: 'body-step-four-payment',
	components: {
		'header-cession': Header,
		'footer-cession': Footer,
		InformationCessionList,
		ProviderList,
		FeedbackAlert,
		ModalDetailCession,
		ModalCancelCession,
		ProviderSelectedModal,
		ProviderRemoveModal,
		DataBankModal,
		ModalDenyReasons,
		ModalReceivablesApproved,
		OriginatorList,
		ModalInformationImportant,
	},
	setup() {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const {
			isOpenModal: isModalDetailCession,
			onCloseModal: onCloseModalDetailCession,
			onOpenModal: openModalDetailCession,
		} = useModal();
		const {
			isOpenModal: isModalCancelCession,
			onCloseModal: onCloseModalCancelCession,
			onOpenModal: openModalCancelCession,
		} = useModal();
		const {
			isOpenModal: isModalProvider,
			onCloseModal: onCloseModalProvider,
			onOpenModal: openModalProvider,
		} = useModal();
		const {
			isOpenModal: isModalProviderRemove,
			onCloseModal: onCloseModalProviderRemove,
			onOpenModal: openModalDProviderRemove,
		} = useModal();
		const {
			isOpenModal: isModalDataBank,
			onCloseModal: onCloseModalDataBank,
			onOpenModal: openModalDataBank,
		} = useModal();
		const {
			isOpenModal: isModalDenyReasons,
			onCloseModal: onCloseModalDenyReasons,
			onOpenModal: openModalDenyReasons,
		} = useModal();
		const {
			isOpenModal: isModalReceivablesApproved,
			onCloseModal: onCloseModalReceivablesApproved,
			onOpenModal: openModalReceivablesApproved,
		} = useModal();
		const {
			isOpenModal: isModalInformationImportant,
			onCloseModal: onCloseModalInformationImportant,
			onOpenModal: openModalInformationImportant,
		} = useModal();
		const { redirectHome, redirectNextStep, redirectSummary } = useRedirect();
		const {
			getInformationCession,
			isLoadingInformationCession,
			isErrorInformationCession,
			informationCessions,
		} = useInformationCession();
		const { cancelCession, isLoadingCancel } = useCancelCession();
		const { getProviderInOperation, isLoadingProvider } = useProviderInOperation();
		const { isLoadingRemoveProvider, removeProviderInOperation } =
			useProvidersInOperationRemove();
		const { addDataCache } = useProductCache();

		const operationId = route.params.id;

		const providerData = ref([]);
		const providerCurrent = ref(null);
		const isModalShowing = ref(false);
		const isCancel = ref(false);
		const isFailCession = ref(false);
		const dataCancel = ref({
			name: '-',
			date: '-',
		});
		const isDateInvalid = ref(false);
		const isValueInvalid = ref(false);
		const isValidForm = ref(false);
		const totalAmountUsed = ref(false);
		const listEmpty = ref(true);
		const listOriginatorEmpty = ref(true);
		const headerData = ref({
			dateDisbursement: '-',
			limit: '-',
			valueDisbursement: '-',
			productName: '-',
			typeOperation: '-',
		});
		const cessionCurrent = ref(null);
		const financialVehicleId = ref(null);
		const isProvider = ref(true);
		const isMinValue = ref(true);
		const labelMinValue = ref('R$ 0,00');
		const checkboxModel = computed(useGetter('operationCession', 'iAgree'));
		const valuesSupplierNotValid = ref(true);

		const title = computed(() => {
			if (isCancel.value) {
				return 'Cessão Cancelada';
			}
			if (informationCessions.value.operationId === 2) {
				return 'Selecione o Dado Bancário para pagamento';
			}
			return 'Selecione o fornecedor para pagamento';
		});
		const isLoading = computed(() => {
			if (
				isLoadingInformationCession.value ||
				isLoadingCancel.value ||
				isLoadingProvider.value ||
				isLoadingRemoveProvider.value
			) {
				return true;
			}
			return false;
		});
		const isError = computed(() => {
			if (isErrorInformationCession.value) {
				return true;
			}
			return false;
		});

		const isDisabledButtonConfirm = computed(() => {
			if (isCancel.value) {
				return false;
			}
			if (isDateInvalid.value) {
				return true;
			}
			if (isMinValue.value) {
				return true;
			}
			if (isProvider.value && listEmpty.value) {
				return listEmpty.value;
			}
			if (!isProvider.value && listOriginatorEmpty.value) {
				return true;
			}
			if (!checkboxModel.value) {
				return true;
			}
			if (isProvider.value && valuesSupplierNotValid.value) {
				return true;
			}
			if (isProvider.value && !isValidForm.value) {
				return true;
			}
			if (isProvider.value && !totalAmountUsed.value) {
				return true;
			}
			return false;
		});

		const providerListId = computed(() => {
			return providerData.value.map(item => {
				return item.id;
			});
		});

		function triggerEventAnalytics(description: string): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 4,
					description,
				},
			};
			trigger(dataTrigger);
		}

		function onOpenModalReceivablesApproved(data): void {
			cessionCurrent.value = data;
			triggerEventAnalytics(`clicou no botão para abrir a modal de aprovados`);
			openModalReceivablesApproved();
		}

		function onOpenModalDenyReasons(data): void {
			cessionCurrent.value = data;
			triggerEventAnalytics(`clicou no botão para abrir a modal de motivos de recusa`);
			openModalDenyReasons();
		}

		function onOpenModalDetailsCession(): void {
			triggerEventAnalytics('clicou no detalhe da cessão');
			openModalDetailCession();
		}

		function onConfirmModalCancelCession(): void {
			cancelCession(operationId, redirectHome);
			triggerEventAnalytics(`clicou no botão de sim cancelar cessão`);
		}

		function onOpenModalCancel(): void {
			triggerEventAnalytics(`clicou no botão de cancelar cessão`);
			openModalCancelCession();
		}

		function onClickConfirmFooter(): void {
			if (isCancel.value) {
				triggerEventAnalytics(
					`clicou no botão voltar cessão status cancelada - ${operationId}`
				);
				redirectHome();
				return;
			}

			if (isProvider.value && valuesSupplierNotValid.value) {
				notification(
					RequestStatusEnum.ERROR,
					'Todos os fornecedores devem possuir os valores definidos'
				);
				return;
			}
			redirectNextStep();
			triggerEventAnalytics(`clicou no botão criar cessão - ${operationId}`);
		}

		function onClickBack(): void {
			triggerEventAnalytics(`clicou no botão voltar - ${operationId}`);
			redirectSummary();
		}

		function onOpenModalProvider(): void {
			triggerEventAnalytics(
				`clicou no botão adicionar fornecedor para pagamento - ${operationId}`
			);
			openModalProvider();
		}

		function onCloseModalDataBankWithLoad(): void {
			onCloseModalDataBank();
			load();
		}

		function onConfirmModalProviderRemove(): void {
			removeProviderInOperation(
				{
					id: operationId,
					idProvider: providerCurrent.value.id,
					name: providerCurrent.value.name,
				},
				load
			);
			triggerEventAnalytics(`clicou no botão de sim remover um fornecedor - ${operationId}`);
			onCloseModalProviderRemove();
		}

		function onConfirmModaDataBank(): void {
			load();
		}

		function onRemoveProvider(item): void {
			providerCurrent.value = item;
			triggerEventAnalytics(`clicou no botão de remover um fornecedor - ${operationId}`);
			openModalDProviderRemove();
		}

		function onAddDataBank(item): void {
			providerCurrent.value = item;
			triggerEventAnalytics(`clicou no botão de dados bancario- ${operationId}`);
			openModalDataBank();
		}

		function checkStatusCession(data): boolean {
			if (data.deleteAt !== null) {
				isCancel.value = true;
				dataCancel.value = {
					name: data.deletedBy,
					date: defaultDateFormat(data.deleteAt) || 'N/A',
					hour: builderHoursCession(data.deleteAt) || 'N/A',
				};
				isFailCession.value = data.failOnCreation;
				return true;
			}
			return false;
		}

		function checkDateDisbursementIsValid(data): void {
			if (!data.dateDisbursementIsValid) {
				isDateInvalid.value = true;
			}
		}

		function updatedDataDetailCession(data): void {
			headerData.value = {
				dateDisbursement: defaultDateFormat(data.dateDisbursement),
				limit: brl(data.limit),
				valueDisbursement: brl(data.valueDisbursement),
				productName: data.productName,
				typeOperation: data.typeOperation,
			};
		}

		function updatedListProvider(data): void {
			if (data.length === 0) {
				providerData.value = [];
				listEmpty.value = true;
				return;
			}
			providerData.value = [...data];
			const isValid = data.filter(item => {
				return item.haveBankDataAssociated === true;
			});
			listEmpty.value = isValid.length === 0;
		}

		function checkMinValue(data): void {
			const { valueDisbursement, minimumCessionValue } = data;
			if (valueDisbursement < minimumCessionValue) {
				isMinValue.value = true;
				return;
			}
			isMinValue.value = false;
		}

		function updatedValueMin(data): void {
			labelMinValue.value = brl(data.minimumCessionValue);
			addDataCache({
				minValue: data.minimumCessionValue,
			});
			checkMinValue(data);
		}

		function updatedPage(data): void {
			updatedDataDetailCession(data);
			updatedValueMin(data);
			if (data.operationId === 1) {
				const params = {
					page: 0,
					limit: 10,
					order: 'DESC',
					onlyInOperation: true,
					financialVehicleId: data.financialVehicle,
				};
				getProviderInOperation(
					{
						id: operationId,
						params,
					},
					updatedListProvider
				);
			} else {
				isProvider.value = false;
			}
			financialVehicleId.value = data.financialVehicle;
			if (checkStatusCession(data)) {
				return;
			}
			checkDateDisbursementIsValid(data);
		}

		function load(): void {
			getInformationCession(operationId, updatedPage);
		}

		function onReload(): void {
			load();
		}

		function onValidButton(value: boolean): void {
			listOriginatorEmpty.value = !value;
		}

		function onChangeinputValues(values) {
			valuesSupplierNotValid.value = values.some(
				value =>
					value === null ||
					value === '0' ||
					value === 'R$0,00' ||
					value === 'R$0,0' ||
					value === 'R$0,' ||
					value === 'R$0' ||
					value === 'R$' ||
					value === '0.00'
			);
		}

		function onChangeIsValidForm(value): void {
			isValidForm.value = value;
		}

		function onChangeTotalAvailable(value): void {
			totalAmountUsed.value = value;
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isLoadingCancel,
			isError,
			isDisabledButtonConfirm,
			isCancel,
			isFailCession,
			isValueInvalid,
			isDateInvalid,
			isModalDetailCession,
			isModalShowing,
			isModalCancelCession,
			isModalProvider,
			isModalProviderRemove,
			isModalDataBank,
			informationCessions,
			isModalDenyReasons,
			isModalReceivablesApproved,
			isModalInformationImportant,
			onCloseModalInformationImportant,
			openModalInformationImportant,
			onChangeIsValidForm,
			onChangeTotalAvailable,
			isProvider,
			isMinValue,
			labelMinValue,
			providerData,
			providerListId,
			providerCurrent,
			title,
			checkboxModel,
			headerData,
			dataCancel,
			financialVehicleId,
			cessionCurrent,
			onCloseModalDetailCession,
			onOpenModalDetailsCession,
			onConfirmModalCancelCession,
			onCloseModalCancelCession,
			onOpenModalCancel,
			onCloseModalDenyReasons,
			onCloseModalReceivablesApproved,
			onClickConfirmFooter,
			onClickBack,
			onCloseModalProvider,
			onOpenModalProvider,
			onCloseModalProviderRemove,
			onConfirmModalProviderRemove,
			onCloseModalDataBankWithLoad,
			onReload,
			onRemoveProvider,
			onAddDataBank,
			onConfirmModaDataBank,
			load,
			onOpenModalReceivablesApproved,
			onOpenModalDenyReasons,
			onValidButton,
			onChangeinputValues,
		};
	},
});
</script>
