import { useAnalytics } from '@/composible/useAnalytics';
import useRouter from '@/composible/useRouter';

type UseRedirect = {
	redirectHome: Function;
	redirectChoiceOfReceivables: Function;
	redirectNextStep: Function;
};

export function useRedirect(): UseRedirect {
	const router = useRouter();
	const { trigger } = useAnalytics();

	function redirectChoiceOfReceivables(id: number, vehicleId?: number): void {
		const dataTrigger = {
			event: 'operation_cession',
			payload:{
				step: 3,
				description:`clicou no botão voltar`
			}
		};
		trigger(dataTrigger);
		router.push(
			`/admin/wallet/cessoes/escolha_de_recebiveis?id_pre_section=${id}&vehicleId=${vehicleId}`
		);
	}

	function redirectHome(): void {
		router.push(`/admin/wallet/cessoes`);
	}

	function redirectNextStep(id: number): void {
		router.push(`/admin/wallet/cessoes/pagamento_do_fornecedor/${id}`);
	}

	return {
		redirectHome,
		redirectChoiceOfReceivables,
		redirectNextStep,
	};
}
