<template>
	<farm-container>
		<header-cession :currentStep="3" :errorCurrentStep="isCancel" :title="title" />
		<farm-box class="mt-2">
			<farm-row v-if="isCancel && !isFailCession">
				<farm-col cols="12">
					<feedback-alert color="error">
						Sua cessão foi cancelada pelo usuário <b> {{ dataCancel.name }} </b> no dia
						<b>{{ dataCancel.date }}</b
						>.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="isCancel && isFailCession">
				<farm-col cols="12">
					<feedback-alert color="error">
						Não foi possível validar seus recebíveis, sua cessão
						foi cancelada no dia <b>{{ dataCancel.date }} às {{ dataCancel.hour }}</b>.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="isNotReceivable">
				<farm-col cols="12">
					<feedback-alert color="error">
						Todos seus recebíveis foram recusados. Selecione outros recebíveis no passo
						anterior clicando em <b>“Voltar”</b> ou descarte esse rascunho clicando em
						<b>“Cancelar”</b>.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isCancel && isDateInvalid">
				<farm-col cols="12">
					<feedback-alert>
						A data selecionada não é mais permitida. Selecione outra data clicando em
						<b>“Voltar”</b> ou descarte esse rascunho clicando em <b>“Cancelar”</b>.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isCancel && !isNotReceivable && isMinValue">
				<farm-col cols="12">
					<feedback-alert color="error">
						O valor da cessão sofreu algumas <b>alterações</b> por conta da análise dos
						recebíveis. É necessário atingir no mínimo {{ labelMinValue }} em recebíveis
						para continuar. Tente alterar ou selecionar mais recebíveis no passo
						anterior.
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="12">
					<farm-bodytext :type="2" bold class="my-4">Detalhes da Cessão</farm-bodytext>
					<information-cession-list
						:dateDisbursement="headerData.dateDisbursement"
						:limit="headerData.limit"
						:productName="headerData.productName"
						:typeOperation="headerData.typeOperation"
						:valueDisbursement="headerData.valueDisbursement"
						@onClickDisbursement="onOpenModalDetailsCession"
					/>
				</farm-col>
			</farm-row>
			<farm-row extra-decrease>
				<farm-line />
			</farm-row>
			<farm-row v-if="isCancel">
				<farm-col cols="12">
					<feedback-alert color="info">
						<farm-typography color="info" tag="p">
							Os recebíveis da cessão cancelada estão disponíveis no banco de
							recebíveis para serem usadas novamente.
						</farm-typography>
					</feedback-alert>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isError" class="mt-4">
				<farm-col cols="12">
					<farm-bodytext :type="2" bold> Validação dos recebíveis </farm-bodytext>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isError" class="mt-2">
				<farm-col class="mt-6" cols="12">
					<collapsible-cession-receivables-approved
						:approved="headerData.approved"
						:data="receivablesApproved"
						:isOpen="false"
						:disabledButton="headerData.approved === 0 || !canWrite"
						:pagination="receivablesApprovedPagination"
						:filter="filterReceivablesApproved"
						:headerTable="headers"
						@onRequest="onRequest"
						@onClickDownload="onClickReceivablesApproved"
						@onStatusOpen="onStatusOpenReceivablesApproved"
					/>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isError">
				<farm-col class="mt-6" cols="12">
					<collapsible-cession-deny-reasons
						:notApproved="headerData.notApproved"
						:data="denyReasonV2"
						:pagination="denyReasonPagination"
						:isOpen="false"
						:disabledButton="headerData.notApproved === 0 || !canWrite"
						@onClickDownload="onClickDenyReasons"
						@onStatusOpen="onStatusOpenDenyReasons"
						@onRequest="onRequestDenyReasons"
					/>
				</farm-col>
			</farm-row>
		</farm-box>
		<farm-loader v-if="isLoading" mode="overlay" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<footer-cession
			:hiddenBack="isCancel"
			:hiddenClose="isCancel"
			:isDisabledButtonConfirm="isDisabledButtonConfirm"
			:labelConfirm="isCancel ? 'Voltar' : 'Continuar'"
			labelCancel="Cancelar"
			@onClickBack="onClickBack"
			@onClickClose="onOpenModalCancel"
			@onClickConfirm="onClickConfirmFooter"
		/>
		<modal-detail-cession
			v-if="isModalDetailCession"
			v-model="isModalDetailCession"
			:data="informationCessions"
			:receivables="false"
			@onClose="onCloseModalDetailCession"
		/>
		<modal-cancel-cession
			v-if="isModalCancelCession"
			v-model="isModalCancelCession"
			@onClose="onCloseModalCancelCession"
			@onConfirm="onConfirmModalCancelCession"
		/>
	</farm-container>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import CollapsibleCessionDenyReasons from '@/components/CollapsibleCessionDenyReasons';
import CollapsibleCessionReceivablesApproved from '@/components/CollapsibleCessionReceivablesApproved';
import FeedbackAlert from '@/components/FeedbackAlert';
import InformationCessionList from '@/components/InformationCessionList';
import ModalCancelCession from '@/components/ModalCancelCession';
import ModalDetailCession from '@/components/ModalDetailCession';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';
import useRoute from '@/composible/useRoute';
import { useProductCache } from '@/features/operationCession/composables/useProductCache';

import { useCancelCession } from '../../composables/useCancelCession';
import { useChangeStep } from '../../composables/useChangeStep';
import { useDenyReasonsV2 } from '../../composables/useDenyReasonsV2';
import { useExportExcel } from '../../composables/useExportExcel';
import { useInformationCession } from '../../composables/useInformationCession';
import { useReceivablesApproved } from '../../composables/useReceivablesApproved';
import { headers } from '../../configurations/headers';
import { builderHoursCession } from '../../helpers/builderHoursCession';
import type { BuilderInformationCession } from '../../helpers/builderInformationCession';
import Footer from '../Footer';
import Header from '../Header';

import { useRedirect } from './composables/useRedirect';

export default defineComponent({
	name: 'body-step-three-summary',
	components: {
		'header-cession': Header,
		'footer-cession': Footer,
		InformationCessionList,
		FeedbackAlert,
		ModalDetailCession,
		ModalCancelCession,
		CollapsibleCessionDenyReasons,
		CollapsibleCessionReceivablesApproved,
	},
	setup() {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const { onDownloadExcel } = useExportExcel();
		const {
			informationCessions,
			getInformationCession,
			isLoadingInformationCession,
			isErrorInformationCession,
		} = useInformationCession();
		const {
			getDenyReasonV2,
			denyReasonPagination,
			denyReasonV2,
			isErrorDenyReason,
			isLoadingDenyReason
		} = useDenyReasonsV2();

		const {
			getReceivablesApproved,
			isErrorReceivablesApproved,
			isLoadingReceivablesApproved,
			receivablesApproved,
			receivablesApprovedPagination,
		} = useReceivablesApproved();
		const { changeStep, isLoadingStepPatch } = useChangeStep();
		const {
			isOpenModal: isModalDetailCession,
			onCloseModal: onCloseModalDetailCession,
			onOpenModal: onOpenModalDetailCession,
		} = useModal();
		const {
			isOpenModal: isModalCancelCession,
			onCloseModal: onCloseModalCancelCession,
			onOpenModal: onOpenModalCancelCession,
		} = useModal();
		const { redirectChoiceOfReceivables, redirectHome, redirectNextStep } = useRedirect();
		const { cancelCession, isLoadingCancel } = useCancelCession();
		const { addDataCache } = useProductCache();

		const operationId = route.params.id;

		const isCancel = ref(false);
		const isFailCession = ref(false);
		const isNotReceivable = ref(false);
		const isDateInvalid = ref(false);
		const isMinValue = ref(true);
		const labelMinValue = ref('R$ 0,00');
		const filterReceivablesApproved = ref({
			page: 0,
			limit: 10,
		});
		const headerData = ref({
			dateDisbursement: '-',
			limit: '-',
			valueDisbursement: '-',
			productName: '-',
			typeOperation: '-',
			approved: 0,
			notApproved: 0,
		});
		const dataCancel = ref({
			name: '-',
			date: '-',
		});

		const title = computed(() => {
			return isCancel.value ? 'Cessão Cancelada' : 'Revise e confirme os dados da cessão';
		});
		const isDisabledButtonConfirm = computed(() => {
			if (isDateInvalid.value) {
				return true;
			}
			if (isNotReceivable.value) {
				return true;
			}
			if (isMinValue.value) {
				return true;
			}
			return false;
		});
		const isLoading = computed(() => {
			return (
				isLoadingInformationCession.value ||
				isLoadingCancel.value ||
				isLoadingDenyReason.value ||
				isLoadingReceivablesApproved.value ||
				isLoadingStepPatch.value
			);
		});
		const isError = computed(() => {
			return (
				isErrorDenyReason.value ||
				isErrorInformationCession.value ||
				isErrorReceivablesApproved.value
			);
		});

		function triggerEventAnalytics(description: string): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 3,
					description,
				},
			};
			trigger(dataTrigger);
		}

		function onRequest(params): void {
			filterReceivablesApproved.value = { ...params };
			getReceivablesApproved(operationId, filterReceivablesApproved.value);
		}

		function onOpenModalDetailsCession(): void {
			triggerEventAnalytics('clicou no detalhe da cessão');
			onOpenModalDetailCession();
		}

		function onConfirmModalCancelCession(): void {
			cancelCession(operationId, redirectHome);
			triggerEventAnalytics(`clicou no botão de sim cancelar cessão`);
		}

		function onOpenModalCancel(): void {
			triggerEventAnalytics(`clicou no botão de cancelar cessão`);
			onOpenModalCancelCession();
		}

		function checkChangeStep(error): void {
			if (error) {
				return;
			}
			redirectNextStep(operationId);
		}

		function onClickConfirmFooter(): void {
			if (isCancel.value) {
				triggerEventAnalytics(`clicou no botão voltar cessão status cancelada`);
				redirectHome();
				return;
			}
			const newStep = 4;
			const payload = {
				newStep,
				id: operationId,
			};
			changeStep(payload, checkChangeStep);
			triggerEventAnalytics(`clicou no botão de continuar`);
		}

		function onClickBack(): void {
			const informationCession = informationCessions.value as BuilderInformationCession;
			const vehicleId = informationCession.financialVehicle;
			redirectChoiceOfReceivables(operationId, vehicleId);
		}

		function onClickDenyReasons(): void {
			const status = 1;
			onDownloadExcel({ id: operationId, status });
			triggerEventAnalytics(`clicou no botão exportar motivos de recusa`);
		}

		function onClickReceivablesApproved(): void {
			const status = 2;
			onDownloadExcel({ id: operationId, status });
			triggerEventAnalytics(`clicou no botão exportar recebiveis aprovados`);
		}

		function checkStatusCession(data): boolean {
			if (data.deleteAt !== null) {
				isCancel.value = true;
				dataCancel.value = {
					name: data.deletedBy,
					date: defaultDateFormat(data.deleteAt) || 'N/A',
					hour: builderHoursCession(data.deleteAt) || 'N/A',
				};
				isFailCession.value = data.failOnCreation;
				return true;
			}
			return false;
		}

		function checkDateDisbursementIsValid(data): boolean {
			if (!data.dateDisbursementIsValid) {
				isDateInvalid.value = true;
				return true;
			}
			return false;
		}

		function checkHasReceivable(data): boolean {
			if (data.approved === 0) {
				isNotReceivable.value = true;
				return true;
			}
			return false;
		}

		function updatedDataDetailCession(data): void {
			headerData.value = {
				dateDisbursement: defaultDateFormat(data.dateDisbursement),
				limit: brl(data.limit),
				valueDisbursement: brl(data.valueDisbursement),
				productName: data.productName,
				typeOperation: data.typeOperation,
				approved: data.approved,
				notApproved: data.refused,
			};
		}

		function checkMinValue(data): void {
			const { valueDisbursement, minimumCessionValue } = data;
			if (valueDisbursement < minimumCessionValue) {
				isMinValue.value = true;
				return;
			}
			isMinValue.value = false;
		}

		function updatedValueMin(data): void {
			labelMinValue.value = brl(data.minimumCessionValue);
			addDataCache({
				minValue: data.minimumCessionValue,
			});
			checkMinValue(data);
		}

		function updatedPage(data): void {
			updatedDataDetailCession(data);
			updatedValueMin(data);
			if (checkStatusCession(data)) {
				return;
			}
			if (checkDateDisbursementIsValid(data)) {
				return;
			}
			checkHasReceivable(data);
		}

		function onStatusOpenDenyReasons(open: boolean): void {
			const action = open ? 'expandir' : 'esconder';
			triggerEventAnalytics(`clicou para ${action} os motivos de recusa`);
		}

		function onStatusOpenReceivablesApproved(open: boolean): void {
			const action = open ? 'expandir' : 'esconder';
			triggerEventAnalytics(`clicou para ${action} os recebiveis aprovados`);
		}

		function onRequestDenyReasons(data){
			const params = {
				...data,
				'id_status':1
			};
			getDenyReasonV2(operationId, params);
		}

		function load(): void {
			getInformationCession(operationId, updatedPage);
			getReceivablesApproved(operationId, filterReceivablesApproved.value);
			const params = {
				page:0,
				limit:10,
				'id_status':1
			};
			getDenyReasonV2(operationId, params);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			isDisabledButtonConfirm,
			isModalDetailCession,
			isModalCancelCession,
			isCancel,
			isFailCession,
			isNotReceivable,
			isDateInvalid,
			isMinValue,
			labelMinValue,
			title,
			filterReceivablesApproved,
			headerData,
			informationCessions,
			denyReasonV2,
			denyReasonPagination,
			receivablesApproved,
			receivablesApprovedPagination,
			headers,
			dataCancel,
			updatedDataDetailCession,
			onOpenModalDetailsCession,
			onOpenModalCancel,
			onClickBack,
			onCloseModalDetailCession,
			onClickConfirmFooter,
			onCloseModalCancelCession,
			onConfirmModalCancelCession,
			onReload,
			onClickDenyReasons,
			onRequest,
			onClickReceivablesApproved,
			onStatusOpenReceivablesApproved,
			onStatusOpenDenyReasons,
			onRequestDenyReasons,
		};
	},
});
</script>
