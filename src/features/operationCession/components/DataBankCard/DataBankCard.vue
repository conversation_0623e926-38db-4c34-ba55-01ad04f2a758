<template>
	<cards>
		<template slot="header">
			<farm-row align="center" justify="space-between">
				<farm-col cols="6">
					<farm-row align="center" justify="space-between" no-default-gutters>
						<farm-radio v-model="model" :value="data.id"  size="md" />
						<farm-col>
							<farm-caption> Tipo</farm-caption>
							<farm-bodytext :type="2" :variation="'bold'">
								{{ data.type === 'account'? data.accountType: 'Pix' }}
							</farm-bodytext>
						</farm-col>
					</farm-row>
				</farm-col>
				<farm-col cols="6">
					<farm-row no-default-gutters>
						<farm-col v-if="data.type === 'account'" no-gutters>
							<farm-caption> Banco</farm-caption>
							<farm-bodytext :type="2" :variation="'bold'" ellipsis>
								{{ data.bank }}
							</farm-bodytext>
						</farm-col>
					</farm-row>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row
				v-if="data.type === 'account'"
				align="center"
				justify="space-between"
			>
				<farm-col cols="6">
					<card-body :value="data.agency" ellipsisValue label="Agência" />
				</farm-col>
				<farm-col cols="6">
					<card-body
						:value="data.account"
						ellipsisValue
						label="Número da Conta"
					/>
				</farm-col>
			</farm-row>
			<farm-row
				v-if="data.type === 'pix'"
				align="center"
				justify="space-between"
			>
				<farm-col cols="6">
					<card-body
						:value="data.pixKeyType"
						ellipsisValue
						label="Tipo de Chave"
					/>
				</farm-col>
				<farm-col cols="6">
					<card-body :value="data.pixKey" ellipsisValue label="Chave" />
				</farm-col>
			</farm-row>
		</template>
	</cards>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue';

import CardTextBody from '@/components/CardTextBody';
import Cards from '@/components/Cards';

export default defineComponent({
	name:"data-bank-card",
	components: {
		Cards,
		'card-body': CardTextBody,
	},
	props: {
		data: {
			type: Object,
			required: true,
		},
		selected: {
			type: Number,
			required: true,
		},

	},
	setup(props, { emit }) {
		const { data, selected } = toRefs(props);
		const model = ref(data.value.inOperation ? data.value.id : 0);

		watch(selected, (newValue) => {
			model.value = newValue;
		});

		watch(model, (newValue) => {
			emit('changeValue', newValue);
			emit('onUpdateDataBankClicked', newValue);
		});

		return {
			model,
			data,
		};
	},
});
</script>
