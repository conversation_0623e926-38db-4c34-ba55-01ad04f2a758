<template>
	<farm-card :class="{ 'farm-card--active': isChecked }">
		<farm-card-content>
			<farm-row align="center">
				<farm-col md="2">
					<farm-checkbox
						v-model="model"
						:value="data.id"
						size="sm"
						:checked="isChecked"
					/>
				</farm-col>
				<farm-col md="5">
					<farm-caption>Razão Social</farm-caption>
					<farm-bodytext :type="2" ellipsis variation="bold">
						{{ data.name }}
					</farm-bodytext>
				</farm-col>
				<farm-col>
					<farm-caption>CNPJ</farm-caption>
					<farm-bodytext :type="2" variation="bold">
						{{ data.document }}
					</farm-bodytext>
				</farm-col>
			</farm-row>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { onMounted } from 'vue';
import { defineComponent, toRefs, ref, watch } from 'vue';

export default defineComponent({
	name: 'provider-selected-card',
	props: {
		data: {
			type: Object,
			required: true,
		},
		checked: {
			type: Boolean,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data, checked } = toRefs(props);
		const model = ref(data.inOperation ? data.id : 0);
		const isChecked = ref(false);

		watch(model, () => {
			emit('onChangeValue', data.value.id);
			isChecked.value = !isChecked.value;
		});

		onMounted(() => {
			setTimeout(function () {
				isChecked.value = checked.value;
			}, 100);
		});

		return {
			data,
			isChecked,
			model,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './ProviderSelectedCard';
</style>

