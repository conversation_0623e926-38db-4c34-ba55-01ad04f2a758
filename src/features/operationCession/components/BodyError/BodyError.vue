<template>
	<farm-container>
		<farm-box>
			<farm-row justify="center" class="mb-4">
				<farm-heading color="primary" type="5" v-if="isSimulation">
					Não foi possível validar seus recebíveis.
				</farm-heading>
				<farm-heading color="primary" type="5" v-if="!isSimulation">
					Não foi possível criar sua cessão.
				</farm-heading>
			</farm-row>
			<farm-row justify="center">
				<div class="d-flex justify-center mb-4">
					<img
						alt="imagem referente a erro na simulação ou cessão"
						class="page-error-img"
						src="@/assets/error.svg"
					/>
				</div>
			</farm-row>
			<farm-row justify="center">
				<farm-col  class="text-center" cols="12">
					<farm-bodytext :type="2" :variation="'medium'" v-if="isSimulation">
						Tente validar novamente em instantes, se persistir entre
						em contato com o <br/>responsável pela sua conta FarmTech.
					</farm-bodytext>
					<farm-bodytext :type="2" :variation="'bold'" v-if="!isSimulation">
						Tente criar novamente em instantes, se persistir entre em contato com o
						<br/>responsável pela sua conta FarmTech.
					</farm-bodytext>
				</farm-col>
			</farm-row>
			<farm-row justify="center" class="mt-4" v-if="isSimulation">
				<farm-col  class="text-center" cols="6">
					<farm-caption
						:variation="'regular'"
						:color="'gray'">
						Os recebíveis dessa cessão estão disponíveis para uso.
					</farm-caption>
				</farm-col>
			</farm-row>
			<farm-row justify="center">
				<farm-col class="text-center mt-6" cols="6">
					<farm-btn @click="onRedirect">
						Retornar ao Inicio
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-box>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import useRouter from '@/composible/useRouter';

export default defineComponent({
	name: 'body-error',
	props:{
		isSimulation: {
			type: Boolean,
			default: false
		}
	},
	setup() {
		const router = useRouter();

		function onRedirect(): void {
			router.push(`/admin/wallet/cessoes`);
		}

		return {
			onRedirect
		};
	}
});
</script>
<style lang="scss" scoped>
@import './BodyError';
</style>
