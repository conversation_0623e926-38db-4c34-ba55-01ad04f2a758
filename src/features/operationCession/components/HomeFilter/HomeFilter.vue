<template>
	<farm-box>
		<farm-form class="mb-6">
			<farm-row>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-product"> Produto </farm-label>
					<farm-select
						id="form-filter-product"
						v-model="productModel"
						:items="productSelect"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-type-operation"> Tipo de Operação </farm-label>
					<farm-select
						id="form-filter-type-operation"
						v-model="typeOperationModel"
						:items="typeOperationSelect"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-status"> Status </farm-label>
					<farm-select
						id="form-filter-status"
						v-model="statusModel"
						:items="statusListSelect"
						item-text="label"
						item-value="id"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-createdAt"> Criação (Início/fim)  </farm-label>
					<farm-input-rangedatepicker
						ref="datepickerCreatedAt"
						inputId="form-filter-createdAt"
						v-model="createdAtModel"
						@input="onInputDatepickerCreatedAt"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-updatedAt">
						Atualização (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerUpdatedAtt"
						inputId="form-filter-updatedAt"
						v-model="updatedAtModel"
						@input="onInputDatepickerUpdatedAt"
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="form-filter-dateDisbursement">
						Desembolso (Início/fim)
					</farm-label>
					<farm-input-rangedatepicker
						ref="datepickerDateDisbursement"
						inputId="form-filter-expiredAtRange"
						v-model="dateDisbursementModel"
						@input="onInputDatepickerDateDisbursement"
					/>
				</farm-col>
				<farm-col cols="2">
					<farm-label for="form-pj-status"> Exibir Cessões Canceladas? </farm-label>
					<div class="d-flex">
						<farm-switcher
						class="mt-3"
						id="form-pj-status"
						v-model="canceledModel"
						block
						@input="onInput"
					></farm-switcher>
					<farm-bodytext
						class="ml-3 mt-2"
						color="neutral"
						colorVariation="darken"
						variation="regular"
						:type="2"
					>
						{{ canceledModel ? 'Sim': 'Não' }}
					</farm-bodytext>
					</div>

				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col>
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive"
						title="Aplicar Filtros"
						@click="onFilterConfirm"
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="onFilterClear"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</farm-form>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, watch } from 'vue';

import { useAnalytics } from '@/composible/useAnalytics';

import { statusData } from './configurations';

export default defineComponent({
	name:"home-filter",
	props: {
		product: {
			type: Array,
			required: true,
		},
		operationType: {
			type: Array,
			required: true,
		},
		filterCurrent: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { product, operationType, filterCurrent } = toRefs(props);
		const { trigger } = useAnalytics();

		const statusModel = ref('');
		const productModel = ref('');
		const typeOperationModel = ref('');
		const createdAtModel = ref([]);
		const updatedAtModel = ref([]);
		const dateDisbursementModel = ref([]);
		const statusListSelect = ref(statusData);
		const productSelect = ref(product.value || []);
		const typeOperationSelect = ref(operationType.value || []);
		const canceledModel = ref(false);

		function onInput(value): void {
			if(value){
				statusListSelect.value = [
					{
						id: 8,
						label: 'Cancelada'
					},
					...statusData
				];
				return;
			}
			statusModel.value = null;
			statusListSelect.value = [...statusData];
		}

		function checkValueDatepicker(value: string) {
			if (value.length === 2) {
				const startDate = new Date(value[0]);
				const endDate = new Date(value[1]);
				if (startDate > endDate) {
					return [value[1], value[0]];
				}
			}
			return value;
		}

		function checkDatepickerCompleted(data, keyStart, keyEnd) {
			return {
				[keyStart]: data[0],
				[keyEnd]: data[1]
			};
		}

		function validDataFilter() {
			let obj = {};
			if(statusModel.value) {
				obj = {
					status: statusModel.value
				};
			}
			if(productModel.value) {
				obj = {
					...obj,
					commercialProdName: productModel.value
				};
			}
			if(typeOperationModel.value) {
				obj = {
					...obj,
					sectionType: typeOperationModel.value
				};
			}
			if(createdAtModel.value && createdAtModel.value.length === 2){
				const result = checkDatepickerCompleted(
					createdAtModel.value,
					'createAtStart',
					'createAtEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			if(updatedAtModel.value && updatedAtModel.value.length === 2){
				const result = checkDatepickerCompleted(
					updatedAtModel.value,
					'updatedAtStart',
					'updatedAtEnd'
				);
				obj = {
					...obj,
					...result
				};
			}
			if(dateDisbursementModel.value && dateDisbursementModel.value.length === 2){
				const result = checkDatepickerCompleted(
					dateDisbursementModel.value,
					'dateDisbursementStart',
					'dateDisbursementEnd'
				);
				obj = {
					...obj,
					...result
				};
			}

			obj = {
				...obj,
				showCancelled: canceledModel.value
			};
			return obj;
		}

		function onInputDatepickerCreatedAt(value: string): void {
			createdAtModel.value = checkValueDatepicker(value);
		}

		function onInputDatepickerUpdatedAt(value: string): void {
			updatedAtModel.value = checkValueDatepicker(value);
		}

		function onInputDatepickerDateDisbursement(value: string): void {
			dateDisbursementModel.value = checkValueDatepicker(value);
		}

		function checkHasProps(obj, prop): boolean{
			return Object.hasOwn(obj, prop);
		}

		function onFilterConfirm(): void {
			const dataFilter = validDataFilter();
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					description:'clicou na opção de aplicar filtro',
					filter:{
						createAtEnd: checkHasProps(dataFilter, 'createAtEnd') || "N/A",
						createAtStart: checkHasProps(dataFilter, 'createAtStart') || "N/A",
						dateDisbursementEnd: checkHasProps(dataFilter, 'dateDisbursementEnd') || "N/A",
						dateDisbursementStart: checkHasProps(dataFilter, 'dateDisbursementStart') || "N/A",
						status: checkHasProps(dataFilter, 'status') || "N/A",
						updatedAtEnd: checkHasProps(dataFilter, 'updatedAtEnd') || "N/A",
						updatedAtStart: checkHasProps(dataFilter, 'updatedAtStart') || "N/A",
					}

				}
			};
			trigger(dataTrigger);
			emit('onApply', dataFilter);
			emit('onFiltersApplied', true);
		}

		function onFilterClear(): void {
			statusModel.value = null;
			createdAtModel.value = null;
			updatedAtModel.value = null;
			dateDisbursementModel.value = null;
			typeOperationModel.value = null;
			productModel.value = null;
			canceledModel.value = false;
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					description:'clicou na opção de limpar filtro',
				}
			};
			trigger(dataTrigger);
			emit('onApply', {
				page: 0,
				limit: filterCurrent.value.limit || 10,
				showCancelled: canceledModel.value
			});
			emit('onFiltersApplied', false);
		}

		watch(product, (newValue) => {
			productSelect.value = newValue;
		});

		watch(operationType, (newValue) => {
			typeOperationSelect.value = newValue;
		});

		return {
			canceledModel,
			statusModel,
			createdAtModel,
			updatedAtModel,
			dateDisbursementModel,
			statusListSelect,
			productModel,
			typeOperationModel,
			productSelect,
			typeOperationSelect,
			onFilterConfirm,
			onFilterClear,
			onInputDatepickerCreatedAt,
			onInputDatepickerUpdatedAt,
			onInputDatepickerDateDisbursement,
			onInput
		};
	},
});
</script>
