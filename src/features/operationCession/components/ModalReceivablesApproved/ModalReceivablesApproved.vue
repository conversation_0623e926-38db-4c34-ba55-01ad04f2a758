<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="98" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Aprovados" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-row v-if="!isError">
				<farm-col cols="12" class="mb-4">
					<farm-caption variation="regular">
						Visualize aqui seus recebíveis aprovados para essa cessão.
					</farm-caption>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isError">
				<farm-col cols="12" class="mt-6">
					<collapsible-cession-receivables-approved
						:approved="count"
						:data="receivablesApproved"
						:isOpen="true"
						:disabledButton="count === 0"
						:pagination="receivablesApprovedPagination"
						:filter="filterReceivablesApproved"
						:headerTable="headers"
						@onRequest="onRequest"
						@onClickDownload="onClickExport"
						@onStatusOpen="onStatusOpenReceivablesApproved"
					/>
				</farm-col>
			</farm-row>
			<farm-loader mode="overlay" v-if="isLoading" />
			<div v-if="isError" class="my-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer confirmLabel="Fechar" :hasCancel="false" @onConfirm="onClose" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

import CollapsibleCessionReceivablesApproved from '@/components/CollapsibleCessionReceivablesApproved';
import TableReceivablesApproved from '@/components/TableReceivablesApproved';
import { useAnalytics } from '@/composible/useAnalytics';
import { useExportExcel } from '@/features/operationCession/composables/useExportExcel';

import { useReceivablesApproved } from '../../composables/useReceivablesApproved';
import { headers } from '../../configurations/headers';


export default defineComponent({
	name: 'modal-receivables-approved',
	components: {
		CollapsibleCessionReceivablesApproved,
		TableReceivablesApproved,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		cessionCurrent: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { trigger } = useAnalytics();
		const {
			getReceivablesApproved,
			isErrorReceivablesApproved,
			isLoadingReceivablesApproved,
			receivablesApproved,
			receivablesApprovedPagination
		} = useReceivablesApproved();

		const { onDownloadExcel } = useExportExcel();

		const count = ref(props.cessionCurrent.approved);

		const filterReceivablesApproved = ref({
			page: 0,
			limit: 10,
		});

		function onClose(): void {
			emit('onCloseModal');
		}

		function onClickExport(): void {
			const id = props.cessionCurrent.id;
			const status = 2;
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					description:'clicou no botão exportar recebiveis aprovados'
				}
			};
			trigger(dataTrigger);
			onDownloadExcel({ id, status });
		}

		function onRequest(data) {
			const id = props.cessionCurrent.id;
			filterReceivablesApproved.value = {
				...data,
			};
			getReceivablesApproved(id, filterReceivablesApproved.value);
		}

		function onStatusOpenReceivablesApproved(data: boolean): void {
			const action = data ? 'expandir': 'esconder';
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					description:`clicou para ${action} os recebiveis aprovados`
				}
			};
			trigger(dataTrigger);
		}

		function load(): void {
			const id = props.cessionCurrent.id;
			getReceivablesApproved(id, filterReceivablesApproved.value);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			isError: isErrorReceivablesApproved,
			isLoading: isLoadingReceivablesApproved,
			count,
			receivablesApproved,
			filterReceivablesApproved,
			headers,
			receivablesApprovedPagination,
			onClose,
			onClickExport,
			onReload,
			onRequest,
			onStatusOpenReceivablesApproved
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';

@include stickytable('.table-deny-reasons', 1, (0));
</style>
