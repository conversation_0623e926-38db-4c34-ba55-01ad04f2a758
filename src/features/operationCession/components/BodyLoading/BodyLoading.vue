<template>
	<farm-box>
		<farm-container v-if="!isErrorCreateOperation">
			<farm-row justify="center">
				<farm-heading color="primary" type="3">
					Estamos validando seus recebíveis!
				</farm-heading>
			</farm-row>
			<farm-row justify="center">
				<farm-heading type="6"><PERSON>so pode levar alguns minutos.</farm-heading>
			</farm-row>
			<farm-row justify="center">
				<lottie :height="400" :options="animationDefaultOptions" :width="400" />
			</farm-row>
			<farm-row justify="center">
				<farm-heading color="primary" type="6">
					{{ processedReceivablesPercentage }}%
				</farm-heading>
			</farm-row>
			<farm-row justify="center">
				<farm-col class="my-6" cols="6">
					<farm-progressbar
						:value="processedReceivablesPercentage"
						backgroundColor="gray"
						valueColor="primary"
					/>
				</farm-col>
			</farm-row>
		</farm-container>
	</farm-box>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref, watch } from 'vue';

import animationData from '@/assets/loader.json';
import { useGetter, useStore } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';
import useRoute from '@/composible/useRoute';

import { useCreateOperation } from './composables/useCreateOperation';
import { useRedirect } from './composables/useRedirect';


export default defineComponent({
	name: 'BodyLoading',
	setup() {
		const store = useStore();
		const route = useRoute();
		const { trigger } = useAnalytics();

		const { redirectErrorSimulation, redirectSummary } = useRedirect();
		const { createOperation, isErrorCreateOperation } = useCreateOperation();

		const id_pre_section = computed(() => route.query.id_pre_section || null);
		const processedReceivablesPercentage = ref(0);

		const id = ref(0);

		const selectedInvoices = computed(useGetter('operationCession', 'selectedInvoices'));
		const productCache = computed(
			useGetter('operationCession', 'operationCessionProductCache')
		);

		const operationCessionBodyLoadingPayload = reactive({
			preSection: {
				id: null,
				financialVehicle: null,
				currentStep: null,
				dateDisbursement: null,
				commercialProdName: null,
				sectionType: null,
				valueDisbursement: null,
				valueNominal: null,
				valueAvailable: null,
				totalLiquidValue: null,
				totalFreeValue: null,
			},
			receivablesBank: [],
		});

		const animationDefaultOptions = {
			animationData: animationData,
		};

		function createPayload() {
			operationCessionBodyLoadingPayload.preSection.id = id_pre_section?.value;
			operationCessionBodyLoadingPayload.preSection.financialVehicle =
				productCache.value.idProduct;
			operationCessionBodyLoadingPayload.preSection.currentStep = 3;
			operationCessionBodyLoadingPayload.preSection.dateDisbursement =
				productCache.value.dateDisbursement;
			operationCessionBodyLoadingPayload.preSection.commercialProdName =
				productCache.value.nameProduct;
			operationCessionBodyLoadingPayload.preSection.sectionType =
				productCache.value.idOperation;
			operationCessionBodyLoadingPayload.preSection.valueDisbursement =
				selectedInvoices.value.valueDisbursement;
			operationCessionBodyLoadingPayload.preSection.valueNominal =
				selectedInvoices.value.sumNominalValue;
			operationCessionBodyLoadingPayload.preSection.valueAvailable =
				productCache.value.availableLimit;
			operationCessionBodyLoadingPayload.preSection.totalLiquidValue =
				selectedInvoices.value.valueDisbursement;
			operationCessionBodyLoadingPayload.preSection.totalFreeValue =
				selectedInvoices.value.sumFreeValue;
			const data = selectedInvoices.value.invoices.map((item) => {
				return {
					id: item.id,
					liquidValue: item.netValue,
					freeValue: item.freeValue,
				};
			});
			operationCessionBodyLoadingPayload.receivablesBank = data;
			operationCessionBodyLoadingPayload.preSection.minValue = selectedInvoices.value.minValue;
			operationCessionBodyLoadingPayload.preSection.maxValue = selectedInvoices.value.maxValue;
			return operationCessionBodyLoadingPayload;
		}

		function addInformationEligibility(id) {
			store.dispatch('operationCession/addInformationEligibility', {
				data: id,
			});
		}

		function startAnimation(): void {
			let intervalId = setInterval(() => {
				if (processedReceivablesPercentage.value < 98) {
					processedReceivablesPercentage.value += 1;
				} else {
					clearInterval(intervalId);
				}
			}, 100);
		}

		function callCreateOperation(data, error): void {
			if (error) {
				const dataTrigger = {
					event: 'operation_cession',
					payload:{
						step: 'loading',
						description:'erro ao criar a operação',
					}
				};
				trigger(dataTrigger);
				redirectErrorSimulation();
				return;
			}
			id.value = data.id;
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					step: 'loading',
					description:'operação criada',
					data:{
						...data
					}
				}
			};
			trigger(dataTrigger);
			addInformationEligibility(data.id);
		}

		function load(): void {
			startAnimation();
			const payload = createPayload();
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					step: 'loading',
					description:'enviando dados para a simulação',
					data:{
						...payload
					}
				}
			};
			trigger(dataTrigger);
			createOperation(payload, callCreateOperation);
		}

		watch(id, () => {
			processedReceivablesPercentage.value = 100;
			redirectSummary(id.value);
		});

		onMounted(() => {
			load();
		});

		return {
			animationDefaultOptions,
			processedReceivablesPercentage,
			isErrorCreateOperation,
		};
	},
});
</script>
