import useRouter from '@/composible/useRouter';

type UseRedirect = {
	redirectErrorSimulation: Function;
	redirectErrorCreateCession: Function;
	redirectSummary: Function
};

const baseUrl = "/admin/wallet/cessoes";

export function useRedirect(): UseRedirect {
	const router = useRouter();

	function redirectErrorSimulation(): void {
		router.push(`${baseUrl}/erro_ao_simular_a_cessao`);
	}

	function redirectErrorCreateCession(): void {
		router.push(`${baseUrl}/criacao_da_cessao_error`);
	}

	function redirectSummary(operationId: number): void {
		router.push(`${baseUrl}//resumo_da_cessao/${operationId}`);
	}

	return {
		redirectErrorSimulation,
		redirectErrorCreateCession,
		redirectSummary,
	};
}
