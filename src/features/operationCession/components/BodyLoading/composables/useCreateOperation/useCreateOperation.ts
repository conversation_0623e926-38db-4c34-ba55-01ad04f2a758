import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';


import { useSelectedProductId } from '@/composible';
import {
	uploadEligibilitySimulation as uploadEligibilitySimulationService
} from '@/features/operationCession/services';

type UseCreateOperation = {
	isLoadingCreateOperation: {
		value: boolean
	};
	isErrorCreateOperation: {
		value: boolean
	};
	createOperation: Function;
};

export function useCreateOperation(): UseCreateOperation {
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({payload, productId}) => uploadEligibilitySimulationService({payload, productId}),
		onSuccess: response => {
			if(callFunc !== null){
				callFunc(response.data.data.content[0], false);
			}
		},
		onError: () => {
			if(callFunc !== null) {
				setTimeout(() => {
					callFunc(null, true);
				}, 1500);
			}
		},
	});

	const isLoadingCreateOperation = computed(() => {
		return isLoading.value;
	});

	const isErrorCreateOperation = computed(() => {
		return isError.value;
	});

	function createNotification(): void {
		notification(RequestStatusEnum.ERROR, 'Erro ao criar a Operação por favor tente novamente !');
	}

	function createOperation(payload, callback: Function): void {
		mutate({ payload, productId });
		callFunc = callback;
	}

	return {
		isLoadingCreateOperation,
		isErrorCreateOperation,
		createOperation,
	};
}
