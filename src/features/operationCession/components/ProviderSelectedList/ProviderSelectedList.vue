<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<provider-selected-card
					:data="item"
					:checked="verifyisChecked(item.id)"
					@onChangeValue="onChangeValue"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { onMounted } from 'vue';
import { defineComponent, toRefs, computed, ref, watch } from 'vue';

import ProviderSelectedCard from '../ProviderSelectedCard';

export default defineComponent({
	name: 'provider-selected-list',
	components: {
		ProviderSelectedCard,
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
		providerListId: {
			type: Array,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data, providerListId } = toRefs(props);
		const selected = ref(0);
		const isDataEmpty = computed(() => props.data.length === 0);
		const listOfSelected = ref([]);

		function addOrRemoveNumber(listOfNumbers: Array<Number>, currentNumber: number) {
			const index = listOfNumbers.indexOf(currentNumber);
			if (index === -1) {
				listOfNumbers.push(currentNumber);
			} else {
				listOfNumbers.splice(index, 1);
			}

			return listOfNumbers;
		}

		function onChangeValue(id: number): void {
			listOfSelected.value = addOrRemoveNumber(listOfSelected.value, id);
		}

		function verifyisChecked(id) {
			return listOfSelected.value.includes(id);
		}

		watch(
			listOfSelected,
			newValue => {
				emit('onUpdateProviderClicked', newValue);
			},
			{
				deep: true,
			}
		);

		onMounted(() => {
			listOfSelected.value = [...listOfSelected.value, ...providerListId.value];
		});

		return {
			data,
			isDataEmpty,
			selected,
			onChangeValue,
			verifyisChecked,
		};
	},
});
</script>
