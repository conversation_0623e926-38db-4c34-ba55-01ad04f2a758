<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="88" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Recusados" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-row v-if="!isError">
				<farm-col cols="12" class="mb-4">
					<farm-caption variation="regular">
						Visualize aqui seus recebíveis recusados para essa cessão.
					</farm-caption>
				</farm-col>
			</farm-row>
			<farm-row v-if="!isError">
				<farm-col cols="12" class="mt-6">
					<collapsible-cession-deny-reasons
						isOpen
						:notApproved="count"
						:data="denyReasonV2"
						:pagination="denyReasonPagination"
						:disabledButton="count === 0"
						@onRequest="onRequest"
						@onClickDownload="onClickExport"
					/>
				</farm-col>
			</farm-row>
			<farm-loader mode="overlay" v-if="isLoading" />
			<div v-if="isError" class="my-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer confirmLabel="Fechar" :hasCancel="false" @onConfirm="onClose" />
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, onMounted, computed } from 'vue';

import CollapsibleCessionDenyReasons from '@/components/CollapsibleCessionDenyReasons';
import { useAnalytics } from '@/composible/useAnalytics';

import { useDenyReasonsV2 } from '../../composables/useDenyReasonsV2';
import { useExportExcel } from '../../composables/useExportExcel';

export default defineComponent({
	name: 'modal-deny-reasons',
	components: {
		CollapsibleCessionDenyReasons,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		cessionCurrent: {
			type: Object,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { cessionCurrent } = toRefs(props);

		const { trigger } = useAnalytics();
		const {
			denyReasonV2,
			denyReasonPagination,
			getDenyReasonV2,
			isErrorDenyReason,
			isLoadingDenyReason
		} = useDenyReasonsV2();
		const { onDownloadExcel } = useExportExcel();

		const count = ref(cessionCurrent.value.refused);

		const isLoading = computed(() => {
			return(
				isLoadingDenyReason.value
			);
		});
		const isError = computed(() => {
			return(
				isErrorDenyReason.value
			);
		});

		function onClose(): void {
			emit('onCloseModal');
		}

		function onClickExport(): void {
			const id = cessionCurrent.value.id;
			const status = 1;
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					description:'clicou no botão exportar motivos de recusa'
				}
			};
			trigger(dataTrigger);
			onDownloadExcel({ id, status });
		}

		function load(): void {
			const id = cessionCurrent.value.id;
			const params ={
				page: 0,
				limit: 10,
				'id_status':1
			};
			getDenyReasonV2(id, params);
		}

		function onRequest(data): void {
			const id = cessionCurrent.value.id;
			const params ={
				...data,
				'id_status':1
			};
			getDenyReasonV2(id, params);
		}

		function onStatusOpenDenyReasons(data: boolean): void {
			const action = data ? 'expandir': 'esconder';
			const dataTrigger = {
				event: 'operation_cession',
				payload:{
					description:`clicou para ${action} os motivos de recusa`
				}
			};
			trigger(dataTrigger);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			isLoading,
			isError,
			denyReasonV2,
			count,
			denyReasonPagination,
			onClose,
			onClickExport,
			onReload,
			onStatusOpenDenyReasons,
			onRequest
		};
	},
});
</script>
