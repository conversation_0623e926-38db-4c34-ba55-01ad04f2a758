<template>
	<farm-modal v-model="value" :offsetBottom="64" :offsetTop="48" size="sm">
		<template v-slot:header>
			<farm-dialog-header title="Remover Fornecedor" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-typography class="mb-2" size="md">
				Deseja remover o fornecedor <b>{{ providerCurrent.corporateName }}</b> da cessão?
			</farm-typography>
			<farm-typography class="mb-6" size="md">
				Clique em <b>“Sim”</b> para remover o fornecedor ou em <b>“Cancelar”</b> para mantê-lo na cessão.
			</farm-typography>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				closeLabel="Cancelar"
				confirmLabel="Sim"
				@onClose="onClose"
				@onConfirm="onConfirm"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
	name:"provider-remove-modal",
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		providerCurrent: {
			type: Object,
			required: true,
		},
	},
	setup(props,{ emit }) {

		const { providerCurrent } = toRefs(props);

		function onClose(): void {
			emit('onClose');
		}

		function onConfirm(): void {
			emit('onConfirm');
		}

		return {
			providerCurrent,
			onClose,
			onConfirm
		};
	},
});
</script>
