<template>
	<farm-container>
		<header-home-cession
			v-if="!isError && totals !== null"
			:totals="totals"
			:disabledBtn="isOperationEnabled"
		/>
		<farm-row class="mb-4" v-if="isOperationEnabled">
			<farm-col>
				<farm-alertbox color="neutral" icon="alert-circle-outline" dismissable
					>Você não possui produtos habilitados para criar novas cessões.
				</farm-alertbox>
			</farm-col>
		</farm-row>
		<farm-row justify="space-between" v-if="!isError">
			<farm-col cols="12" md="6">
				<div class="d-flex align-center justify-center">
					<farm-form-mainfilter
						label="Buscar Cessão"
						:showFilters="isOpenFilter"
						@onInputChange="onInputChangeMainFilter"
						@onClick="onClickMainFilter"
					>
						<div class="d-flex">
							<farm-label class="mb-0 mr-2">Buscar Cessão</farm-label>
							<label-request-results
								v-if="isFilterCounter"
								:totalItems="pagination.totalElements || 0"
							/>
						</div>
					</farm-form-mainfilter>
				</div>
			</farm-col>
			<farm-col cols="12" md="4" align="right">
				<farm-select
					class="mt-8"
					id="form-filtro-status"
					v-model="sortModel"
					item-text="label"
					item-value="value"
					:items="cessionSort"
					@change="onSortSelect"
				/>
			</farm-col>
		</farm-row>
		<collapse-transition :duration="300">
			<home-filter
				v-show="isOpenFilter && !isError"
				:operationType="operationType"
				:product="productFilter"
				:filterCurrent="filterCurrent"
				@onFiltersApplied="onFiltersApplied"
				@onApply="onApplyFilter"
			/>
		</collapse-transition>
		<cession-list
			v-if="!isError"
			:data="cessions"
			@onOpenModalDenyReasons="onOpenModalDenyReasons"
			@onOpenModalReceivablesApproved="onOpenModalReceivablesApproved"
			@onRedirect="onRedirect"
		/>
		<farm-row extra-decrease v-if="!isError">
			<farm-box>
				<farm-datatable-paginator
					v-if="cessions.length > 0"
					class="mt-6 mb-n6"
					:page="page"
					:totalPages="pagination.totalPages"
					:initialLimitPerPage="filterCurrent.limit"
					@onChangePage="onChangePage"
					@onChangeLimitPerPage="onChangePageLimit"
				/>
			</farm-box>
		</farm-row>
		<modal-deny-reasons
			v-if="isModalDenyReasons"
			v-model="isModalDenyReasons"
			:cessionCurrent="cessionCurrent"
			@onCloseModal="onCloseModalDenyReasons"
		/>
		<modal-receivables-approved
			v-if="isModalReceivablesApproved"
			v-model="isModalReceivablesApproved"
			:cessionCurrent="cessionCurrent"
			@onCloseModal="onCloseModalReceivablesApproved"
		/>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch, computed } from 'vue';

import LabelRequestResults from '@/components/LabelRequestResults';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';
import { usePageable } from '@/composible/usePageable';

import { headers } from '../../configurations/headers';
import { cessionSort } from '../../configurations/sort';
import CessionList from '../CessionList';
import HeaderHomeCession from '../HeaderHomeCession';
import HomeFilter from '../HomeFilter';
import ModalDenyReasons from '../ModalDenyReasons';
import ModalReceivablesApproved from '../ModalReceivablesApproved';

import { useEnableOperation } from './composables/useEnableOperation';
import { useOperationCession } from './composables/useOperationCession';
import { useOperationCessionTotals } from './composables/useOperationCessionTotals';
import { useOperationTypeFilter } from './composables/useOperationTypeFilter';
import { useProductFilter } from './composables/useProductFilter';
import { useRedirect } from './composables/useRedirect';

export default defineComponent({
	name: 'body-home',
	components: {
		HeaderHomeCession,
		LabelRequestResults,
		HomeFilter,
		CessionList,
		ModalDenyReasons,
		ModalReceivablesApproved,
	},
	setup() {
		const {
			cessions,
			cessionPagination,
			getOperationCession,
			isErrorOperationCession,
			isLoadingOperationCession,
		} = useOperationCession();
		const { totals, getOperationCessionTotals } = useOperationCessionTotals();
		const { getOperationStatus, isLoadingOperationEnabled, isOperationEnabled } =
			useEnableOperation();
		const { productFilter, getProductFilter, isLoadingProductFilter, isErrorProductFilter } =
			useProductFilter();
		const {
			getOperationTypeFilter,
			isErrorOperationTypeFilter,
			isLoadingOperationTypeFilter,
			operationType,
		} = useOperationTypeFilter();
		const {
			page,
			pagination,
			isOpenFilter,
			isFilterCounter,
			onApplyFilter,
			onChangePage,
			onChangePageLimit,
			onClickMainFilter,
			onInputChangeMainFilter,
			onSortSelect,
			onFiltersApplied,
		} = usePageable(
			{
				calbackFn: params => {
					filterCurrent.value = {
						...params,
						showCancelled: params.showCancelled || false
					};
					getOperationCession(filterCurrent.value);
				},
				filters: {
					showCancelled: false
				},
				keyInputSearch: 'id_pre_section',
				sort: {
					order: 'DESC',
					orderby: 'create_at',
				},
				charInputSearch: 1,
				lowercaseSort: true,
			},
			cessionPagination
		);
		const {
			isOpenModal: isModalDenyReasons,
			onCloseModal: onCloseModalDenyReasons,
			onOpenModal: openModalDenyReasons,
		} = useModal();
		const {
			isOpenModal: isModalReceivablesApproved,
			onCloseModal: onCloseModalReceivablesApproved,
			onOpenModal: openModalReceivablesApproved,
		} = useModal();
		const { redirect } = useRedirect();
		const { trigger } = useAnalytics();

		const sortModel = ref('createdAt_DESC');
		const cessionCurrent = ref(null);
		const filterCurrent = ref({
			page: 0,
			limit: 10,
			order: 'DESC',
			orderby: 'create_at',
			showCancelled: false,
		});

		const isLoading = computed(() => {
			return (
				isLoadingOperationEnabled.value ||
				isLoadingOperationCession.value ||
				isLoadingProductFilter.value ||
				isLoadingOperationTypeFilter.value
			);
		});
		const isError = computed(() => {
			return (
				isErrorOperationCession.value ||
				isErrorProductFilter.value ||
				isErrorOperationTypeFilter.value
			);
		});

		function onOpenModalDenyReasons(data): void {
			cessionCurrent.value = data;
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					idCession: data.id,
					description: 'clicou na opção de reprovado',
				},
			};
			trigger(dataTrigger);
			openModalDenyReasons();
		}

		function onOpenModalReceivablesApproved(data): void {
			cessionCurrent.value = data;
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					idCession: data.id,
					description: 'clicou na opção de aprovados',
				},
			};
			trigger(dataTrigger);
			openModalReceivablesApproved();
		}

		function onRedirect(item): void {
			const steps = {
				3: 2,
				4: 3,
				5: 4,
				6: 5,
				7: 2,
			};

			if (item.sectionStatus === 1 || item.sectionStatus === 8) {
				if (item.canceledByUser) {
					redirect(steps[item.lastStatus], item.id);
					return;
				}

				if (item.currentStep === 3) {
					redirect(0, item.id);
					return;
				}
				redirect(1, item.id);
				return;
			}

			if (item.sectionStatus === 7 && item.lastStatus !== null) {
				redirect(steps[item.lastStatus], item.id);
				return;
			}

			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					idCession: item.id,
					description: 'clicou para ver de detalhe da cessão',
				},
			};
			trigger(dataTrigger);
			redirect(steps[item.sectionStatus], item.id);
		}

		function load(): void {
			getProductFilter();
			getOperationTypeFilter();
			getOperationStatus();
			getOperationCessionTotals();
			const params = {
				page: 0,
				limit: 10,
				order: 'DESC',
				orderby: 'create_at',
				showCancelled: false,
			};
			getOperationCession(params);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		watch(isOpenFilter, newValue => {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					description: 'clicou na opção de esconder filtro',
				},
			};
			if (newValue) {
				dataTrigger.payload.description = 'clicou na opção de ver filtro';
				trigger(dataTrigger);
				return;
			}
			trigger(dataTrigger);
		});

		return {
			page,
			pagination,
			isLoading,
			isError,
			totals,
			cessions,
			cessionCurrent,
			sortModel,
			cessionSort,
			headers,
			productFilter,
			operationType,
			filterCurrent,
			isModalDenyReasons,
			isModalReceivablesApproved,
			isOpenFilter,
			isFilterCounter,
			isOperationEnabled,
			onCloseModalDenyReasons,
			onCloseModalReceivablesApproved,
			onChangePage,
			onChangePageLimit,
			onReload,
			onClickMainFilter,
			onInputChangeMainFilter,
			onApplyFilter,
			onSortSelect,
			onFiltersApplied,
			onOpenModalDenyReasons,
			onOpenModalReceivablesApproved,
			onRedirect,
		};
	},
});
</script>
