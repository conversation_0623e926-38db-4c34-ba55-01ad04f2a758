import { computed, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { useSelectedProductId } from '@/composible';

import { builderProductFilter } from '../../../../helpers/builderProductFilter';
import { getProductFilter as getProductFilterService } from '../../../../services';

type UseProductFilter = {
	productFilter: Array<any>;
	getProductFilter: Function;
	isLoadingProductFilter: ComputedRef<boolean>;
	isErrorProductFilter: ComputedRef<boolean>;
};

export function useProductFilter(): UseProductFilter {

	const productFilter = ref([]);

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getProductFilterService(params),
		onSuccess: (response) => {
			productFilter.value = builderProductFilter(response.data);
		},
		onError: () => {
			productFilter.value = [];
		},
	});

	const isLoadingProductFilter = computed(() => {
		return isLoading.value;
	});

	const isErrorProductFilter = computed(() => {
		return isError.value;
	});

	function getProductFilter(step: number, id: number): void {
		mutate({ productId });
	}

	return {
		productFilter,
		isLoadingProductFilter,
		isErrorProductFilter,
		getProductFilter,
	};
}
