import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	builderOperationCessionTotals
 } from '@/features/operationCession/helpers/builderOperationCessionTotals';
import {
	getOperationCessionTotals as getOperationCessionTotalsService
} from '@/features/operationCession/services';
import { TotalOperationCession } from '@/features/operationCession/types';

type UseOperationCessionTotals = {
	totals: TotalOperationCession;
	isLoadingOperationCessionTotals: boolean;
	isErrorOperationCessionTotals: boolean;
	getOperationCessionTotals: Function;
}

export function useOperationCessionTotals(): UseOperationCessionTotals {

	const productId = useSelectedProductId().value;

	const totals = ref(null);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getOperationCessionTotalsService(params),
		onSuccess: (data) => {
			totals.value = builderOperationCessionTotals(data.data);
		},
	});

	const isLoadingOperationCessionTotals = computed(() => {
		return isLoading.value;
	});

	const isErrorOperationCessionTotals = computed(() => {
		return isError.value;
	});

	function getOperationCessionTotals(): void {
		mutate({ productId });
	}

	return {
		totals,
		isLoadingOperationCessionTotals,
		isErrorOperationCessionTotals,
		getOperationCessionTotals
	};
}
