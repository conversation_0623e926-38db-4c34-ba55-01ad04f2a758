import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { useSelectedProductId } from '@/composible';
import { builderOperationCession } from '@/features/operationCession/helpers/builderOperationCession';
import { getOperationCession as getOperationCessionService } from '@/features/operationCession/services';
import { OperationCession } from '@/features/operationCession/types';

type UseOperationCession = {
	cessionPagination: any;
	cessions: Array<OperationCession>;
	isLoadingOperationCession: ComputedRef<boolean>;
	isErrorOperationCession: ComputedRef<boolean>;
	getOperationCession: Function;
};

export function useOperationCession(): UseOperationCession {
	const cessions = ref([]);
	const cessionPagination = ref(null);

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getOperationCessionService(params),
		onSuccess: data => {
			const { content, pagination } = builderOperationCession(data.data);
			cessions.value = content;
			cessionPagination.value = pagination;
		},
	});

	const isLoadingOperationCession = computed(() => {
		return isLoading.value;
	});

	const isErrorOperationCession = computed(() => {
		return isError.value;
	});

	function getOperationCession(filters): void {
		const params = queryString(filters, {});
		mutate({ productId, filters: params });
	}

	return {
		cessionPagination,
		cessions,
		isLoadingOperationCession,
		isErrorOperationCession,
		getOperationCession,
	};
}
