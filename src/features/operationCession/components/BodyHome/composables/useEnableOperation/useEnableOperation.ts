import { ref, computed } from 'vue';

import { useMutation } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { useSelectedProductId } from '@/composible';
import { getOperationStatus as getOperationStatusService } from '@/features/operationCession/services';

type UseEnableOperation = {
	isOperationEnabled: ComputedRef<boolean>;
	isLoadingOperationEnabled: ComputedRef<boolean>;
	getOperationStatus: () => void;
};

export function useEnableOperation(): UseEnableOperation {
	const productId = useSelectedProductId().value;

	const isOperationEnabled = ref(true);

	const { isLoading, mutate } = useMutation({
		mutationFn: params => getOperationStatusService(params),
		onSuccess: response => {
			isOperationEnabled.value = response.data.enableOperation;
		},
		onError: () => {
			isOperationEnabled.value = false;
		},
	});

	const isLoadingOperationEnabled = computed(() => {
		return isLoading.value;
	});

	function getOperationStatus(): void {
		mutate({ productId });
	}

	return {
		isOperationEnabled,
		isLoadingOperationEnabled,
		getOperationStatus,
	};
}
