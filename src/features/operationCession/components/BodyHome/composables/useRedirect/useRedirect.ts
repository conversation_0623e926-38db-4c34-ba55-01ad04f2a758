import useRouter from '@/composible/useRouter';

type UseRedirect ={
	redirect: Function;
};

const routes = [
	'/admin/wallet/cessoes/resumo_da_cessao',
	'/admin/wallet/cessoes/pagamento_do_fornecedor',
	'/admin/wallet/detalhe_cessao/analisando_cessao',
	'/admin/wallet/detalhe_cessao/aguardando_formalizacao',
	'/admin/wallet/detalhe_cessao/cessao_em_desembolso',
	'/admin/wallet/detalhe_cessao/cessao_concluida',
];

export function useRedirect(): UseRedirect {
	const router = useRouter();

	function redirect(step: number, id: number): void {
		router.push(`${routes[step]}/${id}`);
	}

	return {
		redirect,
	};
}
