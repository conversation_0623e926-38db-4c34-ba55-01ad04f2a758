import { computed, ref } from 'vue';

import { useMutation } from '@tanstack/vue-query';
import type { ComputedRef } from 'vue/types';

import { useSelectedProductId } from '@/composible';

import { builderOperationTypeFilter } from '../../../../helpers/builderOperationTypeFilter';
import { getTypeOperationFilter as getTypeOperationFilterService } from '../../../../services';

type UseOperationTypeFilter = {
	operationType: Array<any>;
	getOperationTypeFilter: Function;
	isLoadingOperationTypeFilter: ComputedRef<boolean>;
	isErrorOperationTypeFilter: ComputedRef<boolean>;
};

export function useOperationTypeFilter(): UseOperationTypeFilter {

	const operationType = ref([]);

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getTypeOperationFilterService(params),
		onSuccess: (response) => {
			operationType.value = builderOperationTypeFilter(response.data);
		},
		onError: () => {
			operationType.value = [];
		},
	});

	const isLoadingOperationTypeFilter = computed(() => {
		return isLoading.value;
	});

	const isErrorOperationTypeFilter = computed(() => {
		return isError.value;
	});

	function getOperationTypeFilter(step: number, id: number): void {
		mutate({ productId });
	}

	return {
		operationType,
		isLoadingOperationTypeFilter,
		isErrorOperationTypeFilter,
		getOperationTypeFilter,
	};
}
