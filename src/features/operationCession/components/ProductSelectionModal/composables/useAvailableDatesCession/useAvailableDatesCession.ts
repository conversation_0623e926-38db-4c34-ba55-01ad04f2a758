import { ref, computed } from "vue";

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import { builderAvailableDatesCession } from '@/features/operationCession/helpers/builderAvailableDatesCession';
import { getAvailableDatesCession as getAvailableDatesCessionService } from '@/features/operationCession/services';


type UseAvailableDatesCession = {
	availableDates: Array<string>;
	isLoadingAvailableDates: boolean;
	isErrorAvailableDates: boolean;
	getAvailableDatesCession: Function;
}

export function useAvailableDatesCession(): UseAvailableDatesCession {
	let callFunc: Function | null = null;
	const availableDates = ref([]);

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => getAvailableDatesCessionService(params),
		onSuccess: (response) => {
			availableDates.value = builderAvailableDatesCession(response.data);
			if(callFunc !== null){
				callFunc(response.data.data, false);
			}
		},
	});

	const isLoadingAvailableDates = computed(() => {
		return isLoading.value;
	});

	const isErrorAvailableDates = computed(() => {
		return isError.value;
	});

	function getAvailableDatesCession(callback: Function): void {
		mutate({productId});
		callFunc = callback;
	}

	return {
		availableDates,
		isLoadingAvailableDates,
		isErrorAvailableDates,
		getAvailableDatesCession
	};
}

