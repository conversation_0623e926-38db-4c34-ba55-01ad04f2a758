<template>
	<farm-modal
		v-model="value"
		:offsetTop="48"
		:offsetBottom="76"
		:persistent="true"
		size="sm"
		@onClose="onCloseModal"
	>
		<template v-slot:header>
			<farm-dialog-header title="Valor e Data" @onClose="onCloseModal" />
		</template>
		<template v-slot:content>
			<div class="product-modal-content">
				<div class="product-modal-main">
					<div class="product-modal-content mt-2">
						<farm-caption>
							Defina o valor e a data desejados para a seleção dos recebíveis.
						</farm-caption>
					</div>
					<div class="product-modal-content-limit mt-4">
						<farm-alertbox color="info">
							<farm-bodytext
								variation="medium"
								color="info"
								color-variation="darken"
								:types="1"
							>
								Limite Disponível: {{ availableLimit }}
							</farm-bodytext>
						</farm-alertbox>
					</div>
					<div class="product-modal-content-checkbox mt-4">
						<farm-radio v-model="radioModel" value="1" size="md" />
						<farm-label class="mb-0 ml-2">
							Selecione Todos Recebíveis Disponíveis
							<farm-tooltip>
								<farm-caption variation="semiBold" color="white">
									Selecione Todos Recebíveis Disponíveis
								</farm-caption>
								<farm-caption variation="regular" color="white">
									Use todos os recebíveis disponíveis para realizar sua cessão dentro
									do Limite Disponível.
								</farm-caption>
								<template v-slot:activator>
									<farm-icon color="gray" size="sm">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-label>
					</div>
					<div class="product-modal-content-checkbox mt-4">
						<farm-radio v-model="radioModel" value="2" size="md" />
						<farm-label class="mb-0 ml-2">
							Valor de Desembolso Desejado
							<farm-tooltip>
								<farm-caption variation="semiBold" color="white">
									Valor de Desembolso Desejado
								</farm-caption>
								<farm-caption variation="regular" color="white">
									O desembolso desejado leva em consideração o valor liquido de cada
									recebível selecionado.
								</farm-caption>
								<template v-slot:activator>
									<farm-icon color="gray" size="sm">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-label>
					</div>
					<farm-textfield-v2
						class="mt-4"
						ref="input"
						v-model="inputModel"
						:disabled="disabledInput"
						:mask="mask"
						:rules="[rules.checkMinValue, rules.checkMaxValue]"
						@change="onChangeInput"
					/>
					<div class="product-modal-content-checkbox">
						<farm-label required class="mb-0 ml-2">
							Data do Desembolso
							<farm-tooltip>
								<farm-caption variation="semiBold" color="white">
									Data do Desembolso
								</farm-caption>
								<farm-caption variation="regular" color="white">
									Data prevista para o desembolso. Para datas de desembolso em D+1
									será considerado horário até as {{ hour }}hs.
								</farm-caption>
								<template v-slot:activator>
									<farm-icon color="gray" size="sm">help-circle</farm-icon>
								</template>
							</farm-tooltip>
						</farm-label>
					</div>
					<farm-select
						id="form-filter-status"
						v-model="selectModel"
						:items="availableDates"
						item-text="label"
						item-value="id"
						@change="onChangeSelect"
					/>
				</div>
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				close-label="Cancelar"
				confirmLabel="Continuar"
				:isConfirmDisabled="disabledButton"
				@onClose="onCloseModal"
				@onConfirm="onConfirmButton"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, toRefs, watch } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import { useAnalytics } from '@/composible/useAnalytics';
import useRouter from '@/composible/useRouter';
import { useProductCache } from '@/features/operationCession/composables/useProductCache';
import { currency as currencyMask, currencyUnmask, formatNumberToCurrent } from '@/helpers/masks';

import { useAvailableDatesCession } from './composables/useAvailableDatesCession';

export default defineComponent({
	name: 'product-selection-modal',
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		productCurrent: {
			type: Object,
			required: true,
		},
		noRedirect: {
			type: Boolean,
			default: false,
		},
		vehicleId: {
			type: String,
			default: null,
		},
		operationId: {
			type: String,
			default: null,
		},
	},
	setup(props, { emit }) {
		const {
			availableDates,
			getAvailableDatesCession,
			isErrorAvailableDates,
			isLoadingAvailableDates,
		} = useAvailableDatesCession();

		const router = useRouter();
		const { addDataCache, operationCessionProductCache } = useProductCache();
		const { trigger } = useAnalytics();

		const { noRedirect } = toRefs(props);
		const availableLimit = ref(brl(props.productCurrent.availableLimit));
		const selectModel = ref('');
		const inputModel = ref('');
		const mask = ref(currencyMask);
		const radioModel = ref('1');
		const disabledInput = ref(true);
		const disabledButton = ref(true);
		const hour = ref(0);
		const validForm = ref([true, false]);

		const rules = computed(() => {
			return {
				checkMinValue: value => {
					const messageError = `O valor mínimo para o desembolso é a partir de ${formatNumberToCurrent(
						props.productCurrent.minValue
					)}`;
					return validateMinValue(value) || messageError;
				},
				lessOrEqual: value => {
					const messageError = 'Você excedeu o limite disponível';
					return validateValue(value) || messageError;
				},
				checkMaxValue: value => {
					const messageError = `O valor máximo para o desembolso neste produto é de ${brl(
						props.productCurrent.maxValue
					)}`;
					return validateMaxValue(value) || messageError;
				},
			};
		});

		function validateValue(inputtedNetValue: string): boolean {
			if (inputtedNetValue.length === 0) {
				return true;
			}
			if (!disabledInput.value) {
				return currencyUnmask(inputtedNetValue) <= currencyUnmask(availableLimit.value);
			}
			return false;
		}

		function validateMinValue(inputtedNetValue: string): boolean {
			if (inputtedNetValue.length === 0) {
				return true;
			}
			if (!disabledInput.value) {
				return (
					parseFloat(props.productCurrent.minValue) <= currencyUnmask(inputtedNetValue)
				);
			}
			return false;
		}

		function validateMaxValue(inputtedNetValue: string): boolean {
			if (inputtedNetValue.length === 0) {
				return true;
			}
			if (!disabledInput.value) {
				return (
					parseFloat(props.productCurrent.minValue) <= currencyUnmask(inputtedNetValue) &&
					currencyUnmask(inputtedNetValue) <= parseFloat(props.productCurrent.maxValue)
				);
			}
			return false;
		}

		function onConfirmButton(): void {
			const value = props.productCurrent.availableLimit;
			let idOperation = props.productCurrent.operationId;
			let nameOperation = props.productCurrent.operationName;
			if (noRedirect.value) {
				idOperation = operationCessionProductCache.value.idOperation;
				nameOperation = operationCessionProductCache.value.nameOperation;
			}

			const data = {
				idOperation,
				nameOperation,
				idProduct: props.productCurrent.id,
				nameProduct: props.productCurrent.name,
				availableLimit: value,
				dateDisbursement: selectModel.value,
				valueSeleted:
					parseInt(radioModel.value, 10) === 1 ? value : currencyUnmask(inputModel.value),
				minValue: props.productCurrent.minValue,
				maxValue: props.productCurrent.maxValue,
			};
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: !noRedirect.value ? 1 : 2,
					description: 'clicou na opção de continuar modal valor e data',
					data: {
						...data,
					},
				},
			};
			trigger(dataTrigger);
			addDataCache(data);
			emit('onSetNewValue', data);
			const url = '/admin/wallet/cessoes/escolha_de_recebiveis';
			if (!noRedirect.value) {
				router.push(url);
			} else {
				emit('onCloseModal');
			}
		}

		function onChangeSelect(value): void {
			if (value !== '') {
				validForm.value = [validForm.value[0], true];
				return;
			}
			validForm.value = [validForm.value[0], false];
		}

		function onChangeInput(value): void {
			if (value.length <= 2) {
				validForm.value = [false, validForm.value[1]];
				return;
			}
			if (
				currencyUnmask(value) <= currencyUnmask(availableLimit.value) &&
				currencyUnmask(value) >= parseFloat(props.productCurrent.minValue) &&
				currencyUnmask(value) <=  parseFloat(props.productCurrent.maxValue)
			) {
				validForm.value = [true, validForm.value[1]];
				return;
			}
			validForm.value = [false, validForm.value[1]];
		}

		function onCloseModal(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: !noRedirect.value ? 1 : 2,
					description: 'clicou na opção de cancelar modal valor e data',
				},
			};
			trigger(dataTrigger);
			emit('onCloseModal');
		}

		function updatedHour(data): void {
			hour.value = data.hour;
		}

		function load(): void {
			getAvailableDatesCession(updatedHour);
		}

		function onReload(): void {
			load();
		}

		watch(radioModel, newValue => {
			if (parseInt(newValue, 10) === 2) {
				validForm.value = [false, validForm.value[1]];
				disabledInput.value = false;
				return;
			}
			validForm.value = [true, validForm.value[1]];
			inputModel.value = '';
			disabledInput.value = true;
		});

		watch(validForm, newValue => {
			const newValid = newValue.filter(item => item === true);
			if (newValid.length === 2) {
				disabledButton.value = false;
				return;
			}
			disabledButton.value = true;
		});

		onMounted(() => {
			load();
		});

		return {
			isError: isErrorAvailableDates,
			isLoading: isLoadingAvailableDates,
			rules,
			inputModel,
			mask,
			hour,
			radioModel,
			selectModel,
			availableDates,
			disabledInput,
			disabledButton,
			availableLimit,
			onCloseModal,
			onReload,
			onConfirmButton,
			onChangeSelect,
			onChangeInput,
			validForm,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './ProductSelectionModal';
</style>
