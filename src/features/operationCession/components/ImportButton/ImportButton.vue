<template>
	<farm-btn
		title="Importar"
		:color="color ? color : 'secondary'"
		class="d-flex align-center justify-center"
		:outlined="outlined"
		@click="onClick"
	>
		<farm-icon size="md">{{ icon }}</farm-icon>
		{{ label }}
	</farm-btn>
</template>

<script lang="ts">
import { toRefs } from 'vue';
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name:"import-button-operation-cession",
	props: {
		label: {
			type: String,
			default: 'Importações',
		},
		outlined: {
			type: Boolean,
		},
		color: {
			type: String,
		},
		icon: {
			type: String,
			default: 'upload-outline',
		},
	},
	setup(props, { emit }) {
		const { label, outlined, color, icon } = toRefs(props);

		const value = ref(false);

		function onClick(): void {
			emit('onClick');
		}

		return {
			onClick,
			value,
			label,
			outlined,
			color,
			icon,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './ImportButton.scss';
</style>
