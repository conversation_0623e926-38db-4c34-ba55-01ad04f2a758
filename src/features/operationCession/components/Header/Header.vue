<template>
	<farm-box>
		<farm-row>
			<farm-col cols="12" sm="4" md="8" align-self="center">
				<div class="mb-6 mb-sm-0">
					<farm-heading :type="6" variation="bold" color-variation="darken">
						{{ title }}
					</farm-heading>
				</div>
			</farm-col>
			<farm-col cols="12" sm="8" md="4">
				<farm-stepper-header
					:steps="steps"
					:currentStep="currentStep"
					:errorCurrentStepStatus="errorCurrentStep"
				/>
			</farm-col>
		</farm-row>
	</farm-box>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	name: 'cession-header',
	props: {
		title: {
			type: String,
			default: 'N/A',
		},
		steps: {
			type: Array,
			default: () => [
				{ label: 'Produto' },
				{ label: '<PERSON>cebí<PERSON>' },
				{ label: 'Resumo' },
				{ label: 'Pagamento' },
			],
		},
		currentStep: {
			type: Number,
			default: 1,
		},
		errorCurrentStep: {
			type: Boolean,
			defalut: false,
		},
	},
});
</script>
