<template>
	<farm-box>
		<farm-card class="mb-6">
			<farm-card-content>
				<farm-row justify="space-between" align="center">
					<farm-col cols="12" md="9">
						<list-id-caption :data="dataCaption" />
					</farm-col>
					<farm-col cols="12" md="3" align="end">
						<farm-btn-confirm
							class="v-btn--responsive"
							customIcon="plus"
							title="Nova Cessão"
							:icon="true"
							:disabled="disabledBtn || !canWrite"
							@click="onClickNewButton"
						>
							Nova Cessão
						</farm-btn-confirm>
					</farm-col>
				</farm-row>
			</farm-card-content>
		</farm-card>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

import ListIdCaption from '@/components/ListIdCaption';
import { useAnalytics } from '@/composible/useAnalytics';
import useRouter from '@/composible/useRouter';

import { list } from '../../configurations/headerPage';

export default defineComponent({
	name: 'header-home-cession',
	components: {
		ListIdCaption,
	},
	props: {
		totals: {
			type: Object,
			required: true,
		},
		disabledBtn: {
			type: Boolean,
			required: false,
		},
	},
	setup(props) {
		const router = useRouter();
		const { trigger } = useAnalytics();

		const { totals } = toRefs(props);

		const dataCaption = computed(() => {
			if (totals.value !== null) {
				list[0].subtitle = brl(totals.value.totalDisbursement) || '-';
				list[1].subtitle = brl(totals.value.totalAnalysis) || '-';
				list[2].subtitle = totals.value.assignmentsInDisbursement || 0;
				return list;
			}
			return list;
		});

		function onClickNewButton() {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					description: 'clicou no botão nova cessão',
				},
			};
			trigger(dataTrigger);
			router.push(`/admin/wallet/cessoes/produto_comercial`);
		}

		return {
			dataCaption,
			onClickNewButton,
		};
	},
});
</script>
