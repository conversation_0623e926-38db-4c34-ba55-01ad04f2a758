<template>
	<cards>
		<template slot="header">
			<farm-row
				id="card__header"
				class="mb-0 flex-column-reverse flex-md-row"
				align="center"
				justify="around"
			>
				<farm-col class="mb-2" cols="12" md="10">
					<farm-bodytext :type="2" variation="bold" ellipsis>
						{{ item.name }}
					</farm-bodytext>
				</farm-col>
				<farm-col cols="12" md="2">
					<div class="d-flex justify-end">
						<farm-btn icon color="error" @click.stop="onRemoveProvider(item)">
							<farm-icon>delete-outline</farm-icon>
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
			<farm-row class="mb-0" align="center" justify="center">
				<farm-col cols="12" md="6">
					<farm-caption variation="regular"> CNPJ </farm-caption>
					<farm-bodytext :type="2" variation="bold" ellipsis>
						{{ item.document }}
					</farm-bodytext>
				</farm-col>
				<farm-col cols="12" md="6">
					<farm-label :required="true"
						>Defina o Valor ({{ percentageValue }}% do Desembolso)
					</farm-label>
					<farm-textfield-v2
						v-model="innerValue"
						:disabled="isDisabled"
						:mask="events.currencyMask"
						:rules="[rules.required, rules.validate, rules.percentage, rules.minValue]"
						@blur="handleBlur(item.id, innerValue)"
					/>
				</farm-col>
			</farm-row>
		</template>
		<template slot="body">
			<farm-row
				v-if="dataBankInOperation"
				align="center"
				justify="space-between"
				no-default-gutters
			>
				<farm-col>
					<farm-caption> Tipo</farm-caption>
					<farm-bodytext :type="2" :variation="'bold'">
						{{
							dataBankInOperation.type === 'account'
								? dataBankInOperation.accountType
								: 'Pix'
						}}
					</farm-bodytext>
				</farm-col>
				<farm-col v-if="dataBankInOperation.type === 'account'">
					<farm-caption> Banco</farm-caption>
					<farm-bodytext :type="2" :variation="'bold'" ellipsis>
						{{ dataBankInOperation.bank }}
					</farm-bodytext>
				</farm-col>
				<farm-col v-if="dataBankInOperation.type === 'account'">
					<farm-caption> Agência</farm-caption>
					<farm-bodytext :type="2" :variation="'bold'" ellipsis>
						{{ dataBankInOperation.agency }}
					</farm-bodytext>
				</farm-col>
				<farm-col v-if="dataBankInOperation.type === 'account'">
					<farm-caption> Número da Conta</farm-caption>
					<farm-bodytext :type="2" :variation="'bold'" ellipsis>
						{{ dataBankInOperation.account }}
					</farm-bodytext>
				</farm-col>
				<farm-col v-if="dataBankInOperation.type === 'pix'">
					<farm-caption>Tipo de Chave</farm-caption>
					<farm-bodytext :type="2" :variation="'bold'" ellipsis>
						{{ dataBankInOperation.pixKeyType }}
					</farm-bodytext>
				</farm-col>
				<farm-col v-if="dataBankInOperation.type === 'pix'">
					<farm-caption>Chave</farm-caption>
					<farm-bodytext :type="2" :variation="'bold'" ellipsis>
						{{ dataBankInOperation.pixKey }}
					</farm-bodytext>
				</farm-col>
			</farm-row>

			<farm-row align="center" justify="center">
				<farm-btn plain @click="onAddDataBank(item)">
					{{ configButtonAccout.text }}
					<farm-icon>{{ configButtonAccout.icon }}</farm-icon>
				</farm-btn>
			</farm-row>
		</template>
	</cards>
</template>

<script lang="ts">
import { defineComponent, toRefs, watch, ref, onMounted, computed } from 'vue';

import { decimals, useAnalytics } from '@farm-investimentos/front-mfe-libs-ts';

import Cards from '@/components/Cards';
import { useRoute } from '@/composible';

import { useDataBank } from '../DataBankModal/composables/useDataBank';

import { useState } from './composibles';
import { useProviderCard } from './composibles/useProviderCard';

export default defineComponent({
	name: 'provider-card',
	components: {
		Cards,
	},
	props: {
		item: {
			required: true,
			type: Object,
		},
		isDisabled: {
			type: Boolean,
			default: false,
		},
		value: {
			type: String,
			default: '',
		},
		valueDisbursement: {
			type: Number,
			required: true,
		},
		totalAvailable: {
			type: Number,
			required: true,
		},
	},
	setup(props, { emit }) {
		const route = useRoute();
		const { state, events } = useState(props, emit);
		const { dataBank, getDataBank } = useDataBank();
		const { updatedAmountToPay } = useProviderCard();
		const { trigger } = useAnalytics();

		const { item, valueDisbursement, totalAvailable } = toRefs(props);
		const innerValue = ref(props.value);
		const operationId = route.params.id;
		const dataBankInOperation = computed(() => {
			return findFirstInOperation(dataBank.value);
		});

		const percentageValue = computed(() => {
			return decimals(convertToDecimal(innerValue.value), valueDisbursement.value, 2);
		});

		const configButtonAccout = computed(() => {
			return dataBankInOperation.value
				? { icon: 'cached', text: 'Alterar Dado Bancário' }
				: { icon: 'open-in-new', text: 'Selecione Dado Bancário' };
		});

		function findFirstInOperation(array) {
			for (let item of array) {
				if (item.inOperation === true) {
					return item;
				}
			}
			return null;
		}

		function contextMenuItems() {
			const removeOption = {
				label: 'Remover Fornecedor',
				handler: 'onRemoveProvider',
				icon: { color: 'gray', type: 'close' },
			};

			const detailsOption = {
				label: 'Ver Dado Bancário',
				handler: 'onAddDataBank',
				icon: { color: 'gray', type: 'open-in-new' },
			};

			return [detailsOption, removeOption];
		}

		function onRemoveProvider(item): void {
			emit('onRemoveProvider', item);
		}

		function onAddDataBank(item): void {
			emit('onAddDataBank', item);
		}

		function convertToDecimal(value) {
			const numericValue = value.replace(/[^\d,]/g, '');
			const numericWithoutSeparator = numericValue.replace(/\./g, '');

			const numericWithDotSeparator = numericWithoutSeparator.replace(/,/g, '.');

			const decimalValue = parseFloat(numericWithDotSeparator);

			return decimalValue;
		}

		function handleBlur(idProvider, amountToPay) {
			const parseAmountToPay = convertToDecimal(amountToPay);
			if (!validateAllRules(amountToPay)) {
				return;
			} else {
				const dataTrigger = {
					event: 'operation_cession',
					payload: {
						step: 4,
						description: `atualiza o valor para o pagamento`,
						data: {
							id: operationId,
							idProvider,
							amountToPay: convertToDecimal(amountToPay),
						},
					},
				};

				trigger(dataTrigger);

				updatedAmountToPay({
					id: operationId,
					idProvider,
					amountToPay: parseAmountToPay,
				});
			}
		}

		function validateAmount(value) {
			const newValue = convertToDecimal(value);
			if (!isNaN(newValue)) {
				return true;
			}
			return 'Campo inválido';
		}

		function maximumPercentage() {
			return totalAvailable.value < 0 ? 'Você ultrapassou o valor para pagamento.' : true;
		}

		function minimumValue(value) {
			const invalidValues = ['R$0,00', 'R$0,0', 'R$0'];

			if (invalidValues.includes(value)) {
				return 'O valor deve ser maior que R$ 0,01';
			}

			return true;
		}

		const rules = computed(() => {
			return {
				required: value => !!value || 'Campo obrigatório',
				validate: value => validateAmount(value),
				percentage: () => maximumPercentage(),
				minValue: value => minimumValue(value),
			};
		});

		function validateAllRules(value) {
			const allRules = rules.value;
			for (const rule in allRules) {
				const validationFunction = allRules[rule];
				const result = validationFunction(value);
				if (result !== true) {
					return false;
				}
			}
			return true;
		}

		watch(
			() => props.value,
			() => {
				innerValue.value = props.value;
			}
		);

		watch(innerValue, n => {
			emit('input', n);
		});

		onMounted(() => {
			getDataBank(operationId, item.value.document);
		});

		return {
			item,
			rules,
			percentageValue,
			dataBankInOperation,
			configButtonAccout,
			events,
			state,
			innerValue,
			contextMenuItems,
			onRemoveProvider,
			onAddDataBank,
			handleBlur,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './ProviderCard.scss';
</style>
