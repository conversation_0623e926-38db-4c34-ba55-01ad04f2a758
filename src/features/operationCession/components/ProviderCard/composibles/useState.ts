import { watch, reactive, computed } from 'vue';

import { brl as formatCurrency } from '@farm-investimentos/front-mfe-libs-ts';

import { currency as currencyMask, currencyUnmask } from '@/helpers/masks';

type ContextMenu = {
	label: string;
	handler: string;
	icon: {
		color: string;
		type: string;
	};
};

type UseState = {
	events: {
		currencyMask: typeof currencyUnmask;
		formatCurrency: (d: any) => string;
		contextMenuItems: () => ContextMenu[];
	};
	state: {
		value: any;
		items: Array<any>;
		required: boolean;
	};
};

export function useState(props, emit): UseState {
	const { value } = props;

	const state = reactive({
		value,
		items: [],
		required: computed(() => value => (value !== 'R$' && !!value) || 'Campo obrigatório'),
	});

	watch(
		() => state.value,
		newValue => {
			emit('input', currencyUnmask(newValue));
		},
		{ deep: true }
	);

	function contextMenuItems() {
		const removeOption = {
			label: 'Remover Fornecedor',
			handler: 'removeProvider',
			icon: { color: 'gray', type: 'close' },
		};

		const detailsOption = {
			label: 'Ver Dado Bancário',
			handler: 'addDataBank',
			icon: { color: 'gray', type: 'open-in-new' },
		};

		return [detailsOption, removeOption];
	}

	return {
		events: {
			currencyMask,
			formatCurrency,
			contextMenuItems,
		},
		state,
	};
}
