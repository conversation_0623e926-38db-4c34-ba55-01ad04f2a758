import { computed, ref } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useGetter, useSelectedProductId, useStore } from '@/composible';

import { updatedPayment as updatedAmountToPayService } from '../../../../services';

export function useProviderCard() {

	const callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id, idProvider, amountToPay }) => {
			const payload = {
				amountToPay
			};

			return updatedAmountToPayService({
				id,
				productId,
				payload,
				idProvider
			});
		},
		onSuccess: () => {
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc(false);
				}, 2000);
			}
		},
		onError: () => {
			createNotificationError();
			if (callFunc !== null) {
				setTimeout(() => {
					callFunc(true);
				}, 2000);
			}
		}
	});

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			`Erro ao atualizar valor. Por favor tente novamente`
		);
	}

	function updatedAmountToPay({ id, idProvider, amountToPay }): void {
		mutate({ id, idProvider, amountToPay });
	}


	return {
		updatedAmountToPay
	};
}
