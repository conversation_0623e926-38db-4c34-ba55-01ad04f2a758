<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters>
			<farm-col cols="12" md="6" v-for="item in data" :key="item.id">
				<data-bank-card
					:selected="selected"
					:data="item"
					@changeValue="changeValue"
					@onUpdateDataBankClicked="onUpdateDataBankClicked"
				/>
			</farm-col>
		</farm-row>
		<farm-row v-if="isDataEmpty">
			<farm-box >
				<farm-emptywrapper subtitle="" />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, toRefs, computed, ref, watch } from 'vue';

import DataBankCard from '../DataBankCard';

export default defineComponent({
	name:"data-bank-list",
	components: {
		DataBankCard
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props, { emit }) {
		const { data } = toRefs(props);
		const isDataEmpty = computed(() => data.value.length === 0);
		const selected = ref(0);


		function changeValue(id: string): void {
			selected.value = parseInt(id, 10);
		}

		function onUpdateDataBankClicked(id): void {
			emit('onUpdateDataBankClicked', id);
		}

		watch(data, (newValue) => {
			const hasSelected = newValue.filter((item) => {
				return item.inOperation === true;
			});
			selected.value = hasSelected.length > 0 ? parseInt(hasSelected[0].id, 10) : 0;
			if(hasSelected.length > 0) {
				emit('onUpdateDataBankClicked', hasSelected[0].id);
			}
		});

		return {
			selected,
			data,
			isDataEmpty,
			changeValue,
			onUpdateDataBankClicked
		};
	}
});
</script>

