<template>
	<farm-modal :offsetBottom="64" :offsetTop="48" :persistent="true" :value="value" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Selecionar Dado Bancário"  @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-row class="my-4">
				<farm-col cols="12">
					<farm-heading :type="6">{{ dataCurrent.name }}</farm-heading>
				</farm-col>
				<farm-col cols="12">
					<farm-caption :variation="'regular'" color="black" color-variation="40">
						CNPJ: {{dataCurrent.document}}
					</farm-caption>
				</farm-col>
			</farm-row>
			<data-bank-list
				v-if="!isError"
				:data="dataBank"
				@onUpdateDataBankClicked="onUpdateDataBankClicked"
			/>
			<farm-row class="mb-4" v-if="!isError"></farm-row>
			<farm-loader mode="overlay"  v-if="isLoading" />
			<div v-if="isError" class="my-10 d-flex justify-center">
				<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
			</div>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				close-label="Cancelar"
				confirmLabel="Continuar"
				:isConfirmDisabled="isDisabledButton"
				@onConfirm="onConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, toRefs, onMounted, ref, computed } from 'vue';

import { useRoute } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';

import DataBankList from '../DataBankList';

import { useDataBank } from './composables/useDataBank';
import { useDataBankJoin } from './composables/useDataBankJoin';

export default defineComponent({
	name:"originator-data-bank-modal",
	components: {
		DataBankList,
	},
	props: {
		value: {
			type: Boolean,
			required: true,
		},
		dataCurrent: {
			type: Object,
			required: true,
		}
	},
	setup(props, { emit }) {
		const route = useRoute();
		const { trigger } = useAnalytics();
		const {
			dataBank,
			getDataBank,
			isLoadingDataBank
		} = useDataBank();

		const {
			isLoadingDataBankJoin,
			saveDataBank
		} = useDataBankJoin();

		const { dataCurrent } = toRefs(props);
		const dataBankClicked = ref(null);
		const operationId = route.params.id;

		const isLoading = computed(() => {
			return isLoadingDataBank.value || isLoadingDataBankJoin.value;
		});

		const isError = computed(() => {
			return false;
		});

		const isDisabledButton = computed(() => {
			return dataBankClicked.value === null;
		});

		function onClose(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 4,
					description:`clicou no botão cancelar modal de dados bancario`
				}
			};
			trigger(dataTrigger);
			emit('onClose');
		}

		function saveDataBankCallBack(): void {
			emit('onClose');
			emit('onReload');
		}

		function onConfirm(): void {
			const id = operationId;
			const payload = [{
				idDataBank: dataBankClicked.value,
				idOriginators: dataCurrent.value.id,
				value: parseFloat(dataCurrent.value.value)
			}];
			saveDataBank({ id, payload }, saveDataBankCallBack);
		}

		function onUpdateDataBankClicked(data): void {
			dataBankClicked.value = data;
		}

		function load(): void {
			const id = operationId;
			const document = dataCurrent.value.document;
			getDataBank(id, document);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			dataBank,
			isLoading,
			isError,
			isDisabledButton,
			onClose,
			onConfirm,
			onUpdateDataBankClicked,
			onReload
		};
	},
});
</script>
