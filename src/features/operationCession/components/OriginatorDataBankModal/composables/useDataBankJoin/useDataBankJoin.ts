
import { computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	createOriginatorDataBankInOperation as createOriginatorDataBankInOperationService
} from '@/features/operationCession/services';


type UseDataBankJoin = {
	isLoadingDataBankJoin: {
		value: boolean
	};
	saveDataBank: Function;
}

export function useDataBankJoin(): UseDataBankJoin {
	let callFunc: Function | null = null;

	const productId = useSelectedProductId().value;

	const { isLoading, mutate } = useMutation({
		mutationFn: ({ id, payload }) => {
			return createOriginatorDataBankInOperationService({
				id,
				productId,
				payload
			});
		},
		onSuccess: () => {
			createNotification();
			if(callFunc !== null) {
				setTimeout(() => {
					callFunc();
				}, 1500);
			}
		},
		onError: () => {
			createNotificationError();
		}
	});

	const isLoadingDataBankJoin = computed(() => {
		return isLoading.value;
	});

	function createNotification(): void {
		notification(
			RequestStatusEnum.SUCCESS,
			`Dado bancário atualizado para pagamento!`
		);
	}

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			`Erro ao atualizar o dado bancário para pagamento!`
		);
	}

	function saveDataBank({ id, payload }, callback: Function): void {
		mutate({ id, payload });
		callFunc = callback;
	}

	return {
		isLoadingDataBankJoin,
		saveDataBank
	};
}

