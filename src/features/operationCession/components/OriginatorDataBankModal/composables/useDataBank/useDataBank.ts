
import { computed, ref } from 'vue';

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	getOriginatorsDataBank as getOriginatorsDataBankServices
} from '@/features/operationCession/services';


type UseDataBank = {
	dataBank: {
		value: Array<any>
	};
	isLoadingDataBank: {
		value: boolean
	};
	isErrorDataBank: {
		value: boolean
	};
	getDataBank: Function;
}

export function useDataBank(): UseDataBank {
	const dataBank = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({ id, document }) => {
			return getOriginatorsDataBankServices({
				id,
				document
			});
		},
		onSuccess: (response) => {
			dataBank.value = response.data.content;
		},
		onError: () => {
			dataBank.value = [];
		},
	});

	const isLoadingDataBank = computed(() => {
		return isLoading.value;
	});

	const isErrorDataBank = computed(() => {
		return isError.value;
	});

	function getDataBank(id: number, document:number): void {
		mutate({ id, document });
	}

	return {
		isLoadingDataBank,
		isErrorDataBank,
		dataBank,
		getDataBank
	};
}

