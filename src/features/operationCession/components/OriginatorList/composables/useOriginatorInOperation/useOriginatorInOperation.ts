import { computed, ref } from 'vue';

import { useMutation } from "@tanstack/vue-query";

import {
	getOriginators as getOriginatorsServices,
	getOriginatorsDataBank as getOriginatorsDataBankService
} from '@/features/operationCession/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';


type UseOriginatorInOperation = {
	originators:Array<any>;
	isLoadingOriginator: {
		value: boolean
	};
	isErrorOriginator: {
		value: boolean
	};
	getOriginatorsInOperation: Function;
}

export function useOriginatorInOperation(): UseOriginatorInOperation {
	let callFunc: Function | null = null;
	const originators = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({ id }) => {
			return getOriginatorsServices({
				id
			});
		},
		onSuccess: (response) => {
			originators.value = response.data.content;
			if(callFunc) callFunc(originators.value, false);

		},
		onError: (error) => {
			if (isHttpRequestError(error, 404)) {
				originators.value = [];
				if(callFunc) callFunc(originators.value, false);
				return;
			}
			if(callFunc) callFunc([], true);
		},
	});

	const isLoadingOriginator = computed(() => {
		return isLoading.value;
	});

	const isErrorOriginator = computed(() => {
		return isError.value;
	});

	function getOriginatorsInOperation({ id }, callback: Function): void {
		mutate({ id });
		originators.value =[];
		callFunc = callback;
	}

	return {
		isLoadingOriginator,
		isErrorOriginator,
		originators,
		getOriginatorsInOperation
	};
}
