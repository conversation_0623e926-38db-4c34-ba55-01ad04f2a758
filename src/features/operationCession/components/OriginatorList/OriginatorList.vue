<template>
	<div class="my-4">
		<farm-row>
			<farm-col
				v-for="item in data"
				cols="6"
				:key="item.id"
				class="mb-6"
			>
				<originator-card
					:item="item"
					@onOpenModalBank="onOpenModalBank(item)"
				/>
			</farm-col>
		</farm-row>
		<farm-row>
			<farm-col cols="12">
				<information-important
					@openModalInformation="openModalInformation"
				/>
			</farm-col>
		</farm-row>
		<farm-loader class="loading-center" v-if="isLoading" />
		<div v-if="isError" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onReload" />
		</div>
		<originator-data-bank-modal
			v-if="isOpenModal"
			v-model="isOpenModal"
			:dataCurrent="dataBankSeleted"
			@onClose="onCloseModal"
			@onReload="onReload"
		/>
	</div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted, ref } from 'vue';

import {
	brl
} from '@farm-investimentos/front-mfe-libs-ts';

import { useModal } from '@/composible/useModal';

import InformationImportant from '../InformationImportant';
import OriginatorCard from '../OriginatorCard';
import OriginatorDataBankModal from '../OriginatorDataBankModal';

import { useOriginatorInOperation } from './composables/useOriginatorInOperation';

export default defineComponent({
	name:"originator-list",
	components: {
		OriginatorCard,
		OriginatorDataBankModal,
		InformationImportant
	},
	props: {
		id: {
			type: Number,
			require: true
		}
	},
	setup(props, {emit}) {

		const {
			getOriginatorsInOperation,
			isLoadingOriginator
		} = useOriginatorInOperation();

		const { isOpenModal,onCloseModal, onOpenModal } = useModal();

		const data = ref([]);
		const isError = ref(false);
		const dataBankSeleted = ref(null);

		const isLoading = computed(() => isLoadingOriginator.value);

		function onOpenModalBank(item): void {
			dataBankSeleted.value = item;
			onOpenModal();
		}

		function updatedPage(originators, error): void {
			if(error) {
				isError.value = error;
				return;
			}
			data.value = originators;
			const isDisabledButton = originators.filter((item) => {
				return item.withBankDataAssociated === true;
			});
			emit('onValidButton', isDisabledButton.length === originators.length);
		}

		function openModalInformation(): void {
			emit('openModalInformationImportant');
		}

		function load(): void {
			const id = props.id;
			getOriginatorsInOperation({ id }, updatedPage);
		}

		function onReload(): void {
			load();
		}

		onMounted(() => {
			load();
		});

		return {
			data,
			dataBankSeleted,
			isLoading,
			isError,
			isOpenModal,
			formatMoney: brl,
			onReload,
			onOpenModalBank,
			onCloseModal,
			openModalInformation
		};
	},
});
</script>

<style lang="scss" scoped>
@import './OriginatorList.scss';
</style>
