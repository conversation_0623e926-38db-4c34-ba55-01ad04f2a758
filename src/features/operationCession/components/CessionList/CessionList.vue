<template>
	<farm-box>
		<farm-row v-if="!isDataEmpty" y-grid-gutters >
			<farm-col cols="12" v-for="item in data" :key="item.id">
				<cession-card
					:item="item"
					@onOpenModalDenyReasons="onOpenModalDenyReasons"
					@onOpenModalReceivablesApproved="onOpenModalReceivablesApproved"
					@onRedirect="onRedirect"
				/>
			</farm-col>
		</farm-row>
		<farm-row extra-decrease v-if="isDataEmpty">
			<farm-box>
				<farm-emptywrapper subtitle="Tente criar uma cessao." />
			</farm-box>
		</farm-row>
	</farm-box>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

import CessionCard from '../CessionCard';

export default defineComponent({
	name:"cession-list",
	components:{
		CessionCard
	},
	props: {
		data: {
			type: Array,
			required: true,
		},
	},
	setup(props, { emit }) {
		const isDataEmpty = computed(() => props.data.length === 0);

		function onOpenModalDenyReasons(data): void{
			emit('onOpenModalDenyReasons', data);
		}

		function onOpenModalReceivablesApproved(data): void{
			emit('onOpenModalReceivablesApproved', data);
		}

		function onRedirect(data): void {
			emit('onRedirect', data);
		}

		return {
			isDataEmpty,
			onOpenModalDenyReasons,
			onOpenModalReceivablesApproved,
			onRedirect
		};
	},
});

</script>
