import { reactive } from 'vue';

export type PaginationType = {
	currentPage: number;
	totalPages: number;
	totalItems: number;
};

type UsePagination = {
	pagination: PaginationType;
	updateCurrentPage: Function;
	updateTotalPages: Function;
	updateTotalItems: Function;
};

export function usePagination(): UsePagination {
	const pagination = reactive({
		currentPage: 1,
		totalPages: 1,
		totalItems: 0,
	});

	function updateCurrentPage(currentPage: number): void {
		pagination.currentPage = currentPage + 1;
	}

	function updateTotalPages(totalPages: number): void {
		pagination.totalPages = totalPages;
	}

	function updateTotalItems(totalItems: number): void {
		pagination.totalItems = totalItems;
	}

	return {
		pagination,
		updateCurrentPage,
		updateTotalPages,
		updateTotalItems,
	};
}
