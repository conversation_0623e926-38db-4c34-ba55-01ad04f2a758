import { computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	builderReceivables
} from '@/features/operationCession/helpers/builderReceivables';
import {
	getReceived as getReceivedService
} from '@/features/operationCession/services';
import { isHttpRequestError } from '@/helpers/isHttpRequestError';

type UseReceivables = {
	receivables:{
		value: Array<any>
	}
	isLoadingReceivables: {
		value: boolean
	};
	isErrorReceivables: {
		value: boolean
	};
	getReceivables: Function;
}

export function useReceivables(): UseReceivables {
	const receivables = ref([]);
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => {
			return getReceivedService(params);
		},
		onSuccess: (response) => {
			const data = builderReceivables(response.data);
			receivables.value = data.content;
			if(callFunc) callFunc(data, false);
		},
		onError: (error) => {
			const dataReturn = {
				content: [],
				roofIds: [],
				selectedItems: [],
				pagination: {
					page:0,
					totalItems:0,
					totalPages:0,
				},
				total: 0,
			};
			if (isHttpRequestError(error, 404)) {
				if(callFunc) {
					receivables.value = [];
					callFunc(dataReturn, false);
				}
				return;
			}
			receivables.value = [];
			if(callFunc) {
				callFunc(dataReturn, true);
			}
		},
	});

	const isLoadingReceivables = computed(() => {
		return isLoading.value;
	});

	const isErrorReceivables = computed(() => {
		return isError.value;
	});

	function getReceivables(filters, callback: Function): void {
		const params = queryString(filters, {});
		mutate({ productId, params });
		callFunc = callback;
	}

	return {
		receivables,
		isLoadingReceivables,
		isErrorReceivables,
		getReceivables
	};
}
