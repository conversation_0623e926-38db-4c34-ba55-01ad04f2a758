import { useAnalytics } from '@/composible/useAnalytics';
import useRouter from '@/composible/useRouter';

type UseRedirect = {
	redirectHome: Function;
	redirectPageSuccess: Function;
	onClickButtonImport: Function;
	onClickBack: Function;
};

export function useRedirect(): UseRedirect {
	const router = useRouter();
	const { trigger } = useAnalytics();

	function onClickButtonImport(): void {
		const dataTrigger = {
			event: 'operation_cession',
			payload:{
				step: 2,
				description:'clicou no botão de importar novos recebíveis',
			}
		};
		trigger(dataTrigger);
		router.push(`/admin/wallet/banco_de_recebiveis/importacao_de_recebiveis`);
	}

	function onClickBack(): void {
		const dataTrigger = {
			event: 'operation_cession',
			payload:{
				step: 2,
				description:'clicou no botão de voltar',
			}
		};
		trigger(dataTrigger);
		router.push(`/admin/wallet/cessoes/produto_comercial`);
	}

	function redirectPageSuccess(id?: string): void {
		const idCession = id ? `?id_pre_section=${id}`: '';
		router.push(`/admin/wallet/cessoes/validando_recebiveis${idCession}`);
	}

	function redirectHome(): void {
		router.push(`/admin/wallet/cessoes`);
	}

	return {
		redirectHome,
		redirectPageSuccess,
		onClickButtonImport,
		onClickBack,
	};
}
