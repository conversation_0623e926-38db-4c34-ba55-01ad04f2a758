import { useMutation } from '@tanstack/vue-query';

import { builderOperationCessionResponse } from '@/features/operationCession/helpers/builderOperationCessionResponse';
import { getReceivedInvoices } from '@/features/operationCession/services';

export function useFetchOperationData({ operationId, productId, state }) {

	const { isLoading, isError, mutate, isSuccess } = useMutation({
		mutationFn: (param) => {
			return getReceivedInvoices(param);
		},
		onSuccess: (response) => {

			const { items, filters, totalPages, totalItems, roofIds } =
				builderOperationCessionResponse(response.data);
			state.invoicesData = items;
			state.filter = filters;
			state.totalItems = totalItems;
			state.totalPages = totalPages;

			state.metaData = roofIds;
		},
		onError: err => {
			const { items, filters, roofIds, totalItems, totalPages } =
				builderOperationCessionResponse(err);
			state.invoicesData = items;
			state.filter = filters;
			state.metaData = roofIds;
			state.totalItems = totalItems;
			state.totalPages = totalPages;
		},
	});

	return {
		isLoading,
		isSuccess,
		isError,
		mutate,
	};
}
