import { computed, ref } from 'vue';

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	checkLimitDraweer as checkLimitDraweerService
} from '@/features/operationCession/services';

type UseDraweer = {
	isLoadingDraweer: {
		value: boolean
	};
	isErrorDraweer: {
		value: boolean
	};
	checkLimitDraweer: Function;
}

export function useDraweer(): UseDraweer {
	const draweerCurrent = ref(null);
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => {
			return checkLimitDraweerService(params);
		},
		onSuccess: (response) => {
			if(callFunc) callFunc({
				id: draweerCurrent.value.id,
				name: draweerCurrent.value.name,
				valid: response.data.canContinue,
				newTotalSmart: response.data?.newTotalSmart || null
			}, false);
		},
		onError: () => {
			if(callFunc) callFunc(null, true);
		},
	});

	const isLoadingDraweer = computed(() => {
		return isLoading.value;
	});

	const isErrorDraweer = computed(() => {
		return isError.value;
	});

	function checkLimitDraweer(data, callback: Function): void {
		draweerCurrent.value = data;
		const payload = {
			...data.payload
		};
		mutate({ productId, payload });
		callFunc = callback;
	}

	return {
		isLoadingDraweer,
		isErrorDraweer,
		checkLimitDraweer
	};
}
