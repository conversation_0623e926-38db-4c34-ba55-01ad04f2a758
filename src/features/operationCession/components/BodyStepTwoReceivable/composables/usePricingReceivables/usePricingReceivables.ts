import { computed } from 'vue';

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	updatedPricing as updatedPricingService
} from '@/features/operationCession/services';


type UsePricingReceivables = {
	isLoadingPricing: {
		value: boolean
	};
	isErrorPricing: {
		value: boolean
	};
	updatedPricing: Function;
}

export function usePricingReceivables(): UsePricingReceivables {
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: ({ vehicleId, disbursementDate }) => {
			const payload = {
				vehicleId,
				disbursementDate,
				productId
			};
			return updatedPricingService(payload);
		},
		onSuccess: () => {
			if(callFunc) callFunc(false);
		},
	});

	const isLoadingPricing = computed(() => {
		return isLoading.value;
	});

	const isErrorPricing = computed(() => {
		return isError.value;
	});

	function updatedPricing({ vehicleId, disbursementDate }, callback: Function): void {
		mutate({ vehicleId, disbursementDate });
		callFunc = callback;
	}

	return {
		isLoadingPricing,
		isErrorPricing,
		updatedPricing
	};
}
