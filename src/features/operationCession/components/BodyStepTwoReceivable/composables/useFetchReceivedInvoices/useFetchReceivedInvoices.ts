import { useMutation } from '@tanstack/vue-query';

import { builderOperationCessionResponse } from '@/features/operationCession/helpers/builderOperationCessionResponse';
import { getReceivedInvoices } from '@/features/operationCession/services';

export function useFetchReceivedInvoices({ productId, state, NOT_FIRST_RENDER, operationId }) {
	const { isLoading, isError, mutate, isSuccess } = useMutation({
		mutationFn: params => getReceivedInvoices({ ...params, productId }),
		onSuccess: ({ data }) => {
			const { items, filters, totalPages, totalItems, roofIds } =
				builderOperationCessionResponse(data);
			state.invoicesData = items;
			state.filter = filters;
			state.totalItems = totalItems;
			state.totalPages = totalPages;

			if (!operationId.value) {
				state.metaData = roofIds;
			}

			NOT_FIRST_RENDER.value = true;
		},
		onError: err => {
			const { items, filters, roofIds, totalItems, totalPages } =
				builderOperationCessionResponse(err);
			state.invoicesData = items;
			state.filter = filters;
			state.metaData = roofIds;
			state.totalItems = totalItems;
			state.totalPages = totalPages;

			NOT_FIRST_RENDER.value = true;
		},
	});

	return {
		isLoading,
		isError,
		mutate,
		isSuccess,
		state,
		NOT_FIRST_RENDER,
	};
}
