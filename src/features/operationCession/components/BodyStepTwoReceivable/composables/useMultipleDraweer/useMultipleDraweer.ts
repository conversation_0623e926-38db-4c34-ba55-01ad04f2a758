import { computed, ref } from 'vue';

import { useMutation } from "@tanstack/vue-query";

import { useSelectedProductId } from '@/composible';
import {
	builderMultipleDraweer
} from '@/features/operationCession/helpers/builderMultipleDraweer';
import {
	checkLimitMultipleDraweer as checkLimitMultipleDraweerService
} from '@/features/operationCession/services';

type UseMultipleDraweer = {
	isLoadingMultipleDraweer: {
		value: boolean
	};
	isErrorMultipleDraweer: {
		value: boolean
	};
	checkLimitMultipleDraweer: Function;
}

export function useMultipleDraweer(): UseMultipleDraweer {
	const draweerCurrent = ref(null);
	let callFunc: Function | null = null;
	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (params) => {
			return checkLimitMultipleDraweerService(params);
		},
		onSuccess: (response) => {
			const data = builderMultipleDraweer(response.data);
			if(callFunc) callFunc(data, false);
		},
		onError: () => {
			if(callFunc) callFunc(null, true);
		},
	});

	const isLoadingMultipleDraweer = computed(() => {
		return isLoading.value;
	});

	const isErrorMultipleDraweer = computed(() => {
		return isError.value;
	});

	function checkLimitMultipleDraweer(data, callback: Function): void {
		mutate({ productId, payload: data });
		callFunc = callback;
	}

	return {
		isLoadingMultipleDraweer,
		isErrorMultipleDraweer,
		checkLimitMultipleDraweer
	};
}
