import { reactive, computed, ref } from 'vue';

import { queryString } from '@farm-investimentos/front-mfe-libs-ts';

import { useRoute, useSelectedProductId } from '@/composible';
import { useProductCache } from '@/features/operationCession/composables/useProductCache';

import { useFetchOperationData } from '../useFetchOperationData';
import { useFetchReceivedInvoices } from '../useFetchReceivedInvoices';

const state = reactive({
	isValid: false,
	isFilterShowing: false,
	showResultsLabel: false,
	showError: false,
	invoicesData: [],
	filter: {
		page: 0,
		limit: 10,
		status: 1,
		order: 'DESC',
	},
	presentablePage: computed(() => state.filter.page + 1),
	totalItems: 0,
	totalPages: 0,
	lastFilterApplied: {},
	showCancelPrompt: false,
	showModalClose: false,
	modalData: {},
	isFilterApplied: false,
	metaData: null,
	isFirstTableLoad: false,
});

export function useBody() {
	const productId = useSelectedProductId().value;
	const NOT_FIRST_RENDER = ref(false);

	const route = useRoute();

	const operationId = computed(() => route.query.id_pre_section || null);
	const vehicleId = computed(() => route.query.vehicleId || null);

	const {
		isError,
		isLoading,
		mutate: refetchInvoices,
	} = useFetchReceivedInvoices({
		productId,
		state,
		NOT_FIRST_RENDER,
		operationId,
	});

	const {
		isLoading: operationIdIsLoading,
		isError: operationIdIsError,
		mutate: fetchOperation,
	} = useFetchOperationData({
		operationId,
		productId,
		state,
	});

	const isLoadingInvoicesData = computed(() => {
		return isLoading.value || operationIdIsLoading.value;
	});

	const isErrorInvoicesData = computed(() => {
		return isError.value || operationIdIsError.value;
	});

	const { operationCessionProductCache } = useProductCache();

	function getOperationInvoices(data) {
		const filters = {
			page: state.filter.page,
			limit: state.filter.limit,
			order: state.filter.order || 'DESC',
			status: 1,
			vehicleId: data.financialVehicle,
			disbursementDate: data.dateDisbursement,
			operationId: data.id
		};

		const params = queryString(filters, {});

		fetchOperation({productId, params});
	}

	function getInvoices(): void {
		const getVehicleId =
			operationCessionProductCache.value?.idProduct ||
			//@ts-expect-error
			operationCessionProductCache.value?.financialVehicle ||
			vehicleId.value;

		const filters = {
			...state.filter,
			status: 1,
			roof: computed(() => operationCessionProductCache.value?.valueSeleted).value,
			vehicleId: getVehicleId,
			disbursementDate: operationCessionProductCache.value.dateDisbursement,
		};
		const params = queryString(filters, {});
		refetchInvoices({ params, productId });
	}

	function onConfirmCancel() {
		state.modalData = false;
	}

	function onCancelOperation() {
		state.showCancelPrompt = !state.showCancelPrompt;
	}

	function onModalClose() {
		state.showModalClose = !state.showModalClose;
	}

	return {
		state,

		onCancelOperation,
		onConfirmCancel,
		onModalClose,
		getInvoices,
		isLoading: isLoadingInvoicesData,
		isError: isErrorInvoicesData,
		NOT_FIRST_RENDER,
		operationId,
		getOperationInvoices,
		vehicleId,
	};
}
