<template>
	<farm-container>
		<header-cession title="Selecione os recebíveis que deseja ceder" :currentStep="2" />
		<farm-box class="mt-2" v-if="!isError">
			<feedback-alert color="error" v-if="isFeedbackNotPrecified" class="mb-6">
				Seus recebíveis não foram precificados. Entre em contato com nosso time de
				atendimento.
			</feedback-alert>
			<feedback-alert v-if="isFeedbackLimit" color="error" class="mb-6">
				Você ultrapassou seu limite disponível, remova alguns recebíveis da seleção para
				prosseguir.
			</feedback-alert>
			<feedback-alert
				v-if="
					!isFeedbackNotPrecified &&
					isRenderFirst !== null &&
					!isRenderFirst &&
					isMinValue
				"
				v-model="isMinValue"
				color="error"
				class="mb-6"
			>
				É necessário selecionar no mínimo {{ labelMinValue }} em recebíveis para continuar.
				Tente selecionar mais recebíveis ou faça um upload.
			</feedback-alert>
			<feedback-alert v-if="isFeedbackReceivables && !isMinValue" class="mb-6">
				Você não tem recebíveis suficientes para atingir o valor de desembolso desejado.
				Caso queira, faça o upload de novos recebíveis.
			</feedback-alert>
			<feedback-alert color="neutral" v-if="isFeedbackValue" class="mb-6">
				Você ultrapassou o valor desejado informado de <b>{{ dataFeedbackValue }}</b
				>.
			</feedback-alert>
			<feedback-alert color="neutral" v-if="isRenderFirst !== null && (!isRenderFirst && isMaxValue)" class="mb-6">
				Prezado parceiro, o limite máximo para essa cessão no produto <b>{{headerData.productName}}</b>
				deve ser de {{ labelMaxValue }}. Entre em contato com o responsável pela sua conta
				FarmTech para realizar uma operação de maior valor neste produto.
			</feedback-alert>
		</farm-box>
		<farm-box v-if="!isError">
			<farm-row class="mt-6 mb-6">
				<farm-col cols="12">
					<div class="d-flex align-center">
						<farm-bodytext :type="2" bold class="mb-0 mr-6">
							Detalhes da Cessão
						</farm-bodytext>
						<farm-btn @click="onClickChangeDates" :outlined="true">
							<farm-icon>pencil-outline</farm-icon>
							Datas e Valores
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
			<farm-row>
				<farm-col cols="12">
					<information-cession-list
						:dateDisbursement="headerData.dateDisbursement"
						:limit="headerData.limit"
						:valueDisbursement="totalValueDisbursement"
						:productName="headerData.productName"
						:typeOperation="headerData.typeOperation"
						@onClickDisbursement="onOpenModalDetailsCession"
					/>
				</farm-col>
			</farm-row>
			<farm-row extra-decrease>
				<farm-line />
			</farm-row>
			<farm-row align="center" class="mt-6" >
				<farm-col md="6" cols="12">
					<div class="d-flex align-center justify-center">
						<farm-form-mainfilter
							outlined
							label="Buscar Recebível"
							:showFilters="isFilterShowing"
							@onInputChange="onInputMainFilter"
							@onClick="onClickMainFilter"
						>
							<div class="d-flex">
								<farm-label class="mb-0 mr-2">Buscar Recebível</farm-label>
								<label-request-results
									v-if="isFilterApplied"
									:totalItems="labelResultTotal"
								/>
							</div>
						</farm-form-mainfilter>
					</div>
				</farm-col>
				<farm-col align="right" md="6" cols="12">
					<import-button-operation-cession
						outlined
						color="primary"
						label="Importar Novos Recebíveis"
						@onClick="onClickButtonImport"
					/>
				</farm-col>
			</farm-row>
			<collapse-transition :duration="300">
				<step-two-filter
					v-show="isFilterShowing"
					@onFiltersApplied="onFiltersApplied"
					@onApply="onApplyFilter"
				/>
			</collapse-transition>
			<farm-row align="center" class="mb-6" v-if="!isErrorReceivables">
				<farm-col cols="12" md="4">
					<farm-chip color="primary" variation="lighten" :dense="true">
						{{ totalSeleted }} selecionado (s)
					</farm-chip>
				</farm-col>
				<farm-col cols="12" md="8" align="right">
					<div>
						<farm-btn
							class="farm-btn--responsive"
							title="Desmarcar Selecionados"
							outlined
							:disabled="totalSeleted === 0"
							@click="onUncheckReceivables"
						>
							Desmarcar Selecionados
						</farm-btn>
					</div>
				</farm-col>
			</farm-row>
			<feedback-alert
				v-if="isFeedbackNotEnoughPrecified"
				color="warning"
				class="mb-6"
			>
				Alguns dos seus recebíveis não foram precificados. Entre em contato com nosso time
				de atendimento.
			</feedback-alert>
			<table-receivables-step-two
				v-if="!isErrorReceivables"
				:data="receivables"
				:paginationPageActive="currentPage"
				:paginationTotalPages="totalPage"
				:isCleanCheckbox="isCleanCheckbox"
				:dataSelected="selectedTable"
				:meta="itemsSelectedTable"
				:forceRender="forceRenderTable"
				@onRequest="onRequest"
				@onChangeCheckbox="onChangeCheckbox"
				@onOpenModalDetails="onOpenModalDetails"
				@onClickButtonImport="onClickButtonImport"
			/>
		</farm-box>
		<farm-loader mode="overlay" v-if="isLoading" />
		<div v-if="isError || isErrorReceivables" class="my-10 d-flex justify-center">
			<farm-alert-reload label="Ocorreu um erro" @onClick="onClickReload" />
		</div>
		<footer-cession
			labelConfirm="Continuar"
			:isDisabledButtonConfirm="isDisabledButton"
			:hiddenBack="hiddenButtonBack"
			@onClickClose="onOpenModalCloseCession"
			@onClickBack="onClickBack"
			@onClickConfirm="onClickConfirmFooter"
		/>
		<modal-close-cession
			v-model="isModalCloseCession"
			v-if="isModalCloseCession"
			@onConfirm="onConfirmModalCloseCession"
			@onClose="onCloseModalCloseCession"
		/>
		<product-selection-modal
			v-if="isModalProductSeleted"
			v-model="isModalProductSeleted"
			:productCurrent="productCurrent"
			:noRedirect="true"
			@onCloseModal="onCloseModalProductSeleted"
			@onSetNewValue="onSetNewValue"
		/>
		<modal-detail-cession
			v-if="isModalDetailCession"
			v-model="isModalDetailCession"
			:data="dataModalDetailCession"
			:receivables="false"
			@onClose="onCloseModalModalDetailCession"
		/>
		<modal-detail-receivables-approved
			v-if="isModalReceivablesApproved"
			v-model="isModalReceivablesApproved"
			:data="detailReceivablesApproved"
			@onClose="onCloseModalReceivablesApproved"
		/>
	</farm-container>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import FeedbackAlert from '@/components/FeedbackAlert';
import InformationCessionList from '@/components/InformationCessionList';
import LabelRequestResults from '@/components/LabelRequestResults';
import ModalCloseCession from '@/components/ModalCloseCession';
import ModalDetailCession from '@/components/ModalDetailCession';
import ModalDetailReceivablesApproved from '@/components/ModalDetailReceivablesApproved';
import { useRoute } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';
import { useProductCache } from '@/features/operationCession/composables/useProductCache';
import { currencyUnmask } from '@/helpers/masks';

import { useInformationCession } from '../../composables/useInformationCession';
import { useSelectedProducts } from '../../composables/useSelectedProducts';
import Footer from '../Footer';
import Header from '../Header';
import ImportButton from '../ImportButton';
import ProductSelectionModal from '../ProductSelectionModal';
import StepTwoFilter from '../StepTwoFilter';
import TableReceivablesV2 from '../TableReceivablesV2';

import { usePricingReceivables } from './composables/usePricingReceivables';
import { useReceivables } from './composables/useReceivables';
import { useRedirect } from './composables/useRedirect';

export default defineComponent({
	name: 'body-step-two-receivable',
	components: {
		'header-cession': Header,
		'footer-cession': Footer,
		'import-button-operation-cession': ImportButton,
		'table-receivables-step-two': TableReceivablesV2,
		StepTwoFilter,
		LabelRequestResults,
		ModalDetailReceivablesApproved,
		ModalDetailCession,
		InformationCessionList,
		ModalCloseCession,
		ProductSelectionModal,
		FeedbackAlert,
	},
	setup() {
		const { trigger } = useAnalytics();
		const route = useRoute();
		const { addDataCache, operationCessionProductCache } = useProductCache();
		const { addSelectedDataToCache } = useSelectedProducts();
		const { getInformationCession, isErrorInformationCession, isLoadingInformationCession } =
			useInformationCession();
		const { updatedPricing, isLoadingPricing, isErrorPricing } = usePricingReceivables();
		const { getReceivables, isLoadingReceivables, receivables } = useReceivables();
		const { onClickBack, onClickButtonImport, redirectPageSuccess, redirectHome } =
			useRedirect();
		const {
			isOpenModal: isModalProductSeleted,
			onCloseModal: onCloseModalProductSeleted,
			onOpenModal: openModalModalProductSeleted,
		} = useModal();
		const {
			isOpenModal: isModalDetailCession,
			onCloseModal: onCloseModalModalDetailCession,
			onOpenModal: onOpenModalModalDetailCession,
		} = useModal();
		const {
			isOpenModal: isModalCloseCession,
			onCloseModal: onCloseModalCloseCession,
			onOpenModal: openModalCloseCession,
		} = useModal();
		const {
			isOpenModal: isModalReceivablesApproved,
			onCloseModal: onCloseModalReceivablesApproved,
			onOpenModal: openModalReceivablesApproved,
		} = useModal();

		const operationId = ref(route.query.id_pre_section || null);
		const vehicleId = ref(route.query.vehicleId || null);
		const headerData = ref({
			dateDisbursement: '-',
			limit: '-',
			valueDisbursement: '-',
			productName: '-',
			typeOperation: '-',
		});
		const dataModalDetailCession = ref({
			dateDisbursement: null,
			limit: 0,
			valueDisbursement: 0,
			productName: '-',
			typeOperation: '-',
			freeValue: 0,
			nominalValue: 0,
		});
		const isFilterShowing = ref(false);
		const isFilterApplied = ref(false);
		const isCleanCheckbox = ref(false);
		const isErrorReceivables = ref(false);
		const labelResultTotal = ref(0);
		const totalSeleted = ref(0);
		const selectedTable = ref([]);
		const itemsSelectedTable = ref([]);
		const currentPage = ref(0);
		const totalPage = ref(0);
		const roofIds = ref(0);
		const detailReceivablesApproved = ref(null);
		const forceRenderTable = ref(false);
		const filterTable = ref({
			page: 0,
			limit: 10,
			order: 'DESC',
		});
		const productCurrent = ref({
			availableLimit: '-',
			id: '-',
			name: '-',
		});
		const valueSeleted = ref(0);
		const totalReceivablesProduct = ref(0);
		const totalLimit = ref(0);
		const labelMinValue = ref(brl(checkValueMinIsNull()));
		const labelMaxValue = ref(brl(checkValueMaxIsNull()));
		const draweeName = ref('');
		const totalSmart = ref(0);
		const totalReceivablesSeleted = ref(0);
		const isRenderFirst = ref(null);
		const smartSelecteds = ref([]);
		const isFeedbackNotEnoughPrecified = ref(null);
		const isFeedbackNotPrecified = ref(null);
		const hiddenButtonBack = ref(false);

		const dataFeedbackValue = computed(() => {
			if (operationCessionProductCache.value.valueSeleted) {
				return brl(operationCessionProductCache.value.valueSeleted);
			}
			return headerData.value.limit;
		});
		const totalValueDisbursement = computed(() => {
			if (selectedTable.value.length > 0) {
				const data = selectedTable.value.reduce((acc, item) => {
					return acc + parseFloat(item.netValue);
				}, 0);
				return brl(data);
			}
			return brl(0);
		});
		const isFeedbackLimit = computed(() => {
			if (
				currencyUnmask(totalValueDisbursement.value) >
				currencyUnmask(headerData.value.limit)
			) {
				return true;
			}
			return false;
		});
		const isMinValue = computed(() => {
			const value = checkValueMinIsNull();
			return (
				isRenderFirst.value !== null &&
				!isRenderFirst.value &&
				currencyUnmask(totalValueDisbursement.value) <= value
			);
		});
		const isMaxValue = computed(() => {
			const value = checkValueMaxIsNull();
			return (
				isRenderFirst.value !== null &&
				!isRenderFirst.value &&
				currencyUnmask(totalValueDisbursement.value) >= value
			);
		});
		const isFeedbackValue = computed(() => {
			if (
				valueSeleted.value > 0 &&
				valueSeleted.value < currencyUnmask(totalValueDisbursement.value)
			) {
				return true;
			}
			return false;
		});
		const isFeedbackReceivables = computed(() => {
			if (
				totalReceivablesProduct.value === itemsSelectedTable.value.length &&
				currencyUnmask(totalValueDisbursement.value) < totalLimit.value
			) {
				return true;
			}
			return false;
		});
		const totalValueFree = computed(() => {
			if (selectedTable.value.length > 0) {
				const data = selectedTable.value.reduce((acc, item) => {
					return acc + item.freeValue;
				}, 0);
				return brl(data);
			}
			return brl(0);
		});
		const totalNominalValue = computed(() => {
			if (selectedTable.value.length > 0) {
				const data = selectedTable.value.reduce((acc, item) => {
					return acc + item.nominalValue;
				}, 0);
				return brl(data);
			}
			return brl(0);
		});
		const isLoading = computed(() => {
			if (
				isLoadingPricing.value ||
				isLoadingInformationCession.value ||
				isLoadingReceivables.value
			) {
				return true;
			}
			return false;
		});
		const isError = computed(() => {
			if (isErrorPricing.value || isErrorInformationCession.value) {
				return true;
			}
			return false;
		});
		const isDisabledButton = computed(() => {
			if (isMinValue.value) {
				return true;
			}
			if (isFeedbackNotPrecified.value) {
				return true;
			}
			if (selectedTable.value.length === 0) {
				return true;
			}
			if (isFeedbackLimit.value) {
				return true;
			}
			if (isMaxValue.value) {
				return true;
			}
			return false;
		});

		function checkValueMinIsNull(): number {
			if (operationCessionProductCache.value === null) {
				return 0;
			}
			return operationCessionProductCache.value.minValue;
		}

		function checkValueMaxIsNull(): number {
			if (operationCessionProductCache.value === null) {
				return 0;
			}
			return operationCessionProductCache.value.maxValue;
		}

		function onSetNewValue(data) {
			updatedHeaderData({
				dateDisbursement: data.dateDisbursement,
				limit: data.availableLimit,
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				productName: data.nameProduct,
				typeOperation: data.nameOperation,
			});
			forceRenderTable.value = true;
			callPricing();
		}

		function onClickChangeDates(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 2,
					description: 'clicou no botão de datas e valores',
				},
			};
			trigger(dataTrigger);
			updatedProductCurrent();
			openModalModalProductSeleted();
		}

		function onOpenModalDetailsCession(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 2,
					description: 'clicou no detalhe da cessão',
					data: {
						...dataModalDetailCession.value,
					},
				},
			};
			trigger(dataTrigger);
			onOpenModalModalDetailCession();
		}

		function onFiltersApplied(data): void {
			isFilterApplied.value = data;
		}

		function onClickMainFilter(): void {
			isFilterShowing.value = !isFilterShowing.value;
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 2,
					description: 'clicou na opção de esconder filtro',
				},
			};
			if (isFilterShowing.value) {
				dataTrigger.payload.description = 'clicou na opção de ver filtro';
				trigger(dataTrigger);
				return;
			}
			trigger(dataTrigger);
		}

		function onApplyFilter(data): void {
			const value = data.value && data.value.toString();
			filterTable.value = {
				...filterTable.value,
				...data,
				value,
			};
			const payload = getPayloadReceivables();
			getReceivables(payload, callbackGetReceivables);
		}

		function onInputMainFilter(value): void {
			if (value.length >= 3 || value === '') {
				filterTable.value = {
					...filterTable.value,
					search: value,
				};
				const payload = getPayloadReceivables();
				getReceivables(payload, callbackGetReceivables);
			}
		}

		function onUncheckReceivables(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 2,
					description: 'clicou no botão de desmarcar todos',
				},
			};
			trigger(dataTrigger);
			isCleanCheckbox.value = true;
		}

		function onClickReload(): void {
			load();
		}

		function onOpenModalCloseCession(): void {
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 2,
					description: 'clicou no botão de fechar',
				},
			};
			trigger(dataTrigger);
			openModalCloseCession();
		}

		function isCheckNumber(value): number {
			if (typeof value !== 'number') {
				return parseFloat(value);
			}
			return value;
		}

		function checkNameItem(item) {
			if (Object.hasOwn(item, 'value')) {
				return isCheckNumber(item.value);
			}
			return isCheckNumber(item.netValue);
		}

		function onClickConfirmFooter(): void {
			const data = selectedTable.value.map(item => {
				return {
					id: item.id,
					netValue: checkNameItem(item),
					freeValue: isCheckNumber(item.freeValue),
				};
			});
			const sendData = {
				invoices: data,
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				isEdit: false,
				sumNominalValue: currencyUnmask(totalNominalValue.value),
				sumFreeValue: currencyUnmask(totalValueFree.value),
				minValue: operationCessionProductCache.value.minValue,
				maxValue: operationCessionProductCache.value.maxValue || null,
			};
			addSelectedDataToCache(sendData);
			redirectPageSuccess(operationId.value);
			const dataTrigger = {
				event: 'operation_cession',
				payload: {
					step: 2,
					description: 'clicou no botão de continuar',
					data: {
						...sendData,
					},
				},
			};
			trigger(dataTrigger);
		}

		function onConfirmModalCloseCession(): void {
			addDataCache(null);
			onCloseModalCloseCession();
			redirectHome();
		}

		function onRequest(data): void {
			filterTable.value = {
				...filterTable.value,
				...data,
			};
			const payload = getPayloadReceivables();
			getReceivables(payload, callbackGetReceivables);
		}

		function onChangeCheckbox(data): void {
			isCleanCheckbox.value = false;
			selectedTable.value = [...data.items];
			totalSeleted.value = selectedTable.value.length;
			updatedDataModalDetailCession({
				dateDisbursement: operationCessionProductCache.value.dateDisbursement,
				limit: operationCessionProductCache.value.availableLimit,
				productName: operationCessionProductCache.value.nameProduct,
				typeOperation: operationCessionProductCache.value.nameOperation,
			});
		}

		function onOpenModalDetails(data): void {
			detailReceivablesApproved.value = data;
			openModalReceivablesApproved();
		}

		function updatedHeaderData(data): void {
			headerData.value = {
				dateDisbursement: defaultDateFormat(data.dateDisbursement),
				limit: brl(data.limit),
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				productName: data.productName,
				typeOperation: data.typeOperation,
			};
		}

		function updatedDataModalDetailCession(data): void {
			dataModalDetailCession.value = {
				dateDisbursement: data.dateDisbursement,
				limit: data.limit,
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				productName: data.productName,
				typeOperation: data.typeOperation,
				freeValue: currencyUnmask(totalValueFree.value),
				nominalValue: currencyUnmask(totalNominalValue.value),
			};
		}

		function updatedDataDetailCessionAPI(data): void {
			updatedHeaderData(data);
			updatedDataModalDetailCession(data);
			const cache = {
				idProduct: data.financialVehicle,
				nameProduct: data.productName,
				idOperation: data.operationId,
				nameOperation: data.typeOperation,
				availableLimit: data.limit,
				dateDisbursement: data.dateDisbursement,
				valueSeleted: data.valueDisbursement,
				limit: data.limit,
				productName: data.productName,
				minValue: data.minimumCessionValue,
				maxValue: data.maxCessionValue,
			};
			addDataCache(cache);
			const payload = getPayloadReceivables();
			getReceivables(payload, callbackGetReceivables);
		}

		function hasParams(): boolean {
			if (operationId.value !== null && vehicleId.value !== null) {
				return true;
			}
			return false;
		}

		function hasDataCache(): boolean {
			if (operationCessionProductCache.value) {
				return true;
			}
			return false;
		}

		function updatedProductCurrent(): void {
			productCurrent.value = {
				availableLimit: operationCessionProductCache.value.availableLimit,
				id: operationCessionProductCache.value.idProduct,
				name: operationCessionProductCache.value.nameProduct,
				minValue: operationCessionProductCache.value.minValue,
				maxValue: operationCessionProductCache.value.maxValue,
			};
		}

		function updatedScreen(): void {
			updatedHeaderData({
				dateDisbursement: operationCessionProductCache.value.dateDisbursement,
				limit: operationCessionProductCache.value.availableLimit,
				valueDisbursement: currencyUnmask(totalValueDisbursement.value),
				productName: operationCessionProductCache.value.nameProduct,
				typeOperation: operationCessionProductCache.value.nameOperation,
			});
			valueSeleted.value = operationCessionProductCache.value.valueSeleted;
			totalLimit.value = operationCessionProductCache.value.availableLimit;
			updatedDataModalDetailCession({
				dateDisbursement: operationCessionProductCache.value.dateDisbursement,
				limit: operationCessionProductCache.value.availableLimit,
				productName: operationCessionProductCache.value.nameProduct,
				typeOperation: operationCessionProductCache.value.nameOperation,
			});
		}

		function callbackGetReceivables(data, hasError: boolean): void {
			if (hasError) {
				isErrorReceivables.value = true;
				return;
			}
			isErrorReceivables.value = false;
			isFeedbackNotEnoughPrecified.value = !data.allPrecified && data.anyPrecified;
			isFeedbackNotPrecified.value = !data.allPrecified && !data.anyPrecified;
			totalSmart.value = data.totalSmart;
			currentPage.value = data.pagination.page === 0 ? 1 : data.pagination.page + 1;
			totalPage.value = data.pagination.totalPages;
			itemsSelectedTable.value = [...data.selectedItems];
			smartSelecteds.value = [...data.selectedItems];
			totalReceivablesProduct.value = data.pagination.totalItems;
			roofIds.value = data.roofIds;
			totalReceivablesSeleted.value = data.totalSmart;
			if (!hasParams()) {
				updatedScreen();
			}
			forceRenderTable.value = false;
			setTimeout(() => {
				isRenderFirst.value = false;
			}, 500);
		}

		function getPayloadReceivables() {
			const { idProduct, dateDisbursement, valueSeleted } =
				operationCessionProductCache.value;
			if (hasParams()) {
				return {
					...filterTable.value,
					disbursementDate: dateDisbursement,
					vehicleId: vehicleId.value,
					operationId: operationId.value,
				};
			}
			return {
				...filterTable.value,
				roof: valueSeleted,
				disbursementDate: dateDisbursement,
				vehicleId: idProduct,
			};
		}

		function callbackUpdatedPricing(): void {
			const payload = getPayloadReceivables();
			getReceivables(payload, callbackGetReceivables);
		}

		function callPricing(): void {
			const { idProduct, dateDisbursement } = operationCessionProductCache.value;
			const payload = {
				vehicleId: idProduct,
				disbursementDate: dateDisbursement,
			};
			updatedPricing(payload, callbackUpdatedPricing);
		}

		function load(): void {
			if (!hasParams() && !hasDataCache()) {
				redirectHome();
				return;
			}
			if (hasParams()) {
				hiddenButtonBack.value = true;
				getInformationCession(operationId.value, updatedDataDetailCessionAPI);
				return;
			}
			hiddenButtonBack.value = false;
			callPricing();
		}

		onMounted(() => {
			if (operationCessionProductCache.value === null) {
				onClickBack();
				return;
			}
			load();
		});

		return {
			isLoading,
			isError,
			isFeedbackReceivables,
			isFeedbackLimit,
			isFeedbackValue,
			isModalProductSeleted,
			isModalDetailCession,
			isFilterApplied,
			isFilterShowing,
			isMinValue,
			isMaxValue,
			isModalCloseCession,
			isCleanCheckbox,
			isModalReceivablesApproved,
			isDisabledButton,
			isErrorReceivables,
			isRenderFirst,
			draweeName,
			headerData,
			dataModalDetailCession,
			dataFeedbackValue,
			productCurrent,
			totalValueDisbursement,
			labelResultTotal,
			totalSeleted,
			totalPage,
			currentPage,
			receivables,
			labelMinValue,
			labelMaxValue,
			detailReceivablesApproved,
			selectedTable,
			itemsSelectedTable,
			forceRenderTable,
			currencyUnmask,
			isFeedbackNotPrecified,
			isFeedbackNotEnoughPrecified,
			hiddenButtonBack,
			onClickChangeDates,
			onCloseModalProductSeleted,
			onSetNewValue,
			onOpenModalDetailsCession,
			onCloseModalModalDetailCession,
			onClickButtonImport,
			onFiltersApplied,
			onApplyFilter,
			onClickMainFilter,
			onInputMainFilter,
			onUncheckReceivables,
			onClickReload,
			onOpenModalCloseCession,
			onCloseModalCloseCession,
			onClickBack,
			onClickConfirmFooter,
			onConfirmModalCloseCession,
			onRequest,
			onChangeCheckbox,
			onOpenModalDetails,
			onCloseModalReceivablesApproved
		};
	},
});
</script>
