<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="68" size="md" @onClose="onClose">
		<template v-slot:header>
			<farm-dialog-header title="Termos e Informações" @onClose="onClose" />
		</template>
		<template v-slot:content>
			<farm-typography class="mb-2" size="12px" tag="p" weight="400">
				Certifique-se de que as informações acima estão corretas antes de concluir a criação
				da sua operação.
			</farm-typography>

			<farm-typography class="mb-4" size="12px" tag="p" weight="400">
				O desembolso estará condicionado à
				<farm-typography size="12px" tag="span" weight="700" color="black">
					verificação de disponibilidade de limite de crédito e de recursos do veículo
					financeiro</farm-typography
				>, assim como à
				<farm-typography size="12px" tag="span" weight="700" color="black"
					>verificação de critérios</farm-typography
				>
				estabelecidos no regulamento do veículo financeiro e variações de taxas de
				desconto/mercado aplicadas à operação.
			</farm-typography>
			<farm-typography class="mb-4" size="12px" tag="p" weight="400">
				Você poderá acompanhar o andamento da sua operação na opção "Cessões" no menu
				superior.
			</farm-typography>
		</template>
		<template v-slot:footer>
			<farm-dialog-footer
				confirmLabel="Fechar"
				:hasCancel="false"
				@onConfirm="onClose"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
	name: 'modal-information-important',
	props: {
		value: {
			type: Boolean,
			required: true,
		},
	},
	setup(props, { emit }) {
		const inputModel = ref(false);

		function onClose(): void {
			emit('onClose');
		}

		return {
			inputModel,
			onClose,
		};
	},
});
</script>
