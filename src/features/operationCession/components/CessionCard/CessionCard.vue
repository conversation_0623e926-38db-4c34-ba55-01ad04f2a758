<template>
	<farm-card>
		<farm-card-content>
			<div class="cession-card-content">
				<div class="cession-card-product">
					<farm-idcaption :copy-text="item.id.toString()" tooltipColor="gray" no-height class="mb-1">
						<template v-slot:subtitle>
							<farm-caption :variation="'semiBold'" color="black">
								ID: {{ item.id }}
							</farm-caption>
						</template>
					</farm-idcaption>
					<farm-idcaption no-height>
						<template v-slot:title>
							<farm-bodysmall  :variation="'bold'" tag="span" color="gray" color-variation="darken">
								Produto:
							</farm-bodysmall>
							<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
								{{ item.commercialProdName || 'N/A' }}
							</farm-bodysmall>
						</template>
					</farm-idcaption>
					<farm-idcaption no-height>
						<template v-slot:title>
							<farm-bodysmall  :variation="'bold'" tag="span" color="gray" color-variation="darken">
								Tipo:
							</farm-bodysmall>
							<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
								{{ item.sectionTypeName || 'N/A' }}
							</farm-bodysmall>
						</template>
					</farm-idcaption>
				</div>
				<div class="cession-card-center">
					<div class="cession-card-flex">
						<div class="cession-card-separation"></div>
						<div class="mr-4">
							<farm-idcaption no-height>
								<template v-slot:title>
									<farm-bodysmall  :variation="'bold'" tag="span" color="gray" color-variation="darken">
										<b>Recebíveis:</b>
									</farm-bodysmall>
									<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
										{{ item.receivables || 'N/A' }}
									</farm-bodysmall>
								</template>
							</farm-idcaption>
							<farm-idcaption
								:link="true"
								:buttonsColor="item.failOnCreation || item.approved === 0 ? 'netral': 'primary'"
								no-height
								@onLinkClick="item.failOnCreation || item.approved === 0 ? null: onOpenModalReceivablesApproved(item)"
							>
								<template v-slot:title>
									<farm-bodysmall  :variation="'bold'" tag="span" color="gray" color-variation="darken">
										<b>Aprovados:</b>
									</farm-bodysmall>
									<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
										{{ item.failOnCreation ? '-' : item.approved }}
									</farm-bodysmall>
								</template>
							</farm-idcaption>
							<farm-idcaption
								:link="true"
								:buttonsColor="item.failOnCreation || item.refused === 0 ? 'netral': 'primary'"
								no-height
								@onLinkClick="item.failOnCreation || item.refused === 0 ? null: onOpenModalDenyReasons(item)"
							>
								<template v-slot:title>
									<farm-bodysmall  :variation="'bold'" tag="span" color="gray" color-variation="darken">
										<b>Recusados:</b>
									</farm-bodysmall>
									<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
										{{ item.failOnCreation ? '-' : item.refused }}
									</farm-bodysmall>
								</template>
							</farm-idcaption>
						</div>
					</div>
					<div class="cession-card-flex">
						<div class="cession-card-separation"></div>
						<div class="mr-4">
							<farm-idcaption no-height>
								<template v-slot:title>
									<farm-bodysmall  :variation="'bold'" tag="span" color="gray" color-variation="darken">
										<b>Criação:</b>
									</farm-bodysmall>
									<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
										{{ formatDate(cleanDate(item.createdAt)) || 'N/A' }}
									</farm-bodysmall>
								</template>
							</farm-idcaption>
							<farm-idcaption no-height>
								<template v-slot:title>
									<farm-bodysmall :variation="'bold'" tag="span" color="gray" color-variation="darken">
										<b>Atualização:</b>
									</farm-bodysmall>
									<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
										{{ formatDate(cleanDate(item.updatedAt)) || 'N/A' }}
									</farm-bodysmall>
								</template>
							</farm-idcaption>
							<farm-idcaption no-height>
								<template v-slot:title>
									<farm-bodysmall :variation="'bold'" tag="span" color="gray" color-variation="darken">
										<b>Desembolso:</b>
									</farm-bodysmall>
									<farm-bodysmall  :variation="'medium'" tag="span" color="gray" color-variation="darken">
										{{ formatDate(cleanDate(item.dateDisbursement)) || 'N/A' }}
									</farm-bodysmall>
								</template>
							</farm-idcaption>
						</div>
					</div>
					<div class="cession-card-flex">
						<div class="cession-card-separation"></div>
						<div class="mr-4">
							<farm-caption>
								<farm-tooltip>
									<span> Valor nominal dos recebíveis da operação. </span>
									<template v-slot:activator>
										<farm-caption :variation="'semiBold'" color="black" tag="span">
											Valor Nominal
										</farm-caption>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-caption>
							<farm-caption :variation="'medium'" color="gray" color-variation="darken">
								{{ formatMoney(item.valueNominal) || 'N/A' }}
							</farm-caption>
						</div>
					</div>
					<div class="cession-card-flex">
						<div class="cession-card-separation"></div>
						<div class="mr-4">
							<farm-caption>
								<farm-tooltip>
									<span> Valor nominal menos o valor do excedente. </span>
									<template v-slot:activator>
										<farm-caption :variation="'semiBold'" color="black" tag="span">
											Valor Livre
										</farm-caption>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-caption>
							<farm-caption :variation="'medium'" color="gray" color-variation="darken">
								{{ formatMoney(item.totalFreeValue) || 'N/A' }}
							</farm-caption>
						</div>
					</div>

					<div class="cession-card-flex">
						<div class="cession-card-separation"></div>
						<div class="mr-4">
							<farm-caption>
								<farm-tooltip>
									<span> Valor desembolsado/a desembolsar da operação. </span>
									<template v-slot:activator>
										<farm-caption :variation="'semiBold'" color="black" tag="span">
											Valor Liquido
										</farm-caption>
										<farm-icon size="sm" color="gray">help-circle</farm-icon>
									</template>
								</farm-tooltip>
							</farm-caption>
							<farm-caption :variation="'medium'" color="gray" color-variation="darken">
								{{ formatMoney(item.totalLiquidValue) || 'N/A' }}
							</farm-caption>
						</div>
					</div>
				</div>
				<div class="cession-card-flex">
					<div class="cession-card-right">
						<cession-tooltip :status="item.sectionStatus">
							<cession-status :status="item.sectionStatus" class="mr-2" />
						</cession-tooltip>
						<farm-btn
							icon
							:disabled="item.sectionStatus === 2 || item.sectionStatus === 9"
							@click="onRedirect(item)"
						>
							<farm-icon color="primary" size="md"> open-in-new</farm-icon>
						</farm-btn>
					</div>
				</div>
			</div>
		</farm-card-content>
	</farm-card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { defaultDateFormat, brl } from '@farm-investimentos/front-mfe-libs-ts';

import CessionStatus from '../CessionStatus';
import CessionTooltip from '../CessionTooltip';

export default defineComponent({
	name: 'cession-card',
	components: {
		CessionStatus,
		CessionTooltip,
	},
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	setup(_, { emit }) {
		function onOpenModalDenyReasons(data): void {
			emit('onOpenModalDenyReasons', data);
		}

		function onOpenModalReceivablesApproved(data): void {
			emit('onOpenModalReceivablesApproved', data);
		}

		function onRedirect(data): void {
			emit('onRedirect', data);
		}

		function cleanDate(data) {
			if (data.includes('T')) {
				const dataSplit = data.split('T')[0];
				return dataSplit;
			}
			return data;
		}

		return {
			formatDate: defaultDateFormat,
			formatMoney: brl,
			cleanDate,
			onOpenModalDenyReasons,
			onOpenModalReceivablesApproved,
			onRedirect,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './CessionCard';
</style>
