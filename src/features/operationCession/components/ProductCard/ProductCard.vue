<template>
	<farm-card>
		<farm-card-content>
			<div class="product-card-center">
				<div class="mb-3">
					<farm-bodytext :type="2" bold>
						{{ item.name }}
					</farm-bodytext>
				</div>
				<div class="product-card-center mb-4">
					<farm-caption variation="regular" color="gray">
						Limite Disponível
					</farm-caption>
					<farm-heading :type="6">
						{{ formatMoney(item.availableLimit) || 'N/A' }}
					</farm-heading>
				</div>
				<div class="product-card-center mb-4">
					<farm-caption variation="regular" color="gray"> Limite Utilizado</farm-caption>
					<farm-bodytext :type="2" bold>
						{{ formatMoney(item.usedLimit) || 'N/A' }}
					</farm-bodytext>
				</div>
				<div class="product-card-center mb-4">
					<farm-caption variation="regular" color="gray">
						Validade do Limite
					</farm-caption>
					<farm-bodytext :type="2" bold>
						{{ formatDate(item.dueDateLimit) || 'N/A' }}
					</farm-bodytext>
				</div>
				<div class="mb-3">
					<farm-col>
						<farm-row>
							<farm-btn
								class="product-card-button"
								color="primary"
								:disabled="isCheckPaymentAccount(item)"
								@click="onClickSelected(item, 'account')"
							>
								Receber em Conta
								<img
									src="@/assets/icons/bank-account-icon.svg"
									alt="pay supplier icon"
								/>
							</farm-btn>
						</farm-row>
						<farm-row class="mt-3">
							<farm-btn
								class="product-card-button"
								color="primary"
								:disabled="item.isPaymentProviders"
								@click="onClickSelected(item, 'providers')"
							>
								Pagar Fornecedor
								<img
									src="@/assets/icons/supplier-payment-icon.svg"
									alt="pay supplier icon"
								/>
							</farm-btn>
						</farm-row>
					</farm-col>
					<product-selection-modal
						v-if="isOpenModal"
						v-model="isOpenModal"
						:productCurrent="productCurrent"
						@onCloseModal="onCloseModal"
					/>
				</div>
			</div>
		</farm-card-content>
	</farm-card>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';

import { brl, defaultDateFormat, environment } from '@farm-investimentos/front-mfe-libs-ts';

import { useSelectedProductId } from '@/composible';
import { useAnalytics } from '@/composible/useAnalytics';
import { useModal } from '@/composible/useModal';

import { PAYMENT_ACCOUNT, PAYMENT_PROVIDER } from '../../constants';
import ProductSelectionModal from '../ProductSelectionModal';

const notPaymentAccount = {
	production: {
		id: 116142,
	},
	stage: {
		id: 121007,
	},
};
const commercialProduct = 118000;

export default defineComponent({
	name: 'product-card',
	components: {
		ProductSelectionModal,
	},
	props: {
		item: {
			type: Object,
			required: true,
		},
	},
	setup() {
		const { isOpenModal, onCloseModal, onOpenModal } = useModal();
		const { trigger } = useAnalytics();
		const productId = useSelectedProductId().value;
		const productCurrent = ref(null);

		function onClickSelected(item, type): void {
			const typePayment = {
				account: {
					operationId: PAYMENT_ACCOUNT.id,
					operationName: PAYMENT_ACCOUNT.name,
				},
				providers: {
					operationId: PAYMENT_PROVIDER.id,
					operationName: PAYMENT_PROVIDER.name,
				},
			};
			productCurrent.value = {
				...item,
				...typePayment[type],
			};

			const dataTrigger = {
				step: 1,
				event: 'operation_cession',
				payload: {
					description: 'clicou no botão selecionar do produto comercial',
					data: {
						...item,
						...typePayment[type],
						limit: item.usedLimit,
					},
				},
			};
			trigger(dataTrigger);
			onOpenModal();
		}

		function isCheckPaymentAccount(item): boolean {
			let env = 'production';
			const isUAT = environment.apiSuperCessaoV2.match(/uat/g);
			if (isUAT && isUAT.length > 0) {
				env = 'stage';
			}
			if (item.id === commercialProduct && notPaymentAccount[env].id === productId) {
				return true;
			}
			return item.isPaymentAccount;
		}

		return {
			isOpenModal,
			isCheckPaymentAccount,
			productCurrent,
			formatMoney: brl,
			formatDate: defaultDateFormat,
			onCloseModal,
			onClickSelected,
		};
	},
});
</script>

<style lang="scss" scoped>
@import './ProductCard';
</style>
