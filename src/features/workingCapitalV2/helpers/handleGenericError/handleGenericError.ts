import { ERROR_MESSAGES } from '@/features/workingCapitalV2/constants/errorMessages';

export function handleGenericError(
	error: any,
	fallbackMessage: string = ERROR_MESSAGES.UNEXPECTED_ERROR,
	codeMessage?: string
): string[] {
	const errorMessages = error?.response?.data?.errors || [fallbackMessage];
	const regex = /\([A-Za-z0-9]+\)$/;

	const codesFound = errorMessages
		.map((msg: string) => {
			const match = msg.match(regex);
			return match ? match[0] : null;
		})
		.filter(code => code !== null);
	if (codesFound.length > 0) {
		const allCodes = codesFound.join(' ');
		const messageToUse = codeMessage || ERROR_MESSAGES.GENERIC_NOT_FOUND;
		return [`${messageToUse} ${allCodes}`];
	}
	return [fallbackMessage];
}
