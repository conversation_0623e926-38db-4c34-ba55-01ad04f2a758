# Documentação: Lógica de Exibição de Erros - Working Capital V2

## 1. Visão Geral do Sistema de Erros

O sistema de tratamento de erros do Working Capital V2 utiliza uma arquitetura centralizada onde todos os erros são direcionados para modais específicos através de um fluxo bem definido de propagação de eventos.

### Arquitetura Principal
- **Ponto Central**: `OperationCreate.vue` coordena todos os modais de erro
- **Modal Principal**: `WorkingCapitalErrorModal.vue` exibe os erros ao usuário
- **Propagação**: Erros são propagados via eventos (`@open-error-modal`) entre componentes filhos

## 2. Componentes Principais

### 2.1 OperationCreate.vue (`/views/OperationCreate/OperationCreate.vue`)

**Responsabilidades:**
- Coordenação central de todos os modais de erro
- Gerenciamento do estado `isOpenErrorModal` e `errorMessages`
- Handler principal `onError()` que recebe mensagens de erro

**Pontos de Erro:**
```typescript
// Função principal de tratamento de erros
const onError = (messages = []) => {
    errorMessages.value = messages;
    onOpenErrorModal();
    onCloseProductsModal();
};

// Watchers para erros automáticos
watch(isError, () => {
    if (isError.value) {
        onError(); // Erro sem mensagens específicas
    }
});
```

### 2.2 OperationForm.vue (`/components/OperationForm/OperationForm.vue`)

**Responsabilidades:**
- Formulário principal com múltiplos pontos de validação
- Emissão de eventos de erro para o componente pai
- Tratamento de erros específicos por contexto

**Principais Pontos de Erro:**
```typescript
// Função de abertura de modal de erro
const openErrorModal = (condition, errorMessages = []) => {
    if (condition) {
        if (errorMessages.length > 0) {
            emit('open-error-modal', errorMessages);
        } else {
            onOpenErrorModal();
            emit('has-no-info');
        }
    }
};

// Tratamento de erro na mutação de pricing
onError: (error) => {
    if (error?.response?.status === 400 && error?.response?.data?.errors) {
        const errorMessages = handlePreEligibilityError(error);
        openErrorModal(true, errorMessages);
    } else {
        openErrorModal(true);
    }
}
```

### 2.3 OperationSimulationResult.vue (`/components/OperationSimulationResult/OperationSimulationResult.vue`)

**Responsabilidades:**
- Simulação de pré-elegibilidade e criação de operações
- Tratamento de erros específicos de simulação
- Dois tipos de modais: erro genérico e erro específico

**Lógica de Detecção de Erro:**
```typescript
const handleSimulatePreEligibilityError = (error: any) => {
    const errorMessages = error?.response?.data?.errors || [];
    
    const numericCodeRegex = /\(\d+\)$/;
    const hasNumericCodes = errorMessages.some((msg: string) => numericCodeRegex.test(msg));
    
    const alphanumericCodeRegex = /\([^)]*[A-Za-z][^)]*\)$/;
    const hasAlphanumericCodes = errorMessages.some((msg: string) => alphanumericCodeRegex.test(msg));
    
    if (hasAlphanumericCodes) {
        return {
            messages: handleGenericError(error),
            useGenericModal: true 
        };
    } else if (hasNumericCodes) {
        return {
            messages: handlePreEligibilityError(error),
            useGenericModal: false 
        };
    } else {
        return {
            messages: handlePreEligibilityError(error),
            useGenericModal: false 
        };
    }
};
```

### 2.4 WorkingCapitalErrorModal.vue (`/components/WorkingCapitalErrorModal/WorkingCapitalErrorModal.vue`)

**Responsabilidades:**
- Exibição centralizada de mensagens de erro
- Interface padronizada com informações de contato
- Recebe array de mensagens via prop `errorMessages`

## 3. Tipos de Erros por Contexto

### 3.1 Erros de Produtos (`useCommercialProducts`)
- **Origem**: Composable `useCommercialProducts.ts:100-106`
- **Trigger**: Falha no carregamento de produtos comerciais ou limites
- **Tratamento**: Define `isWalletError` no store Vuex

```typescript
if (isErrorCommercialProducts.value || isErrorCommercialProductsLimits.value) {
    store.commit('dashboards/setIsWalletError', { isWalletError: true });
}
```

### 3.2 Erros de Campanhas (`useCampaigns`)
- **Origem**: Composable `useCampaigns.ts:27-29`
- **Callback**: `handleCampaignsError` define `campaignsError.value`
- **Propagação**: Watcher no `OperationForm.vue:687-691`

```typescript
const handleCampaignsError = (errorMessages: string[]) => {
    campaignsError.value = errorMessages;
};

watch(campaignsError, (newError) => {
    if (newError && newError.length > 0) {
        openErrorModal(true, newError);
    }
});
```

### 3.3 Erros de Fornecedores (`useSuppliers`)
- **Origem**: Composable `useSuppliers.ts`
- **Variáveis**: `suppliersError` e `bankAccountsError`
- **Propagação**: Watchers no `OperationForm.vue:693-703`

### 3.4 Erros de Datas
- **Contexto**: Busca de dias úteis e datas de vencimento
- **Tratamento**: `handleDueDatesQueryError` no `OperationForm.vue:479-482`
- **Modal Específico**: `WorkingCapitalDueDateErrorModal`

### 3.5 Erros de Simulação/Pricing
- **Origem**: Mutação `priceWorkingCapital` no `OperationForm.vue:519-526`
- **Helper**: `handlePreEligibilityError` para status 400
- **Fallback**: Modal genérico para outros erros

### 3.6 Erros de Pré-elegibilidade
- **Origem**: `simulatePreEligibility` no `OperationSimulationResult.vue:297-306`
- **Detecção**: `handleSimulatePreEligibilityError` analisa padrões de código
- **Resultado**: Direciona para modal apropriado

## 4. Helpers de Tratamento de Erros

### 4.1 handlePreEligibilityError (`/helpers/handlePreEligibilityError/handlePreEligibilityError.ts`)

**Responsabilidade**: Processa erros com códigos numéricos ou textos específicos

```typescript
export function handlePreEligibilityError(error: any) {
    const errorMessages = error?.response?.data?.errors || [ERROR_MESSAGES.UNEXPECTED_ERROR];
    const regex = /\(\d+\)$/;

    const isSecondScenario = errorMessages.some((msg: string) => regex.test(msg));
    if (isSecondScenario) {
        return errorMessages; // Retorna mensagens com códigos numéricos
    }

    // Processa mensagens por palavras-chave
    return errorMessages.map((msg: string) => {
        if (/Segmento|Segmentação/i.test(msg)) {
            return "Problema no segmento/classificação.";
        }
        if (/Fornecedor/i.test(msg)) {
            return "Problema no fornecedor.";
        }
        return ERROR_MESSAGES.UNEXPECTED_ERROR;
    });
}
```

### 4.2 handleGenericError (`/helpers/handleGenericError/handleGenericError.ts`)

**Responsabilidade**: Processa erros com códigos alfanuméricos

```typescript
export function handleGenericError(error: any, fallbackMessage: string = ERROR_MESSAGES.UNEXPECTED_ERROR, codeMessage?: string): string[] {
    const errorMessages = error?.response?.data?.errors || [fallbackMessage];
    const regex = /\([A-Za-z0-9]+\)$/;

    const codesFound = errorMessages
        .map((msg: string) => {
            const match = msg.match(regex);
            return match ? match[0] : null;
        })
        .filter(code => code !== null);
        
    if (codesFound.length > 0) {
        const allCodes = codesFound.join(' ');
        const messageToUse = codeMessage || ERROR_MESSAGES.GENERIC_NOT_FOUND;
        return [`${messageToUse} ${allCodes}`];
    }
    return [fallbackMessage];
}
```

## 5. Fluxo de Propagação de Erros

### 5.1 Fluxo Principal
```
API Error → Composable/Query → Component Handler → Event Emission → OperationCreate → Modal Display
```

### 5.2 Exemplos de Fluxo

**Erro de Campanha:**
```
useCampaigns query error → handleCampaignsError → campaignsError.value → 
OperationForm watcher → openErrorModal → emit('open-error-modal') → 
OperationCreate onError → WorkingCapitalErrorModal
```

**Erro de Pré-elegibilidade:**
```
simulatePreEligibility mutation error → handleSimulatePreEligibilityError → 
análise de padrão → handlePreEligibilityError ou handleGenericError → 
modal específico ou genérico
```

### 5.3 Pontos de Emissão de Eventos

**OperationForm → OperationCreate:**
- `@open-error-modal`: Erros gerais com mensagens
- `@has-no-info`: Indica ausência de dados (ativa ContactCard)

**Outros Componentes → OperationCreate:**
- `@on-error`: Utilizado por modais filhos (OriginationMachineProductsModal)

## 6. Configurações e Constantes

### 6.1 ERROR_MESSAGES (`/constants/errorMessages.ts`)
```typescript
export const ERROR_MESSAGES = {
    GENERIC_NOT_FOUND: 'Não foi possível encontrar nenhuma informação cadastrada para seleção',
    LIST_FETCH_ERROR: 'Erro ao buscar os dados da lista',
    UNEXPECTED_ERROR: 'Um erro inesperado ocorreu',
} as const;
```

### 6.2 Padrões de Regex
- **Códigos Numéricos**: `/\(\d+\)$/` - Ex: "Error message (123)"
- **Códigos Alfanuméricos**: `/\([^)]*[A-Za-z][^)]*\)$/` - Ex: "Error message (ABC123)"
- **Palavras-chave**: `/Segmento|Segmentação/i`, `/Fornecedor/i`

## 7. Estados de Modal

### 7.1 Modais Ativos
- `isOpenErrorModal`: Modal principal de erro
- `isOpenGenericErrorModal`: Modal para erros genéricos com códigos
- `isOpenDueDateErrorModal`: Modal específico para erros de data

### 7.2 Mensagens de Estado
- `errorMessages`: Array de mensagens para exibição
- `errorsArr`: Array específico para erros de simulação

## 8. Tratamentos Especiais

### 8.1 Detecção de Contexto
- Verifica se é Working Capital ou Origination Machine via `operationTypeId`
- Ajusta mensagens e comportamentos conforme o contexto

### 8.2 Fallbacks
- Mensagens padrão quando não há erros específicos
- Modal genérico quando não consegue classificar o tipo de erro
- ContactCard para indicar falta de informações

---

**Observações:**
- Este sistema centraliza o tratamento de erros garantindo consistência na UX
- A análise por regex permite classificação automática do tipo de erro
- A propagação via eventos mantém o desacoplamento entre componentes
- Os helpers permitem reutilização da lógica de tratamento em diferentes contextos