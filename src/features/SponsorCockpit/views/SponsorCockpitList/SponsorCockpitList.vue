<template>
	<farm-container>
		<SponsorCockpitSummary class="mb-6" :summary="sponsorCockpitSummary" />

		<farm-row align="center">
			<farm-col cols="12" md="6">
				<farm-form-mainfilter
					label="Buscar Nome, Documento ou ID"
					:show-filters="isOpenFilter"
					@onInputChange="handleSearchFilters"
					@onClick="onClickMainFilter"
				/>
			</farm-col>
			<farm-col cols="12" md="6" align="right">
				<farm-btn v-if="canReprocess" plain class="mr-2" @click.stop="onOpenReprocessModal">
					Reprocessar
				</farm-btn>
				<farm-btn outlined :disabled="isDisabled" @click.stop="onExportSponsorCockpit">
					<farm-icon size="md">download-outline</farm-icon> Exportar
				</farm-btn>
			</farm-col>
		</farm-row>

		<collapse-transition :duration="300">
			<farm-row v-show="isOpenFilter" class="mb-6">
				<farm-col cols="12" md="3">
					<farm-label for="filter-status"> Status </farm-label>
					<farm-select
						id="filter-status"
						v-model="filters.status"
						:items="status || []"
						item-text="desc"
						item-value="id"
						multiple
					/>
				</farm-col>
				<farm-col cols="12" md="3">
					<farm-label for="filter-import-type"> Tipo de Importação </farm-label>
					<farm-select
						id="filter-import-type"
						v-model="filters.source"
						:items="sources || []"
						item-text="desc"
						item-value="id"
						multiple
					/>
				</farm-col>
				<farm-col cols="12">
					<farm-btn-confirm
						outlined
						class="farm-btn--responsive ml-0"
						title="Aplicar Filtros"
						@click="handleApplyFilters"
						
					>
						Aplicar Filtros
					</farm-btn-confirm>
					<farm-btn
						plain
						depressed
						class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
						title="Limpar Filtros"
						@click="handleCleanFilters"
					>
						Limpar Filtros
					</farm-btn>
				</farm-col>
			</farm-row>
		</collapse-transition>

		<div class="mx-n6">
			<farm-line no-spacing />
			<SponsorCockpitListTable
				:sponsorCockpitList="sponsorCockpitList"
				@onSort="onSortSelect"
			/>
			<farm-datatable-paginator
				v-if="sponsorCockpitList.length > 0"
				class="mt-6 mb-n6"
				:page="page"
				:totalPages="pagination.totalPages"
				:initialLimitPerPage="pagination.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</div>

		<ReprocessModal
			ref="reprocessModalRef"
			v-model="isReprocessModalOpen"
			@onClose="onOpenCancelModal"
			@onReprocessed="onReprocessed"
			@onReprocessError="onReprocessError"
		/>
		<ReprocessCancelModal
			v-model="isCancelModalOpen"
			@onBack="onBackToReprocess"
			@onConfirm="onConfirmCancel"
		/>
		<ReprocessSuccessModal
			v-model="isReprocessSuccessModalOpen"
			v-if="isReprocessSuccessModalOpen"
			:type="reprocessModalType"
			@onClose="onCloseReprocessSuccessModal"
		/>

		<farm-loader mode="overlay" v-if="isLoading" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';

import { useModal } from '@/composible/useModal';
import ReprocessCancelModal from '@/features/SponsorCockpit/components/ReprocessCancelModal';
import ReprocessModal from '@/features/SponsorCockpit/components/ReprocessModal';
import ReprocessSuccessModal from '@/features/SponsorCockpit/components/ReprocessSuccessModal';
import SponsorCockpitListTable from '@/features/SponsorCockpit/components/SponsorCockpitListTable';
import SponsorCockpitSummary from '@/features/SponsorCockpit/components/SponsorCockpitSummary';
import { useSources } from '@/features/SponsorCockpit/composables';
import useSponsorCockpitExport from '@/features/SponsorCockpit/composables/useSponsorCockpitExport';
import useSponsorCockpitList from '@/features/SponsorCockpit/composables/useSponsorCockpitList';
import useSponsorCockpitPageable from '@/features/SponsorCockpit/composables/useSponsorCockpitPageable';
import { useSponsorCockpitPermissions } from '@/features/SponsorCockpit/composables/useSponsorCockpitPermissions';
import useSponsorCockpitSummary from '@/features/SponsorCockpit/composables/useSponsorCockpitSummary';
import useStatus from '@/features/SponsorCockpit/composables/useStatus';

const ORIGINATION_OPERATION_TYPE = 2;

export default defineComponent({
	name: 'SponsorCockpitList',
	components: {
		SponsorCockpitSummary,
		SponsorCockpitListTable,
		ReprocessModal,
		ReprocessCancelModal,
		ReprocessSuccessModal,
	},
	setup() {
		const reprocessModalRef = ref(null);
		
		const filters = ref({
			status: [],
			source: [],
		});
		const sort = ref({
			order: 'DESC',
			orderby: 'updatedAt',
		});

		const {
			getSponsorCockpitList,
			isLoadingSponsorCockpitList,
			sponsorCockpitPagination,
			sponsorCockpitList,
		} = useSponsorCockpitList(ORIGINATION_OPERATION_TYPE);

		const { exportSponsorCockpit } = useSponsorCockpitExport();

		const { getSponsorCockpitSummary, sponsorCockpitSummary, isLoadingSponsorCockpitSummary } =
			useSponsorCockpitSummary(ORIGINATION_OPERATION_TYPE);

		const { getStatus, status, isLoadingStatus } = useStatus();

		const { getSources, sources, isLoadingSources } = useSources();

		const { canReprocess } = useSponsorCockpitPermissions();

		const {
			page,
			pagination,
			onChangePage,
			onChangePageLimit,
			onSortSelect,
			onInputChangeMainFilter,
			onApplyFilter,
			isOpenFilter,
			onClickMainFilter,
		} = useSponsorCockpitPageable(
			{
				sort: sort.value,
				lowercaseSort: true,
				keyInputSearch: 'operation',
				filters,
				charInputSearch: 2,
				calbackFn: params => {
					getSponsorCockpitList(params);
				},
			},
			sponsorCockpitPagination
		);

		const {
			isOpenModal: isReprocessModalOpen,
			onOpenModal: onOpenReprocessModal,
			onCloseModal: onCloseReprocessModal,
		} = useModal();

		const {
			isOpenModal: isCancelModalOpen,
			onOpenModal: onOpenCancelModal,
			onCloseModal: onCloseCancelModal,
		} = useModal();

		const {
			isOpenModal: isReprocessSuccessModalOpen,
			onOpenModal: onOpenReprocessSuccessModal,
			onCloseModal: onCloseReprocessSuccessModal,
		} = useModal();

		const reprocessModalType = ref('success');

		const isDisabled = computed(() => {
			return isLoading.value || !sponsorCockpitList.value?.length;
		});

		const isLoading = computed(() => {
			return (
				isLoadingSponsorCockpitList.value ||
				isLoadingStatus.value ||
				isLoadingSponsorCockpitSummary.value ||
				isLoadingSources.value
			);
		});

		const onReprocessed = () => {
			reprocessModalType.value = 'success';
			onCloseReprocessModal();
			onOpenReprocessSuccessModal();
		};

		const onReprocessError = () => {
			reprocessModalType.value = 'error';
			onCloseReprocessModal();
			onOpenReprocessSuccessModal();
		};

		const onBackToReprocess = () => {
			// Fecha o modal de cancelamento e volta para o modal de reprocessamento
			onCloseCancelModal();
		};

		const onConfirmCancel = () => {
			// Limpa a seleção do modal de reprocessamento
			if (reprocessModalRef.value && reprocessModalRef.value.selection) {
				reprocessModalRef.value.selection.clearSelection();
			}
			// Fecha ambos os modais quando confirma o cancelamento
			onCloseCancelModal();
			onCloseReprocessModal();
		};

		function handleSearchFilters(value: string) {
			onInputChangeMainFilter(value);
		}

		function handleApplyFilters() {
			onApplyFilter(filters.value);
		}

		function handleCleanFilters() {
			filters.value.status = [];
			filters.value.source = [];
			onApplyFilter(filters.value);
		}

		function onExportSponsorCockpit(): void {
			exportSponsorCockpit(ORIGINATION_OPERATION_TYPE, filters.value);
		}

		onMounted(() => {
			getSponsorCockpitList({
				page: 0,
				limit: 10,
				order: sort.value.order,
				orderby: sort.value.orderby
			});
			getSponsorCockpitSummary();
			getStatus();
			getSources();
		});

		return {
				reprocessModalRef,
				page,
				pagination,
				isOpenFilter,
				isLoading,
				isDisabled,
				filters,
				sponsorCockpitList,
				status,
				sources,
				handleSearchFilters,
				handleApplyFilters,
				handleCleanFilters,
				canReprocess,
				onChangePage,
				onChangePageLimit,
				onClickMainFilter,
				onSortSelect,
				sponsorCockpitSummary,
				onExportSponsorCockpit,
				onOpenReprocessModal,
				ORIGINATION_OPERATION_TYPE,
				isReprocessModalOpen,
				onCloseReprocessModal,
				onReprocessed,
				isCancelModalOpen,
				onOpenCancelModal,
				onCloseCancelModal,
				onBackToReprocess,
				onConfirmCancel,
				onOpenReprocessSuccessModal,
				onCloseReprocessSuccessModal,
				isReprocessSuccessModalOpen,
				reprocessModalType,
				onReprocessError,
			};
	},
});
</script>
