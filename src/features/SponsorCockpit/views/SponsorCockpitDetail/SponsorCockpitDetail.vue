<template>
	<farm-container>
		<farm-col cols="12">
			<farm-row align="center" justify="space-between">
				<farm-heading type="6">Detalhes da Operação</farm-heading>
				<div>
					<farm-caption tag="span" color="black" color-variation="30" variation="regular">
						Última atualização:
					</farm-caption>
					<farm-caption tag="span">
						{{ formatDateOrNA(sponsorCockpitDetails.updatedAt) }}
					</farm-caption>
				</div>
			</farm-row>
		</farm-col>

		<farm-row extra-decrease>
			<farm-line no-spacing class="my-6" />
		</farm-row>

		<farm-heading type="6">Status e atualizações</farm-heading>
		<farm-logger :items="statusWithExtraMessage" :vertical="false" align="left" class="py-6 custom-logger"/>
		

		<farm-row extra-decrease>
			<farm-tabs
				class="mb-6 mt-n6"
				:tabs="availableTabs"
				:allowUserChange="true"
				:showCounter="false"
				:initialSelect="initialTabIndex"
				@update="updateTab"
			/>
		</farm-row>
		<farm-row>
			<farm-col cols="12">
				<SponsorCockpitDetailContent
					v-if="currentTab === 'detalhes'"
					:details="sponsorCockpitDetails.details"
				/>
			</farm-col>
			<farm-col cols="12">
				<SponsorCockpitDetailSupplier
					v-if="currentTab === 'fornecedor'"
					:suppliers="sponsorCockpitDetails.providers"
				/>
			</farm-col>
			<farm-col cols="12">
<TabFormalization
v-if="currentTab === 'formalizacao'"
:formalization="formalization"
:isLoading="isLoadingFormalization"
:isError="isErrorFormalization"
:downloadContext="'sponsor'"
:operationId="operationId"
/>
			</farm-col>
			<farm-col cols="12" v-if="canReprocess && currentTab === 'log'">
				<SponsorCockpitDetailLog :operationId="operationId" />
			</farm-col>
		</farm-row>
		<div class="mx-n6 my-6">
			<farm-line no-spacing />
		</div>
		<farm-row>
			<farm-col class="d-flex justify-end" cols="12">
				<farm-btn to="/admin/meusclientes/wallet/originacoes_sacados">Voltar</farm-btn>
			</farm-col>
		</farm-row>
		<farm-loader v-if="isLoading" mode="overlay" />
	</farm-container>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';

import TabFormalization from '@/components/TabFormalization';
import { useRouter, useRoute } from '@/composible';
import { useFormalization } from '@/composible/useFormalization';
import SponsorCockpitDetailContent from '@/features/SponsorCockpit/components/SponsorCockpitDetailContent';
import SponsorCockpitDetailLog from '@/features/SponsorCockpit/components/SponsorCockpitDetailLog';
import SponsorCockpitDetailSupplier from '@/features/SponsorCockpit/components/SponsorCockpitDetailSupplier';
import useSponsorCockpitDetails from '@/features/SponsorCockpit/composables/useSponsorCockpitDetails';
import { useSponsorCockpitPermissions } from '@/features/SponsorCockpit/composables/useSponsorCockpitPermissions';
import useTotalSignsGetter from '@/features/SponsorCockpit/composables/useTotalSignsGetter';
import { formatDateOrNA } from '@/helpers/formatCards';

import { detailsTabs, detailsTabsWithLog } from '../../configurations/tabs';

export default defineComponent({
	components: {
		SponsorCockpitDetailContent,
		SponsorCockpitDetailSupplier,
		TabFormalization,
		SponsorCockpitDetailLog,
	},
	props: {},
	setup() {
		const currentTab = ref('detalhes');
		const router = useRouter();
		const route = useRoute();
		const operationId = ref(Number(route.params.operationId));

		const { canReprocess } = useSponsorCockpitPermissions();
		const availableTabs = computed(() =>
			canReprocess.value ? detailsTabsWithLog : detailsTabs
		);

		const initialTabIndex = computed(() => {
			const path = route.query.path as string || 'detalhes';
			const index = availableTabs.value.findIndex(tab => tab.path === path);
			return index !== -1 ? index : 0;
		});

		const { numberOfTotalSignatories, numberOfTotalSignedSignatories } = useTotalSignsGetter();

		const formalizationExtraMessage = computed(() => {
			const message =
				numberOfTotalSignatories.value > 0
					? `Aguardando assinatura (${numberOfTotalSignedSignatories.value}/${numberOfTotalSignatories.value})`
					: '';
			return message;
		});

		const statusWithExtraMessage = computed(() => {
			if (!sponsorCockpitDetails.value?.status) return [];

			const hasFormalizationInHistory = sponsorCockpitDetails.value.status.some(
				status => status.message === 'Formalização' && status.status === 'success'
			);

			return sponsorCockpitDetails.value.status.map(status => {
				const shouldShowExtra =
					status.message === 'Formalização' &&
					!hasFormalizationInHistory &&
					formalizationExtraMessage.value &&
					status.status !== 'success';

				if (shouldShowExtra) {
					return {
						...status,
						extraMessage: formalizationExtraMessage.value,
					};
				}
				return status;
			});
		});

		const { getSponsorCockpitDetails, isLoadingSponsorCockpitDetails, sponsorCockpitDetails } =
			useSponsorCockpitDetails();

		const { formalization, getFormalization, isLoadingFormalization, isErrorFormalization } =
			useFormalization('sponsor');

		const isLoading = computed(
			() => isLoadingSponsorCockpitDetails.value || isLoadingFormalization.value
		);
		onMounted(() => {
			let path = route.query.path as string;
			if (!path) {
				path = 'detalhes';
				router.replace({
					path: route.path,
					query: { path: path },
				});
			}
			currentTab.value = path;
			getSponsorCockpitDetails(+operationId.value);
			getFormalization(+operationId.value);
		});

		function updateTab(item): void {
			if (!item) {
				return;
			}

			if (item.path === currentTab.value) {
				return;
			}
			currentTab.value = item.path;
			router.replace({
				query: {
					path: item.path,
				},
			});
		}

		return {
			currentTab,
			availableTabs,
			initialTabIndex,
			updateTab,
			sponsorCockpitDetails,
			formatDateOrNA,
			isLoading,
			formalization,
			isLoadingFormalization,
			isErrorFormalization,
			statusWithExtraMessage,
			operationId,
			canReprocess,
		};
	},
});
</script>
<style lang="scss" scoped>
::v-deep .logger--horizontal {
	overflow-x: hidden;
	gap: 40px !important;
	.logger__divider {
		background: var(--farm-stroke-base) !important;
	}

	
}

::v-deep .logger__item.logger__item--default .farm-icon {
	background-color: #fff !important;

	&::before {
		color: var(--farm-bw-black-50) !important;
	}
}

::v-deep .logger__item {
	max-width: 100px;
}
</style>
