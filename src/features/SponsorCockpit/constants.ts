export const SPONSOR_COCKPIT_STATUSES = {
	UNDER_ANALYSIS: 1,
	READY_FOR_DISBURSEMENT: 2,
	DISBURSED: 3,
	CANCELED: 4,
	FORMALIZATION: 5,
	WAITING_FORMALIZATION: 6,
} as const;


export const SPONSOR_COCKPIT_LIST_CHIP_STATUSES = [	
	{
		id: SPONSOR_COCKPIT_STATUSES.UNDER_ANALYSIS,
		text: 'Em Análise',
		color: 'info',
	},
	{
		id: SPONSOR_COCKPIT_STATUSES.READY_FOR_DISBURSEMENT,
		text: 'Pronto para Desembolso',
		color: 'info',
	},
	{
		id: SPONSOR_COCKPIT_STATUSES.DISBURSED,
		text: 'Desembolsado',
		color: 'success',
	},
	
	{
		id: SPONSOR_COCKPIT_STATUSES.CANCELED,
		text: 'Cancelado',
		color: 'error',
	},
	
	{
		id: SPONSOR_COCKPIT_STATUSES.FORMALIZATION,
		text: 'Em Formalização',
		color: 'warning',
	},
	{
		id: SPONSOR_COCKPIT_STATUSES.WAITING_FORMALIZATION,
		text: 'Aguardando Formalização',
		color: 'warning',
	},

	
];

export const SPONSOR_COCKPIT_LIST_CHIP_STATUSES_ID_ENUM = SPONSOR_COCKPIT_LIST_CHIP_STATUSES.reduce(
	(accumulator, value) => ({
		...accumulator,
		[value.id]: {
			...value,
		},
	}),
	{}
);

