import { StatusIds, StatusMessages, StatusIcons } from '@/features/SponsorCockpit/constants/status';
import {
	SponsorCockpitDetailsRawData,
	Provider,
	StatusHistory,
	Status as LoggerStatusType,
} from '@/features/SponsorCockpit/types';
import formatDateAndHours from '@/helpers/formatDateAndHours';

export type SponsorCockpitDetails = {
	status: LoggerStatusType[];
	details: {
		id: number;
		campaignId: number;
		campaignName: string;
		tax: number;
		total: number;
		dueDate: string;
		receivableStartDueDate: string;
		receivableEndDueDate: string;
		draweeName: string;
		draweeDocument: string;
	};
	providers: Provider[];
	updatedAt: string;
};

export default function builderSponsorCockpitDetails(
	data: SponsorCockpitDetailsRawData
): SponsorCockpitDetails {
	const history = data.statusHistory || [];
	const currentStatusId = data.status;

	const hasStatusInHistory = (hist: StatusHistory[] | undefined, statusId: number): boolean =>
		hist?.some(status => status.statusId === statusId) ?? false;

	const getStatusDateFromHistory = (
		hist: StatusHistory[] | undefined,
		statusId: number
	): string | undefined => hist?.find(status => status.statusId === statusId)?.createdAt;

	const has = (statusId: number) => hasStatusInHistory(history, statusId);
	const getDate = (statusId: number) => getStatusDateFromHistory(history, statusId);
	const formatDate = (dateStr?: string) => (dateStr ? formatDateAndHours(dateStr) : '');

	function createStep(
		message: string,
		statusType: string,
		iconName: string,
		rawDate?: string
	): LoggerStatusType {
		return {
			message,
			status: statusType,
			icon: iconName,
			formattedDate: formatDate(rawDate),
		};
	}

	function getStepStatus(stepStatusId: number): { status: string; icon: string; date?: string } {
		const isCancelled = has(StatusIds.CANCELED);
		const isDisbursed = currentStatusId === StatusIds.DISBURSED;

		if (isCancelled) {
			return { status: 'success', icon: 'check', date: getDate(stepStatusId) };
		}

		if (isDisbursed) {
			return { status: 'success', icon: 'check', date: getDate(stepStatusId) };
		}

		if (has(stepStatusId)) {
			return { status: 'success', icon: 'check', date: getDate(stepStatusId) };
		}

		if (currentStatusId === stepStatusId) {
			return {
				status: 'info',
				icon: getIconForStatus(stepStatusId),
				date: getDate(stepStatusId),
			};
		}

		if (shouldBeActiveStep(stepStatusId, currentStatusId, history)) {
			return { status: 'info', icon: getIconForStatus(stepStatusId) };
		}

		return { status: 'default', icon: getIconForStatus(stepStatusId) };
	}

	function getIconForStatus(statusId: number): string {
		switch (statusId) {
			case StatusIds.UNDER_ANALYSIS:
				return StatusIcons.UNDER_ANALYSIS;
			case StatusIds.FORMALIZATION:
			case StatusIds.WAITING_FORMALIZATION:
				return StatusIcons.FORMALIZATION;
			case StatusIds.READY_FOR_DISBURSEMENT:
				return StatusIcons.READY_FOR_DISBURSEMENT;
			case StatusIds.DISBURSED:
				return StatusIcons.DISBURSED;
			default:
				return 'help-circle';
		}
	}

	function shouldBeActiveStep(
		stepStatusId: number,
		currentStatusId: number,
		history: StatusHistory[]
	): boolean {
		if (history.length === 0 && stepStatusId === StatusIds.UNDER_ANALYSIS) {
			return true;
		}

		if (
			(currentStatusId === StatusIds.FORMALIZATION ||
				currentStatusId === StatusIds.WAITING_FORMALIZATION) &&
			stepStatusId === StatusIds.WAITING_FORMALIZATION
		) {
			return true;
		}

		if (
			currentStatusId === StatusIds.READY_FOR_DISBURSEMENT &&
			stepStatusId === StatusIds.READY_FOR_DISBURSEMENT
		) {
			return true;
		}

		return false;
	}

	const statuses: LoggerStatusType[] = [];

	const step1Status = getStepStatus(StatusIds.UNDER_ANALYSIS);
	const step1 = createStep(
		StatusMessages.UNDER_ANALYSIS,
		step1Status.status,
		step1Status.icon,
		step1Status.date
	);
	statuses.push(step1);

	let step2Status: { status: string; icon: string; date?: string };
	let step2Message = StatusMessages.WAITING_FORMALIZATION;

	if (currentStatusId === StatusIds.FORMALIZATION) {
		step2Status = { status: 'info', icon: getIconForStatus(StatusIds.FORMALIZATION) };
		step2Message = StatusMessages.FORMALIZATION;
	} else if (currentStatusId === StatusIds.WAITING_FORMALIZATION) {
		step2Status = { status: 'info', icon: getIconForStatus(StatusIds.WAITING_FORMALIZATION) };
		step2Message = StatusMessages.WAITING_FORMALIZATION;
	} else if (has(StatusIds.FORMALIZATION)) {
		step2Status = { status: 'success', icon: 'check', date: getDate(StatusIds.FORMALIZATION) };
		step2Message = StatusMessages.FORMALIZATION;
	} else if (has(StatusIds.WAITING_FORMALIZATION)) {
		step2Status = {
			status: 'success',
			icon: 'check',
			date: getDate(StatusIds.WAITING_FORMALIZATION),
		};
		step2Message = StatusMessages.FORMALIZATION;
	} else {
		if (shouldBeActiveStep(StatusIds.WAITING_FORMALIZATION, currentStatusId, history)) {
			step2Status = {
				status: 'info',
				icon: getIconForStatus(StatusIds.WAITING_FORMALIZATION),
			};
		} else {
			step2Status = {
				status: 'default',
				icon: getIconForStatus(StatusIds.WAITING_FORMALIZATION),
			};
		}
	}

	const step2 = createStep(step2Message, step2Status.status, step2Status.icon, step2Status.date);
	statuses.push(step2);

	const step3Status = getStepStatus(StatusIds.READY_FOR_DISBURSEMENT);
	const step3 = createStep(
		StatusMessages.READY_FOR_DISBURSEMENT,
		step3Status.status,
		step3Status.icon,
		step3Status.date
	);
	statuses.push(step3);

	let step4: LoggerStatusType;
	if (has(StatusIds.CANCELED)) {
		step4 = createStep(StatusMessages.CANCELED, 'error', 'close', getDate(StatusIds.CANCELED));
	} else {
		const step4Status = getStepStatus(StatusIds.DISBURSED);
		step4 = createStep(
			StatusMessages.DISBURSED,
			step4Status.status,
			step4Status.icon,
			step4Status.date
		);
	}
	statuses.push(step4);

	return {
		status: statuses,
		details: {
			id: data.id,
			campaignId: data.campaignId,
			campaignName: data.campaignName,
			tax: data.tax,
			total: data.total,
			dueDate: data.dueDate,
			receivableStartDueDate: data.receivableStartDueDate,
			receivableEndDueDate: data.receivableEndDueDate,
			draweeName: data.draweeName,
			draweeDocument: data.draweeDocument,
		},
		providers: data.providers,
		updatedAt: data.updatedAt,
	};
}
