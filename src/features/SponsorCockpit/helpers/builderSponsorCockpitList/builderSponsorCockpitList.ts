import { SponsorCockpitListRawData } from '@/features/SponsorCockpit/types';


export default function (data: SponsorCockpitListRawData) {
	const filtered = data.content?.map(x => {
		return {
			name: x.draweeName,
			document: x.draweeDocument,
			operationId: x.id,
			operationStatus: x.status,
			dueDate : x.dueDate,
			requestedValue: x.requestedValue,
			total: x.total,
			source: x.source,
		};
	});

	const pagination = data.pageable || { pageNumber: 0, pageSize: 10 };
	const content = filtered || [];
	return {
		content,
		pagination,
	};
}
