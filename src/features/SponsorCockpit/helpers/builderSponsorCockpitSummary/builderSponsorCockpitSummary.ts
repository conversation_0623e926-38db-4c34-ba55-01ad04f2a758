import { SponsorCockpitSummaryRawData } from '@/features/SponsorCockpit/types/types';

export default function (data: SponsorCockpitSummaryRawData) {
	return {
		productName: data?.productName,
		inAnalysis: data?.inAnalysis || 0,
		waitingFormalization: data?.waitingFormalization || 0,
		formalization: data?.formalization || 0,
		readyForDisbursement: data?.readyForDisbursement || 0,
		inAnalysisValue: data?.inAnalysisValue || 0,
		waitingFormalizationValue: data?.waitingFormalizationValue || 0,
		formalizationValue: data?.formalizationValue || 0,
		readyForDisbursementValue: data?.readyForDisbursementValue || 0,
	};
}
