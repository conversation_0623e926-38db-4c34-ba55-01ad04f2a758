import type { AxiosResponse } from 'axios';

export type Provider = {
	providerId: number;
	providerName: string;
	providerDocument: string;
	receivedValue: number;
	paymentDate: string;
	type: string;
	bankDataId: number;
	bankAccountType: string;
	bankName: string;
	bankAgency: string;
	bankAccount: string;
	pixKey: string;
	pixKeyType: string;
};
export enum StatusType {
	SUCCESS = 'success',
	INFO = 'info',
	WARNING = 'warning',
	ERROR = 'error',
	DEFAULT = 'default',
}

export interface Status {
	message: string;
	formattedDate?: string;
	status: StatusType;
	icon?: string;
	extraMessage?: string;
}

export type StatusHistory = {
	statusId: number;
	desc: string;
	createdAt: string;
};

export type SponsorCockpitDetailsRawData = {
	id: number;
	campaignId: number;
	campaignName: string;
	tax: number;
	total: number;
	dueDate: string;
	receivableStartDueDate: string;
	receivableEndDueDate: string;
	status: number;
	statusName: string;
	updatedAt: string;
	receivableId: number;
	draweeName: string;
	draweeDocument: string;
	providers: Provider[];
	statusHistory: StatusHistory[];
};
export type Sort = {
	unsorted: boolean;
	sorted: boolean;
	empty: boolean;
};
export type Pageable = {
	sort: Sort;
	pageNumber: number;
	pageSize: number;
	totalPages: number;
	totalElements: number;
};
export type SponsorCockpitListRawData = {
	content: SponsorCockpitListContent[];
	pageable: Pageable;
};
export type SponsorCockpitListResponseData = {
	data: SponsorCockpitListRawData;
	meta: string[];
	errors: string[];
};
export type SponsorCockpitListResponse = AxiosResponse<SponsorCockpitListResponseData>;
export type SponsorCockpitSummaryRawData = {
	productId: number;
	productName: string;
	readyForDisbursement: number;
	waitingFormalization: number;
	inAnalysis: number;
	formalization: number;
	readyForDisbursementValue: number;
	waitingFormalizationValue: number;
	inAnalysisValue: number;
	formalizationValue: number;
};
export type SponsorCockpitSummaryResponseData = {
	data: SponsorCockpitSummaryRawData;
	meta: string[];
	errors: string[];
};
export type SponsorCockpitSummaryResponse = AxiosResponse<SponsorCockpitSummaryResponseData>;
export type SponsorCockpitDetails = {
	status: {
		message: string;
		formattedDate?: string;
		status: string;
		icon?: string;
	}[];
	details: {
		id: number;
		campaignId: number;
		campaignName: string;
		tax: number;
		total: number;
		dueDate: string;
		receivableStartDueDate: string;
		receivableEndDueDate: string;
		draweeName: string;
		draweeDocument: string;
	};
	providers: Provider[];
	updatedAt: string;
};
