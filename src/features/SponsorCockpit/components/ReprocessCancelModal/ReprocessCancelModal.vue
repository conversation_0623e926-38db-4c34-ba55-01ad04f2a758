<template>
	<farm-modal
		v-model="internalValue"
		:offsetTop="48"
		:offsetBottom="68"
		@onClose="onClose"
		size="sm"
	>
		<template v-slot:header>
			<farm-dialog-header title="Cancelar" :hasCloseIcon="false" />
		</template>

		<template v-slot:content>
			<farm-caption variation="semiBold" class="mb-4">
				Tem certeza que deseja cancelar o reprocessamento?
			</farm-caption>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				:confirmLabel="'Cancelar'"
				:hasCancel="false"
				:extraButtons="extraButtons"
				@onConfirm="handleCancelConfirm"
				@onBack="handleBack"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
	name: 'ReprocessCancelModal',

	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},

	setup(props, { emit }) {
		const internalValue = ref(props.value);

		watch(
			() => props.value,
			newValue => {
				internalValue.value = newValue;
			}
		);

		const extraButtons = [
			{
				label: 'Voltar',
				color: 'primary',
				outlined: true,
				listener: 'onBack',
			},
		];

		const onClose = () => {
			emit('update:value', false);
			emit('onClose');
		};

		const handleBack = () => {
			emit('update:value', false);
			emit('onBack');
		};

		const handleCancelConfirm = () => {
			emit('update:value', false);
			emit('onConfirm');
		};

		return {
			internalValue,
			onClose,
			handleBack,
			handleCancelConfirm,
			extraButtons,
		};
	},
});
</script>

<style lang="scss" scoped></style>
