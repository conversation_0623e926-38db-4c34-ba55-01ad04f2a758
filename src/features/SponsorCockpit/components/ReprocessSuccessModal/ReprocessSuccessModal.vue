<template>
	<farm-modal
		v-model="internalValue"
		:offsetTop="48"
		:offsetBottom="68"
		@onClose="handleCloseModal"
		size="sm"
	>
		<template v-slot:header>
			<farm-dialog-header :title="modalTitle" :hasCloseIcon="false" />
		</template>

		<template v-slot:content>
			<farm-caption variation="semiBold" class="mb-4">
				{{ modalMessage }}
			</farm-caption>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				:confirmLabel="'Fechar'"
				:hasCancel="false"
				@onConfirm="handleCloseModal"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue';

export default defineComponent({
	name: 'ReprocessSuccessModal',

	props: {
		value: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			default: 'success', // 'success' ou 'error'
			validator: (value: string) => ['success', 'error'].includes(value),
		},
	},

	setup(props, { emit }) {
		const internalValue = ref(props.value);

		watch(
			() => props.value,
			newValue => {
				internalValue.value = newValue;
			}
		);

		const modalTitle = computed(() => {
			return props.type === 'success' ? 'Concluído' : 'Erro';
		});

		const modalMessage = computed(() => {
			return props.type === 'success' 
				? 'O reprocessamento foi efetuado com sucesso.'
				: 'Um erro inesperado ocorreu.';
		});

		const handleCloseModal = () => {
			emit('update:value', false);
			emit('onClose');
		};

		return {
			internalValue,
			handleCloseModal,
			modalTitle,
			modalMessage,
		};
	},
});
</script>
