<template>
	<farm-modal v-model="value" :offsetTop="48" :offsetBottom="140" @onClose="onClose">
		<farm-loader mode="overlay" v-if="isLoading || isLoadingReprocess" />
		<template v-slot:header>
			<farm-dialog-header :title="modalTitle" @onClose="onClose" />
		</template>

		<template v-slot:content>
			<farm-caption variation="semiBold" class="mb-4">
				Selecione os itens que você deseja reprocessar:
			</farm-caption>
			<farm-row>
				<farm-col cols="12" md="8">
					<farm-form-mainfilter
						label="Buscar Nome, Documento ou ID"
						:show-filters="isOpenFilter"
						@onInputChange="handleSearchFilters"
						@onClick="onClickMainFilter"
					/>
				</farm-col>
			</farm-row>
			<collapse-transition :duration="300">
				<farm-row v-show="isOpenFilter" class="mb-6">
					<farm-col cols="12" md="3">
						<farm-label for="filter-status"> Status </farm-label>
						<farm-select
							id="filter-status"
							v-model="filters.status"
							:items="errors || []"
							item-text="desc"
							item-value="id"
							multiple
						/>
					</farm-col>
					<farm-col cols="12" md="3">
						<farm-label for="filter-source"> Tipo de Importação </farm-label>
						<farm-select
							id="filter-source"
							v-model="filters.source"
							:items="sources || []"
							item-text="desc"
							item-value="id"
							multiple
						/>
					</farm-col>
					<farm-col cols="12">
						<farm-btn-confirm
							outlined
							class="farm-btn--responsive"
							title="Aplicar Filtros"
							@click="handleApplyFilters"
						>
							Aplicar Filtros
						</farm-btn-confirm>
						<farm-btn
							plain
							depressed
							class="farm-btn--responsive ml-0 ml-sm-2 mt-2 mt-sm-0"
							title="Limpar Filtros"
							@click="handleCleanFilters"
						>
							Limpar Filtros
						</farm-btn>
					</farm-col>
				</farm-row>
			</collapse-transition>

			<ReprocessModalTable
				class="mt-6"
				:reprocessItems="sponsorCockpitListReprocess"
				:errors="errors"
				:loading="isLoading"
				:page="page"
				:pagination="pagination"
				@onSort="onSortSelect"
				@onSelectionChange="handleSelectionChange"
				@onSelectAllToggled="handleSelectAllFromTable"
				@onChangePage="onChangePage"
				@onChangePageLimit="onChangePageLimit"
			/>
		</template>

		<template v-slot:footer>
			<farm-dialog-footer
				class="mt-2"
				:confirmLabel="modalTitle"
				:hasCancel="false"
				:extraButtons="extraButtons"
				@onConfirm="handleReprocessConfirm"
				@onClose="onClose"
			/>
		</template>
	</farm-modal>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';

import { useSources, useCentralizedSelection } from '@/features/SponsorCockpit/composables';
import useErrors from '@/features/SponsorCockpit/composables/useErrors';
import { useReprocess } from '@/features/SponsorCockpit/composables/useReprocess';
import useSponsorCockpitListReprocess from '@/features/SponsorCockpit/composables/useSponsorCockpitListReprocess';
import useSponsorCockpitListReprocessIds from '@/features/SponsorCockpit/composables/useSponsorCockpitListReprocessIds';
import useSponsorCockpitPageable from '@/features/SponsorCockpit/composables/useSponsorCockpitPageable';

import ReprocessModalTable from '../ReprocessModalTable';

const ORIGINATION_OPERATION_TYPE = 2;

export default defineComponent({
	name: 'ReprocessModal',
	components: { ReprocessModalTable },

	props: {
		value: {
			type: Boolean,
			default: false,
		},
	},

	setup(props, { emit }) {
		const modalTitle = 'Reprocessar';

		const extraButtons = [
			{
				label: 'Cancelar',
				color: 'primary',
				outlined: true,
				listener: 'onClose',
				disabled: computed(() => isLoadingReprocess.value),
			},
		];

		const selection = useCentralizedSelection();

		const filters = ref({
			status: [],
			source: [],
		});

		const sort = ref({
			order: 'DESC',
			orderby: 'updatedAt',
		});

		const { getSources, sources, isLoadingSources } = useSources();
		const { getErrors, errors, isLoadingErrors } = useErrors();

		const {
			getSponsorCockpitListReprocess,
			sponsorCockpitListReprocess,
			sponsorCockpitPaginationReprocess,
			isLoadingSponsorCockpitListReprocess,
		} = useSponsorCockpitListReprocess(ORIGINATION_OPERATION_TYPE);

		const {
			getSponsorCockpitListReprocessIds,
			sponsorCockpitListReprocessIds,
			isLoadingSponsorCockpitListReprocessIds,
		} = useSponsorCockpitListReprocessIds(ORIGINATION_OPERATION_TYPE);

		const initialPagination = {
			pageNumber: 0,
			pageSize: 10,
			sort: null,
			totalElements: 0,
			totalPages: 0,
		};

		const {
			page,
			pagination,
			isOpenFilter,
			onClickMainFilter,
			onInputChangeMainFilter,
			onApplyFilter,
			onSortSelect,
			onChangePage,
			onChangePageLimit,
		} = useSponsorCockpitPageable(
			{
				keyInputSearch: 'operation',
				filters,
				sort: sort.value,
				lowercaseSort: true,
				charInputSearch: 2,
				calbackFn: params => {
					getSponsorCockpitListReprocess(params);
				},
			},
			sponsorCockpitPaginationReprocess || initialPagination
		);

		const onClose = () => {
			selection.clearSelection();
			emit('onClose');
		};

		const selectedItems = ref([]);

		function handleSelectionChange(items) {
			selectedItems.value = items;

			const currentItems = normalizeToArray(sponsorCockpitListReprocess);

			currentItems.forEach(item => {
				const id = Number(item.operationId);
				const isSelected = items.some(
					selectedItem => Number(selectedItem.operationId) === id
				);

				if (isSelected) {
					selection.selectItem(id);
				} else {
					selection.unselectItem(id);
				}
			});
		}

		const { reprocess, isLoadingReprocess, isErrorReprocess, isSuccessReprocess } =
			useReprocess();

		function handleReprocessConfirm() {
			if (isLoadingReprocess.value) return;
			let ids: number[] = [];
			const effectiveIds = selection.effectiveSelectedIds.value;

			if (effectiveIds.isAllSelected) {
				const allIds = normalizeToArray(sponsorCockpitListReprocessIds);

				ids = allIds
					.filter(id => !effectiveIds.excludedIds.includes(Number(id)))
					.map(id => Number(id));
			} else {
				ids = normalizeToArray(selectedItems.value).map(item => Number(item.operationId));
			}

			reprocess(ids);
		}

		const handleSearchFilters = (value: string) => {
			onInputChangeMainFilter(value);
		};

		const handleApplyFilters = () => {
			onApplyFilter(filters.value);
		};

		const handleCleanFilters = () => {
			filters.value.status = [];
			filters.value.source = [];
			onApplyFilter(filters.value);
		};

		const isLoading = computed(() => {
			return (
				isLoadingSources.value ||
				isLoadingErrors.value ||
				isLoadingSponsorCockpitListReprocess.value
			);
		});

		function handleSelectAllFromTable(isTryingToSelectAll: boolean) {
			if (isTryingToSelectAll) {
				selection.allSelected.value = true;

				getSponsorCockpitListReprocessIds({
					...filters.value,
				});
			} else {
				selection.clearSelection();
				selectedItems.value = [];
			}
		}

		watch(sponsorCockpitListReprocessIds, newIds => {
			if (selection.allSelected.value) {
				const idsArray = normalizeToArray(newIds);

				if (idsArray.length > 0) {
					selection.selectAll(idsArray.map(id => Number(id)));
				}
			}
		});

		watch(isSuccessReprocess, success => {
			if (success) {
				emit('onReprocessed');
				selection.clearSelection();
				selectedItems.value = [];
			}
		});

		watch(isErrorReprocess, error => {
			if (error) {
				emit('onReprocessError');
				selection.clearSelection();
				selectedItems.value = [];
			}
		});

		function normalizeToArray(data: any): any[] {
			if (Array.isArray(data)) {
				return data;
			}

			if (data && typeof data === 'object') {
				if ('value' in data && Array.isArray(data.value)) {
					return data.value;
				}
			}

			return [];
		}

		watch(
			() => props.value,
			isOpening => {
				if (isOpening) {
					filters.value.status = [];
					filters.value.source = [];

					selection.clearSelection();
					selectedItems.value = [];

					getSources();
					getErrors();

					const initialLoadParams = {
						page: 0,
						limit: 10,
						status: [],
						source: [],
						orderby: sort.value.orderby,
						order: sort.value.order,
					};
					getSponsorCockpitListReprocess(initialLoadParams);
				}
			},
			{ immediate: true }
		);

		return {
			onClose,
			handleReprocessConfirm,
			modalTitle,
			extraButtons,
			sources,
			errors,
			isOpenFilter,
			filters,
			onClickMainFilter,
			handleSearchFilters,
			handleApplyFilters,
			handleCleanFilters,
			page,
			pagination,
			isLoading,
			sponsorCockpitListReprocess,
			onSortSelect,
			selectedItems,
			handleSelectionChange,
			handleSelectAllFromTable,
			sponsorCockpitListReprocessIds,
			isLoadingSponsorCockpitListReprocessIds,
			reprocess,
			isLoadingReprocess,
			isErrorReprocess,
			isSuccessReprocess,
			selection,
			onChangePage,
			onChangePageLimit,
		};
	},
});
</script>

<style lang="scss" scoped></style>
