<template>
	<animated-fade-in>
		<farm-row v-for="supplier in suppliers" v-bind:key="supplier.providerId">
			<farm-col cols="12" md="6">
				<farm-card class="card--primary-color">
					<farm-card-content class="pb-3">
						<farm-bodytext :type="2" variation="bold">
							Fornecedor:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.providerName
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="py-3">
						<farm-bodytext :type="2" variation="bold">
							CNPJ:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.providerDocument
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="py-3">
						<farm-bodytext :type="2" variation="bold">
							Valor a receber:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								formatCurrency(supplier.receivedValue)
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="pt-3">
						<farm-bodytext :type="2" variation="bold">
							Data de pagamento:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								formatDateOrNA(supplier.paymentDate)
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
				</farm-card>
			</farm-col>
			<farm-col v-if="supplier.type === 'pix'" cols="12" md="6">
				<farm-card class="card--primary-color">
					<farm-card-content class="pb-3">
						<farm-bodytext :type="2" variation="bold">
							Tipo:
							<farm-bodytext tag="span" :type="2" variation="regular"
								>Pix</farm-bodytext
							>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="pt-3">
						<farm-bodytext :type="2" variation="bold">
							Chave:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.pixKey
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
				</farm-card>
			</farm-col>
			<farm-col v-else cols="12" md="6">
				<farm-card class="card--primary-color">
					<farm-card-content class="pb-3">
						<farm-bodytext :type="2" variation="bold">
							Tipo de conta:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.bankAccountType
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="py-3">
						<farm-bodytext :type="2" variation="bold">
							Banco:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.bankName
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="py-3">
						<farm-bodytext :type="2" variation="bold">
							Agência:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.bankAgency
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
					<farm-card-content class="pt-3">
						<farm-bodytext :type="2" variation="bold">
							Número da conta:
							<farm-bodytext tag="span" :type="2" variation="regular">{{
								supplier.bankAccount
							}}</farm-bodytext>
						</farm-bodytext>
					</farm-card-content>
				</farm-card>
			</farm-col>
		</farm-row>
	</animated-fade-in>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import AnimatedFadeIn from "@/components/AnimatedFadeIn";
import { formatDateOrNA } from '@/helpers/formatCards';
import { formatNumberToCurrent } from '@/helpers/masks';

import { Provider } from '../../types';


export default defineComponent({
	components: {AnimatedFadeIn},
	props: {
		suppliers: { 
			type: Array as PropType<Provider[]>,
			required: true,
		},
	},
	setup() {
		function formatCurrency(value: number) {
			if (!value) return 'N/A';
			return formatNumberToCurrent(value);
		}
		return {
			formatDateOrNA,
			formatCurrency,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './SponsorCockpitDetailSupplier';
</style>
