<template>
	<farm-row y-grid-gutters>
		<farm-col cols="12" md="6" lg="3">
			<farm-card>
				<farm-card-content gutter="lg" class="d-flex align-center">
					<farm-idcaption
						icon="dots-horizontal-circle-outline"
						copyText=""
					>
						<template #title>
							<farm-caption variation="regular" color="gray" tag="span">
								Em Análise ({{ summary.inAnalysis || 0 }})
							</farm-caption>
						</template>
						<template #subtitle>
							<farm-bodytext variation="bold" :type="1">
								{{ brl(summary.inAnalysisValue || 0) }}
							</farm-bodytext>
						</template>
					</farm-idcaption>
				</farm-card-content>
			</farm-card>
		</farm-col>

		<farm-col cols="12" md="6" lg="3">
			<farm-card>
				<farm-card-content gutter="lg" class="d-flex align-center">
					<farm-idcaption
						icon="dots-horizontal-circle-outline"
						copyText=""
					>
						<template #title>
							<farm-caption variation="regular" color="gray" tag="span">
								Aguardando Formaliza<PERSON> ({{ summary.waitingFormalization || 0 }})
							</farm-caption>
							<b></b>
						</template>
						<template #subtitle>
							<farm-bodytext variation="bold" :type="1">
								{{ brl(summary.waitingFormalizationValue || 0) }}
							</farm-bodytext>
						</template>
					</farm-idcaption>
				</farm-card-content>
			</farm-card>
		</farm-col>

		<farm-col cols="12" md="6" lg="3">
			<farm-card>
				<farm-card-content gutter="lg" class="d-flex align-center">
					<farm-idcaption
						icon="clipboard-text-outline"
						copyText=""
					>
						<template #title>
							<farm-caption variation="regular" color="gray" tag="span">
								Em Formalização ({{ summary.formalization || 0 }})
							</farm-caption>
							<b></b>
						</template>
						<template #subtitle>
							<farm-bodytext variation="bold" :type="1">
								{{ brl(summary.formalizationValue || 0) }}
							</farm-bodytext>
						</template>
					</farm-idcaption>
				</farm-card-content>
			</farm-card>
		</farm-col>

		<farm-col cols="12" md="6" lg="3">
			<farm-card>
				<farm-card-content gutter="lg" class="d-flex align-center">
					<farm-idcaption 
						icon="send-outline" 
						copyText="" 
					>
						<template #title>
							<farm-caption variation="regular" color="gray" tag="span">
								Pronto para Desembolso ({{ summary.readyForDisbursement || 0 }})
							</farm-caption>
						</template>
						<template #subtitle>
							<farm-bodytext variation="bold" :type="1">
								{{ brl(summary.readyForDisbursementValue || 0) }}
							</farm-bodytext>
						</template>
					</farm-idcaption>
				</farm-card-content>
			</farm-card>
		</farm-col>
	</farm-row>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import { brl } from '@farm-investimentos/front-mfe-libs-ts';

export default defineComponent({
	props: {
		summary: {
			type: Object,
			required: true,
			default: () => ({
				inAnalysis: 0,
				waitingFormalization: 0,
				formalization: 0,
				readyForDisbursement: 0,
				inAnalysisValue: 0,
				waitingFormalizationValue: 0,
				formalizationValue: 0,
				readyForDisbursementValue: 0
			})
		},
	},
	setup() {
		return {
			brl,
		};
	},
});
</script>

