.reprocess-modal-table {
	:deep(.header-text) {
		display: inline-flex !important; 
		align-items: center !important; 
		white-space: nowrap !important; 

		.farm-icon {
			position: static !important; 
		}
	}

	
} 

// Alinha o checkbox do header com o checkbox da tabela
:deep(.span-checkbox) {
	justify-content: center !important;
}
:deep(.v-simple-checkbox .v-input--selection-controls__input ) {
	margin: 0 !important;
}
:deep(.v-simple-checkbox .v-input--selection-controls__input .v-icon.mdi-checkbox-blank-outline) {
	color: #5c5c5c !important; 
	transition: all 0.4s;

	&::before {
		content: "";
		display: inline-block;
		box-sizing: border-box;
		width: 16px;
		height: 16px;
		border: 2px solid currentColor;
		border-radius: 4px;
		position: relative; 
		top: 1px;
		left: 0px;
	}
} 

:deep(.v-simple-checkbox .v-input--selection-controls__input .v-icon.mdi-checkbox-marked) {
	color: #4f8406 !important; 

} 


// Estilos para o ripple/efeito de clique do v-simple-checkbox
:deep(.v-simple-checkbox .v-input--selection-controls__ripple) {
  display: none !important; // Remove completamente o efeito de ripple
}

// Nova tentativa: Centralizar o checkbox na primeira coluna da tabela
:deep(tbody tr td:first-child) {
  text-align: center !important; 
}
