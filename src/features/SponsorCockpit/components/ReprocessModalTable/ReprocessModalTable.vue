<template>
	<v-data-table
		id="reprocess-modal-table"
		class="reprocess-modal-table"
		hide-default-header
		hide-default-footer
		:headers="headers"
		:items="tableItems"
		:server-items-length="tableItems.length"
		show-select
		v-model="selectedItems"
		return-object
		item-key="operationId"
		:disable-sort="true"
		:loading="loading"
		:options.sync="tableOptions"
		@item-selected="onItemSelected"
		@toggle-select-all="onToggleSelectAll"
		ref="reprocessTableRef"
	>
		<template v-slot:header="{ props }">
			<farm-datatable-header
				v-model="localAllSelected"
				:headers="props.headers"
				:sortClick="sortClicked"
				:showCheckbox="true"
				:headerProps="customHeaderProps"
				@toggleSelectAll="onToggleSelectAll"
				@onClickSort="onSort"
			/>
		</template>

		<template #no-data>
			<farm-emptywrapper subtitle="Nenhum item encontrado" :bordered="false" />
		</template>

		<template #loading>
			<farm-box class="my-12">
				<farm-loader />
			</farm-box>
		</template>

		<template #item.data-table-select="{ item, isSelected }">
			<v-simple-checkbox 
				:value="isSelected" 
				@input="value => onSelectItem(item, value)"
			/>
		</template>

		<template #item.name="{ item }">
			{{ item.name || '-' }}
		</template>
		<template #item.document="{ item }">
			{{ item.document || '-' }}
		</template>
		<template #item.operationId="{ item }">
			<div class="d-flex align-center">
				<span class="mr-2">{{ item.operationId }}</span>
				<farm-tooltip v-if="item.source" :fluid="true" position="top-right" class="source-icon-tooltip">
					{{ getSourceTooltipText(item.source) }}
					<template #activator>
						<material-icon type="outlined" color="black" variation="40">
							{{ getSourceIcon(item.source) }}
						</material-icon>
					</template>
				</farm-tooltip>
			</div>	
		</template>
		<template #item.operationStatus="{ item }">
			<farm-chip color="error" v-if="item.operationStatus">
				{{ getErrorStatusText(item.operationStatus) }}
			</farm-chip>
			<span v-else>-</span>
		</template>
		<template #item.dueDate="{ item }">
			{{ formatDueDate(item.dueDate) || '-' }}
		</template>
		<template #item.requestedValue="{ item }">
			{{ formatCurrency(item.requestedValue) || '-' }}
		</template>
		<template #item.total="{ item }">
			{{ formatCurrency(item.total) || '-' }}
		</template>

		<template #footer>
			<farm-datatable-paginator
				v-if="tableItems.length > 0"
				class="mt-6"
				:hasGutter="false"
				:page="page"
				:totalPages="pagination.totalPages"
				:initialLimitPerPage="pagination.pageSize"
				@onChangePage="onChangePage"
				@onChangeLimitPerPage="onChangePageLimit"
			/>
		</template>
	</v-data-table>
</template>

<script lang="ts">
import { defineComponent, ref, PropType, computed, watch, onMounted } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { useCentralizedSelection } from '@/features/SponsorCockpit/composables';
import { reprocessModalTableHeaders as headers } from '@/features/SponsorCockpit/configurations/headers/reprocessModalTableHeaders';
import { ImportTypeIcons, ImportType } from '@/features/SponsorCockpit/constants/importType';

interface ReprocessItem {
	operationId: string | number;
	name?: string;
	document?: string;
	operationStatus?: number;
	dueDate?: string;
	requestedValue?: number | string;
	total?: number | string;
	source?: number;
}

export default defineComponent({
	name: 'ReprocessModalTable',
	props: {
		reprocessItems: {
			type: Array as PropType<ReprocessItem[]>,
			default: () => [],
		},
		errors: {
			type: Array as PropType<any[]>,
			default: () => [],
		},
		loading: {
			type: Boolean,
			default: false,
		},
		page: {
			type: Number,
			default: 1,
		},
		pagination: {
			type: Object,
			default: () => ({}),
		},
	},
	emits: ['onSort', 'onSelectionChange', 'onSelectAllToggled', 'onChangePage', 'onChangePageLimit'],
	setup(props, { emit }) {
		const sortClicked = ref([]);
		const reprocessTableRef = ref<any>(null);
		const tableOptions = ref({});
		const localAllSelected = ref(false);

		
		const selection = useCentralizedSelection();

		const tableItems = computed(() => {
			return props.reprocessItems || [];
		});


		const selectedItems = ref<ReprocessItem[]>([]);
		
	
		watch(() => selection.allSelected.value, (isAllSelected) => {

			if (isAllSelected) {
				updateSelectedItems();
			}
		});
		
		watch(tableItems, () => {
			updateSelectedItems();
		});
		
		function updateSelectedItems() {
			const itemsToSelect = tableItems.value.filter(item => 
				selection.isItemSelected(Number(item.operationId))
			);
						
			selectedItems.value = itemsToSelect;
			
			localAllSelected.value = tableItems.value.length > 0 && 
				itemsToSelect.length === tableItems.value.length;
		}
		
		function onSelectItem(item: ReprocessItem, isSelected: boolean) {
			if (isSelected) {
				selection.selectItem(Number(item.operationId));
			} else {
				selection.unselectItem(Number(item.operationId));
			}
			
			if (isSelected) {
				if (!selectedItems.value.some(i => i.operationId === item.operationId)) {
					selectedItems.value.push(item);
				}
			} else {
				const idx = selectedItems.value.findIndex(i => i.operationId === item.operationId);
				if (idx !== -1) {
					selectedItems.value.splice(idx, 1);
				}
			}
			
			emit('onSelectionChange', selectedItems.value);
		}
		
		function onItemSelected({ item, value }) {
			
			if (value) {
				selection.selectItem(Number(item.operationId));
			} else {
				selection.unselectItem(Number(item.operationId));
			}
			
			emit('onSelectionChange', selectedItems.value);
		}
		
		function onToggleSelectAll(value: boolean) {
			
			localAllSelected.value = value;
			
			emit('onSelectAllToggled', value);
			
			if (!value && selection.allSelected.value) {
				tableItems.value.forEach(item => {
					selection.unselectItem(Number(item.operationId));
				});
			}
			
			if (value) {
				tableItems.value.forEach(item => {
					selection.selectItem(Number(item.operationId));
				});
				
				selectedItems.value = [...tableItems.value];
			} else {
				selectedItems.value = [];
			}
			
			emit('onSelectionChange', selectedItems.value);
		}

		const customHeaderProps = computed(() => {
			return {
				headers: headers,
				everyItem: localAllSelected.value,
				someItems: selectedItems.value.length > 0 && !localAllSelected.value
			};
		});

		function onSort(sortInfo: any) {
			emit('onSort', `${sortInfo.field}_${sortInfo.descending}`);
		}

		function onChangePage(newPage: number) {
			emit('onChangePage', newPage);
		}

		function onChangePageLimit(newLimit: number) {
			emit('onChangePageLimit', newLimit);
		}

		function getSourceIcon(source: number): string | null {
			if (source) {
				return ImportTypeIcons[source] || 'help_outline';
			}
			return null;
		}

		function getSourceTooltipText(source: number): string {
			switch (source) {
				case ImportType.CriadoPorCliente:
					return 'Criado pelo cliente';
				case ImportType.API:
					return 'API';
				case ImportType.Importacao:
					return 'Importação';
				default:
					return 'Origem desconhecida';
			}
		}

		function getErrorStatusText(statusId: number): string {
			const errorStatus = props.errors.find(error => error.id === statusId);
			return errorStatus ? errorStatus.desc : 'Erro Desconhecido';
		}

		function formatDueDate(date: string | undefined): string {
			return date ? defaultDateFormat(date) : '';
		}

		function formatCurrency(value: number | string | undefined): string {
			if (value === undefined || value === null) return '';
			return brl(Number(value));
		}

		onMounted(() => {
			updateSelectedItems();
		});

		return {
			headers,
			sortClicked,
			tableItems,
			selectedItems,
			reprocessTableRef,
			tableOptions,
			onSort,
			onToggleSelectAll,
			onSelectItem,
			onItemSelected,
			onChangePage,
			onChangePageLimit,
			getSourceIcon,
			getSourceTooltipText,
			getErrorStatusText,
			customHeaderProps,
			formatDueDate,
			formatCurrency,
			localAllSelected
		};
	},
});
</script>

<style lang="scss" scoped>
@import './ReprocessModalTable.scss';
</style>
