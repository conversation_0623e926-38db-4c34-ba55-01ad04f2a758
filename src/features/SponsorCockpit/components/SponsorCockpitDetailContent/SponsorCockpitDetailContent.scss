.details {
	display: flex;


	gap: 24px;

	&__caption {
		position: relative;

		&::before {
			content: '';
			background-color: var(--farm-stroke-base);
			right: -12px;
			position: absolute;
			width: 1px;
			height: 100%;
		}
	}

	&__last-caption {
		margin-left: auto;
	}

	@media (max-width: 920px) {
		flex-direction: column;

		&__last-caption {
			margin-left: 0;
		}
	}
}
.name-document {
	display: flex;
	gap: 24px;
	&__item {
		position: relative;

		&::before {
			content: '';
			background-color: var(--farm-stroke-base);
			right: -12px;
			position: absolute;
			width: 1px;
			height: 100%;
		}
	}

	.info__last-item {
		margin-left: auto;
	}


}
