<template>
	<animated-fade-in>
		<farm-row>
			<farm-col cols="12">
				<farm-row no-default-gutters class="name-document">
				<farm-bodytext :type="2" variation="bold" class="info__item">
					Nome:
					<farm-bodytext tag="span" :type="2" variation="regular" class="name-document__item">
						{{ details.draweeName }}
					</farm-bodytext>
				</farm-bodytext>
				<farm-bodytext :type="2" variation="bold" class="name-document__last-item">
					Documento:
					<farm-bodytext tag="span" :type="2" variation="regular">
						{{ details.draweeDocument }}
					</farm-bodytext>
				</farm-bodytext>
			</farm-row>
		</farm-col>

		<farm-col class="my-6">
			<farm-line no-spacing />
		</farm-col>
		<farm-col class="details" cols="12">
			<farm-idcaption class="details__caption" icon="clipboard-text-outline" copy-text="">
				<template #title>ID</template>
				<template #subtitle> {{ details.id }}</template>
			</farm-idcaption>
			<farm-idcaption class="details__caption" icon="bullhorn-outline" copy-text="">
				<template #title>Campanha</template>
				<template #subtitle> {{ details.campaignName }}</template>
			</farm-idcaption>
			<farm-idcaption class="details__caption" icon="currency-usd" copy-text="">
				<template #title> Taxa</template>
				<template #subtitle> {{ details.tax }}% a.m.</template>
			</farm-idcaption>
			<farm-idcaption icon="currency-usd" copy-text="">
				<template #title>
					Total a pagar
					<farm-tooltip>
						Este valor é calculado com base nas informações utilizadas na simulação da
						sua operação
						<template #activator>
							<farm-icon class="ml-1" size="sm" color="primary"
							>information-outline
							</farm-icon>
						</template>
					</farm-tooltip>
				</template>
				<template #subtitle> {{ formatCurrency(details.total) }}</template>
			</farm-idcaption>
			<farm-idcaption class="details__last-caption" copy-text="">
				<template #title> Vencimento</template>
				<template #subtitle>
					{{ formatDateOrNA(details.dueDate) }}
				</template>
			</farm-idcaption>
			</farm-col>
		</farm-row>
	</animated-fade-in>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

import AnimatedFadeIn from "@/components/AnimatedFadeIn";
import { formatDateOrNA } from '@/helpers/formatCards';
import { formatNumberToCurrent } from '@/helpers/masks';

export default defineComponent({
	components: {AnimatedFadeIn},
	props: {
		details: {
			type: Object,
			required: true,
		},
	},
	setup() {
		function formatCurrency(value: number) {
			if (!value) return 'N/A';
			return formatNumberToCurrent(value);
		}


		return {
			formatDateOrNA,
			formatCurrency,
		};
	},
});
</script>
<style lang="scss" scoped>
@import './SponsorCockpitDetailContent';
</style>
