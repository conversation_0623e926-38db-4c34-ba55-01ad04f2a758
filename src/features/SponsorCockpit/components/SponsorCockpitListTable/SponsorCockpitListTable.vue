<template>
	<v-data-table
		id="sponsor-cockpit-list-table"
		class="sponsor-cockpit-list-table"
		hide-default-header
		hide-default-footer
		:headers="headers"
		:items="sponsorCockpitList"
		:server-items-length="sponsorCockpitList.length"
	>
		<template #no-data>
			<farm-emptywrapper subtitle="Tente filtrar novamente sua pesquisa" :bordered="false" />
		</template>

		<template #header="{ props }">
			<farm-datatable-header
				:headers="props.headers"
				:sort-click="sortClicked"
				first-selected
				:selected-index="3"
				:show-checkbox="false"
				@onClickSort="onSort"
			/>
		</template>

		<!-- eslint-disable vue/valid-v-slot -->

		<template #item.requestedValue="{ item }">
			{{ brl(item.requestedValue) || '-' }}
		</template>

		<template #item.total="{ item }">
			{{ brl(item.total) || '-' }}
		</template>

		<template #item.dueDate="{ item }">
			{{ defaultDateFormat(item.dueDate) }}
		</template>

		<template #item.operationId="{ item }">
			<div class="d-flex align-center">
				<span class="mr-2">{{ item.operationId }}</span>
				<farm-tooltip v-if="item.source" :fluid="true" position="top-left" class="source-icon-tooltip">
					{{ getSourceTooltipText(item.source) }}
					<template #activator>
						<material-icon type="outlined" color="black" variation="40">
							{{ getSourceIcon(item.source) }}
						</material-icon>
					</template>
				</farm-tooltip>
			</div>
		</template>

		<template #item.operationStatus="{ item }">
			<farm-chip
				:color="SPONSOR_COCKPIT_LIST_CHIP_STATUSES_ID_ENUM[item.operationStatus].color"
			>
				{{ SPONSOR_COCKPIT_LIST_CHIP_STATUSES_ID_ENUM[item.operationStatus].text }}
			</farm-chip>
		</template>

		<template #item.contextMenu="{ item }">
			<farm-btn icon @click="goToDetail(item.operationId)">
				<farm-icon size="md">open-in-new</farm-icon>
			</farm-btn>
		</template>
		<!-- eslint-enable vue/valid-v-slot -->
	</v-data-table>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

import { brl, defaultDateFormat } from '@farm-investimentos/front-mfe-libs-ts';

import { useRouter } from '@/composible';
import { sponsorCockpitListHeaders as headers } from '@/features/SponsorCockpit/configurations/headers';
import { SPONSOR_COCKPIT_LIST_CHIP_STATUSES_ID_ENUM } from '@/features/SponsorCockpit/constants';
import { ImportTypeIcons } from '@/features/SponsorCockpit/constants/importType';
import { ImportType } from '@/features/SponsorCockpit/constants/importType';

export default defineComponent({
	props: {
		sponsorCockpitList: {
			type: Array,
			default: () => [],
		},
	},
	setup(props, { emit }) {
		const sortClicked = ref([]);
		const router = useRouter();

		function onSort(sort) {
			emit('onSort', `${sort.field}_${sort.descending}`);
		}

		function goToDetail(operationId) {
			router.push(`/admin/meusclientes/wallet/originacoes_sacados/${operationId}`);
		}

		function getSourceIcon(source: number): string | null {
			if (source in ImportType) {
				return ImportTypeIcons[source];
			}
			console.warn(
				`[SponsorCockpitListTable] Valor de source inválido: ${source}. Valores esperados: ${Object.values(
					ImportType
				)
					.filter(v => typeof v === 'number')
					.join(', ')}`
			);
			return null;
		}

		function getSourceTooltipText(source: number): string {
			switch (source) {
				case ImportType.CriadoPorCliente:
					return 'Criado pelo cliente';
				case ImportType.API:
					return 'API';
				case ImportType.Importacao:
					return 'Importação';
				default:
					return 'Origem desconhecida';
			}
		}

		return {
			SPONSOR_COCKPIT_LIST_CHIP_STATUSES_ID_ENUM,
			headers,
			sortClicked,
			defaultDateFormat,
			brl,
			onSort,
			goToDetail,
			getSourceIcon,
			getSourceTooltipText,
		};
	},
});
</script>

<style lang="scss">
@import '@farm-investimentos/front-mfe-components/src/scss/Sticky-table';
@include stickytable('.sponsor-cockpit-list-table', 1, (0));
</style>

<style lang="scss" scoped>
@import './SponsorCockpitListTable';
</style>
