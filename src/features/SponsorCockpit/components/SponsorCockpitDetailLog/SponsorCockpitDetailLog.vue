<template>
	<animated-fade-in>
		<farm-row>
			<farm-col cols="12">
				<farm-textarea readonly :value="formattedLogForTextarea" />
			</farm-col>
		</farm-row>
	</animated-fade-in>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed } from 'vue';

import AnimatedFadeIn from "@/components/AnimatedFadeIn";
import { useSponsorCockpitLog } from '@/features/SponsorCockpit/composables/useSponsorCockpitLog/useSponsorCockpitLog';


export default defineComponent({
	components: {AnimatedFadeIn},
	props: {
		operationId: {
			type: Number,
			required: true,
		},
	},
	setup(props) {
		const {
			sponsorCockpitLog,
			isLoadingSponsorCockpitLog,
			isErrorSponsorCockpitLog,
			getSponsorCockpitLog,
		} = useSponsorCockpitLog();

		onMounted(() => {
			if (props.operationId) {
				getSponsorCockpitLog(props.operationId);
			}
		});
		
		
		const formattedLogForTextarea = computed(() => {
			if (sponsorCockpitLog.value && Array.isArray(sponsorCockpitLog.value)) {
				return sponsorCockpitLog.value.join('\n');
			}
			return ''; 
		});
		
		return {
			formattedLogForTextarea,
			isLoadingSponsorCockpitLog,
			isErrorSponsorCockpitLog,
		};
	},
});
</script>
