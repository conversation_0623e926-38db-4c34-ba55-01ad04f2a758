import { ref, computed } from 'vue';

import { RequestStatusEnum, notification } from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { sendReprocessRequest as sendReprocessRequestService } from '@/features/SponsorCockpit/services';

type UseReprocess = {
	isLoadingReprocess: { value: boolean };
	isErrorReprocess: { value: boolean };
	isSuccessReprocess: { value: boolean };
	reprocess: Function;
};

export function useReprocess(): UseReprocess {
	const productId = useSelectedProductId().value;
	const isSuccessReprocessRef = ref(false);
	const isErrorReprocessRef = ref(false);

	const { isLoading, mutate } = useMutation({
		mutationFn: params => sendReprocessRequestService(params),
		onSuccess: response => {
			const responseData = response?.data;

			if (responseData && responseData.successIds && responseData.successIds.length > 0) {
				isSuccessReprocessRef.value = true;
			} else if (
				responseData &&
				responseData.failedIds &&
				responseData.failedIds.length > 0
			) {
				isErrorReprocessRef.value = true;
			} else {
				isErrorReprocessRef.value = true;
			}
		},
		onError: () => {
			isErrorReprocessRef.value = true;
		},
	});

	const isLoadingReprocess = computed(() => isLoading.value);
	const isErrorReprocess = computed(() => isErrorReprocessRef.value);
	const isSuccessReprocess = computed(() => isSuccessReprocessRef.value);

	function createNotificationError(): void {
		notification(
			RequestStatusEnum.ERROR,
			'Erro ao reprocessar os recebíveis. Por favor tente novamente'
		);
	}

	function reprocess(ids: Array<number>): void {
		isSuccessReprocessRef.value = false;
		isErrorReprocessRef.value = false;
		mutate(ids);
	}

	return {
		isLoadingReprocess,
		isErrorReprocess,
		isSuccessReprocess,
		reprocess,
	};
}
