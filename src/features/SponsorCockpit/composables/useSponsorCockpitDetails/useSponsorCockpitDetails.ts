import { ref, computed, Ref } from 'vue';

import {
	errorBuilder,
	notification,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import builderSponsorCockpitDetails, {
	SponsorCockpitDetails as SponsorCockpitDetailsType,
} from '@/features/SponsorCockpit/helpers/builderSponsorCockpitDetails/builderSponsorCockpitDetails';
import { getSponsorCockpitDetails as getSponsorCockpitDetailsService } from '@/features/SponsorCockpit/services';

type UseSponsorCockpitDetails = {
	isLoadingSponsorCockpitDetails: Ref<boolean>;
	isErrorSponsorCockpitDetails: Ref<boolean>;
	sponsorCockpitDetails: Ref<SponsorCockpitDetailsType>;
	getSponsorCockpitDetails: (id: number) => void;
};

export function useSponsorCockpitDetails(): UseSponsorCockpitDetails {
	const sponsorCockpitDetails = ref<SponsorCockpitDetailsType>({
		status: [],
		details: {
			id: 0,
			campaignId: 0,
			campaignName: '',
			tax: 0,
			total: 0,
			dueDate: '0000-01-01',
			receivableStartDueDate: '0000-01-01',
			receivableEndDueDate: '0000-01-01',
			draweeName: '',
			draweeDocument: '',
		},
		providers: [],
		updatedAt: '0000-01-01',
	});
	const keyAccountId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (operationId: number) =>
			getSponsorCockpitDetailsService(keyAccountId, operationId),
		onSuccess: data => {
			sponsorCockpitDetails.value = builderSponsorCockpitDetails(data.data.data);
		},
		onError: err => {
			sponsorCockpitDetails.value = {
				status: [],
				details: {
					id: 0,
					campaignId: 0,
					campaignName: '',
					tax: 0,
					total: 0,
					dueDate: '0000-01-01',
					receivableStartDueDate: '0000-01-01',
					receivableEndDueDate: '0000-01-01',
					draweeName: '',
					draweeDocument: '',
				},
				providers: [],
				updatedAt: '0000-01-01',
			};
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os detalhes do limite: ${error?.message || ''}`
			);
		},
	});

	const isLoadingSponsorCockpitDetails = computed(() => isLoading.value);
	const isErrorSponsorCockpitDetails = computed(() => isError.value);
	function getSponsorCockpitDetails(operationId: number) {
		mutate(operationId);
	}

	return {
		isLoadingSponsorCockpitDetails,
		isErrorSponsorCockpitDetails,
		sponsorCockpitDetails,
		getSponsorCockpitDetails,
	};
}
