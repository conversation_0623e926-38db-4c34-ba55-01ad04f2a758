import type { Ref, ComputedRef } from 'vue';
import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { getSources as getSourcesService } from '@/features/SponsorCockpit/services';

interface Source {
	id: number;
	desc: string;
}

type UseSources = {
	isLoadingSources: ComputedRef<boolean>;
	isErrorSources: ComputedRef<boolean>;
	sources: Ref<Source[]>;
	getSources: () => void;
};

export function useSources(): UseSources {
	const sources = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getSourcesService(),
		onSuccess: data => {
			sources.value = data.data.data;
		},
		onError: err => {
			sources.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os tipos de importação: ${error?.message || ''}`
			);
		},
	});

	const isLoadingSources = computed(() => {
		return isLoading.value;
	});

	const isErrorSources = computed(() => {
		return isError.value;
	});

	function getSources(): void {
		mutate();
	}

	return {
		isLoadingSources,
		isErrorSources,
		sources,
		getSources,
	};
}