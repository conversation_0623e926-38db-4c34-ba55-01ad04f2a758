import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { getSponsorCockpitStatus as getSponsorCockpitStatusService } from '@/features/SponsorCockpit/services';

type UseStatus = {
	isLoadingStatus: {
		value: boolean;
	};
	isErrorStatus: {
		value: boolean;
	};
	status: Array<any>;
	getStatus: Function;
};

export function useStatus(): UseStatus {
	const status = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getSponsorCockpitStatusService(),
		onSuccess: data => {
			status.value = data.data.data;
		},
		onError: err => {
			status.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os dados de status: ${error?.message || ''}`
			);
		},
	});

	const isLoadingStatus = computed(() => {
		return isLoading.value;
	});

	const isErrorStatus = computed(() => {
		return isError.value;
	});

	function getStatus(): void {
		mutate();
	}

	return {
		isLoadingStatus,
		isErrorStatus,
		status,
		getStatus,
	};
}
