import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { getSponsorCockpitLog as getSponsorCockpitLogService } from '@/features/SponsorCockpit/services';

type UseSponsorCockpitLog = {
	isLoadingSponsorCockpitLog: {
		value: boolean;
	};
	isErrorSponsorCockpitLog: {
		value: boolean;
	};
	sponsorCockpitLog: string[];
	getSponsorCockpitLog: Function;
};

export function useSponsorCockpitLog(): UseSponsorCockpitLog {
	const sponsorCockpitLog = ref<string[]>([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: (workingCapitalId: number) => getSponsorCockpitLogService(workingCapitalId),
		onSuccess: data => {
			const rawLogs: string[] = data.data.logs || [];
			sponsorCockpitLog.value = rawLogs.map(log => formatLogString(log));
		},
		onError: err => {
			sponsorCockpitLog.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os dados de status: ${error?.message || ''}`
			);
		},
	});

	const isLoadingSponsorCockpitLog = computed(() => {
		return isLoading.value;
	});

	const isErrorSponsorCockpitLog = computed(() => {
		return isError.value;
	});

	function getSponsorCockpitLog(workingCapitalId: number): void {
		mutate(workingCapitalId);
	}

	function formatLogString(logEntry: string): string {
		if (typeof logEntry !== 'string' || !logEntry) {
			return ''; 
		}
		const trimmedEntry = logEntry.trim();
		const parts = trimmedEntry.split(/\s{4,}/);
		return parts.join('\n');
	}

	return {
		isLoadingSponsorCockpitLog,
		isErrorSponsorCockpitLog,
		sponsorCockpitLog,
		getSponsorCockpitLog,
	};
}
