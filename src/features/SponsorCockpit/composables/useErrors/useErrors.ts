import type { Ref, ComputedRef } from 'vue';
import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { getErrors as getErrorsService } from '@/features/SponsorCockpit/services';

interface Error {
	id: number;
	desc: string;
}

type UseErrors = {
	isLoadingErrors: ComputedRef<boolean>;
	isErrorErrors: ComputedRef<boolean>;
	errors: Ref<Error[]>;
	getErrors: () => void;
};

export function useErrors(): UseErrors {
	const errors = ref([]);

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: () => getErrorsService(),
		onSuccess: data => {
			errors.value = data.data.data;
		},
		onError: err => {
			errors.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os status de erro: ${error?.message || ''}`
			);
		},
	});

	const isLoadingErrors = computed(() => {
		return isLoading.value;
	});

	const isErrorErrors = computed(() => {
		return isError.value;
	});

	function getErrors(): void {
		mutate();
	}

	return {
		isLoadingErrors,
		isErrorErrors,
		errors,
		getErrors,
	};
}
