import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	queryString,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderSponsorCockpitList } from '@/features/SponsorCockpit/helpers/builderSponsorCockpitList';
import { getSponsorCockpitListReprocess as getSponsorCockpitListReprocessService } from '@/features/SponsorCockpit/services';

type UseSponsorCockpitListReprocess = {
	isLoadingSponsorCockpitListReprocess: {
		value: boolean;
	};
	isErrorSponsorCockpitListReprocess: {
		value: boolean;
	};
	sponsorCockpitListReprocess: Array<any>;
	sponsorCockpitPaginationReprocess: any;
	getSponsorCockpitListReprocess: Function;
};

export function useSponsorCockpitListReprocess(
	operationId: number
): UseSponsorCockpitListReprocess {
	const sponsorCockpitListReprocess = ref([]);
	const sponsorCockpitPaginationReprocess = ref(null);

	const key_account_id = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getSponsorCockpitListReprocessService(params, key_account_id),
		onSuccess: data => {
			const { content, pagination } = builderSponsorCockpitList(data.data.data);
			sponsorCockpitListReprocess.value = content;
			sponsorCockpitPaginationReprocess.value = pagination;
		},
		onError: err => {
			sponsorCockpitListReprocess.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os dados da lista: ${error?.message || ''}`
			);
		},
	});

	const isLoadingSponsorCockpitListReprocess = computed(() => {
		return isLoading.value;
	});

	const isErrorSponsorCockpitListReprocess = computed(() => {
		return isError.value;
	});

	function getSponsorCockpitListReprocess(filters): void {
		// Adicionando valores padrão caso não estejam presentes nos filtros
		const defaultParams = {
			orderby: 'id',
			order: 'DESC',
			page: 0,
			limit: 10,
		};

		const paramsObject = {
			...defaultParams,
			...filters,
			operation_type: operationId,
		};

		if (Array.isArray(paramsObject.status)) {
			paramsObject.status = paramsObject.status.join(',');
		}
		if (Array.isArray(paramsObject.source)) {
			paramsObject.source = paramsObject.source.join(',');
		}
		const params = queryString(paramsObject, {});
		mutate(params);
	}

	return {
		isLoadingSponsorCockpitListReprocess,
		isErrorSponsorCockpitListReprocess,
		sponsorCockpitPaginationReprocess,
		sponsorCockpitListReprocess,
		getSponsorCockpitListReprocess,
	};
}
