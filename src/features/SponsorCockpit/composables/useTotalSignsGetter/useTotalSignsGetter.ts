import { computed } from 'vue';

import { useGetter } from '@/composible';

type TotalSignsGetter = {
	numberOfTotalSignatories: {
		value: number;
	};
	numberOfTotalSignedSignatories: {
		value: number;
	};
};

export default function useTotalSignsGetter(): TotalSignsGetter {
	const computedProps = {} as TotalSignsGetter;

	computedProps.numberOfTotalSignatories = computed(useGetter('sponsor', 'numberOfTotalSignatories'));
	computedProps.numberOfTotalSignedSignatories = computed(useGetter('sponsor', 'numberOfTotalSignedSignatories'));

	return computedProps;
}
