import { ref, computed } from 'vue';

// Definir o estado FORA da função para que seja compartilhado (singleton)
const allSelected = ref(false);
const selectedIds = ref(new Set<number>());
const excludedIds = ref(new Set<number>());

export function useCentralizedSelection() {
	// Computed para obter todos IDs selecionados considerando exclusões
	const effectiveSelectedIds = computed(() => {
		// Se 'selecionar tudo' estiver ativo, a lista é dinâmica e baseada em exclusões
		if (allSelected.value) {
			return {
				isAllSelected: true,
				excludedIds: Array.from(excludedIds.value),
			};
		}

		// C<PERSON>o contrário, retorna só os IDs explicitamente selecionados
		return {
			isAllSelected: false,
			selectedIds: Array.from(selectedIds.value),
		};
	});

	// Verificar se um item está selecionado
	function isItemSelected(id: number): boolean {
		if (allSelected.value) {
			// Se 'selecionar tudo' está ativo, o item está selecionado a menos que esteja excluído
			return !excludedIds.value.has(id);
		}
		// Caso contrário, verificar se está na lista de selecionados
		return selectedIds.value.has(id);
	}

	// Selecionar um item individual
	function selectItem(id: number): void {
		if (id % 1000 === 0) {
			console.log(`Selecionando item ${id}. allSelected: ${allSelected.value}`);
		}

		if (allSelected.value) {
			// Se 'selecionar tudo' está ativo, remove da lista de exclusões
			excludedIds.value.delete(id);
		} else {
			// Adiciona à lista de selecionados
			selectedIds.value.add(id);
		}
	}

	// Desselecionar um item individual
	function unselectItem(id: number): void {
		// Apenas log para alguns IDs para evitar spam no console
		if (id % 1000 === 0) {
			console.log(`Desselecionando item ${id}. allSelected: ${allSelected.value}`);
		}

		if (allSelected.value) {
			// Se 'selecionar tudo' está ativo, adiciona à lista de exclusões
			excludedIds.value.add(id);
		} else {
			// Remove da lista de selecionados
			selectedIds.value.delete(id);
		}
	}

	// Definir todos os IDs como selecionados
	function selectAll(ids: number[]): void {
		console.log(`Selecionando todos: ${ids.length} itens`, ids);
		allSelected.value = true;
		excludedIds.value.clear(); // Limpa exclusões

		// Também armazenamos todos os IDs para referência
		// Isso ajuda com a compatibilidade entre os componentes
		selectedIds.value.clear();
		ids.forEach(id => selectedIds.value.add(id));
	}

	// Limpar todas as seleções
	function clearSelection(): void {
		console.log('Limpando todas as seleções');
		allSelected.value = false;
		selectedIds.value.clear();
		excludedIds.value.clear();
	}

	// Alternar a seleção de um item
	function toggleSelection(id: number, selected: boolean): void {
		if (selected) {
			selectItem(id);
		} else {
			unselectItem(id);
		}
	}

	// Retorna as refs do módulo e as funções que operam nelas
	return {
		allSelected,
		selectedIds,
		excludedIds,
		effectiveSelectedIds,
		isItemSelected,
		selectItem,
		unselectItem,
		selectAll,
		clearSelection,
		toggleSelection,
	};
}

export default useCentralizedSelection;
