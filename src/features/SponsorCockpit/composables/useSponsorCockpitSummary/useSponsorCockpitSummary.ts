import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	queryString,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { builderSponsorCockpitSummary } from '@/features/SponsorCockpit/helpers/builderSponsorCockpitSummary';
import { getSponsorCockpitSummary as getSponsorCockpitSummaryService } from '@/features/SponsorCockpit/services';

type UseSponsorCockpitSummary = {
	isLoadingSponsorCockpitSummary: {
		value: boolean;
	};
	isErrorSponsorCockpitSummary: {
		value: boolean;
	};
	sponsorCockpitSummary: any;
	getSponsorCockpitSummary: Function;
};

export function useSponsorCockpitSummary(operationId: number): UseSponsorCockpitSummary {
	const sponsorCockpitSummary = ref({
		productName: 'Carregando...',
		inAnalysis: 0,
		inAnalysisValue: 0,
		waitingFormalization: 0,
		waitingFormalizationValue: 0,
		formalization: 0,
		formalizationValue: 0,
		readyForDisbursement: 0,
		readyForDisbursementValue: 0,
	});

	const productId = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getSponsorCockpitSummaryService(params, productId),
		onSuccess: data => {
			sponsorCockpitSummary.value = builderSponsorCockpitSummary(data.data.data);
		},
		onError: err => {
			sponsorCockpitSummary.value = {
				productName: '',
				inAnalysis: 0,
				inAnalysisValue: 0,
				waitingFormalization: 0,
				waitingFormalizationValue: 0,
				formalization: 0,
				formalizationValue: 0,
				readyForDisbursement: 0,
				readyForDisbursementValue: 0,
			};
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os dados do sumário: ${error?.message || ''}`
			);
		},
	});

	const isLoadingSponsorCockpitSummary = computed(() => {
		return isLoading.value;
	});

	const isErrorSponsorCockpitSummary = computed(() => {
		return isError.value;
	});

	function getSponsorCockpitSummary(): void {
		const paramsObj = {
			operation_type: operationId,
		};

		const params = queryString(paramsObj, {});

		mutate(params);
	}

	return {
		isLoadingSponsorCockpitSummary,
		isErrorSponsorCockpitSummary,
		sponsorCockpitSummary,
		getSponsorCockpitSummary,
	};
}
