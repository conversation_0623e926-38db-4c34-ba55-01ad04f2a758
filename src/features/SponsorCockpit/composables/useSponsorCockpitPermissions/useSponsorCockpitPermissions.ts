import { computed } from 'vue';
import type { ComputedRef } from 'vue';

import { useStore } from '@/composible';

interface UseSponsorCockpitPermissions {
	canReprocess: ComputedRef<boolean>;
}

export function useSponsorCockpitPermissions(): UseSponsorCockpitPermissions {
	const store = useStore();

	const currentUserRoles = computed(() => store.getters['userAccess/currentUserRoles']);

	const canReprocess = computed(() => {
		return (
			!!currentUserRoles.value &&
			currentUserRoles.value['meusclientes.originacoes_sacados'] === 2
		);
	});

	return {
		canReprocess,
	};
}
