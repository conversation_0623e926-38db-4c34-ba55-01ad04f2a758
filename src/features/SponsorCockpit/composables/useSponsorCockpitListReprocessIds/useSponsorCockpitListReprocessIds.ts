import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	queryString,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import { getSponsorCockpitListReprocessIds as getSponsorCockpitListReprocessIdsService } from '@/features/SponsorCockpit/services';

type UseSponsorCockpitListReprocessIds = {
	isLoadingSponsorCockpitListReprocessIds: { value: boolean };
	isErrorSponsorCockpitListReprocessIds: { value: boolean };
	sponsorCockpitListReprocessIds: { value: Array<any> };
	getSponsorCockpitListReprocessIds: Function;
};

export function useSponsorCockpitListReprocessIds(
	operationId: number
): UseSponsorCockpitListReprocessIds {
	const sponsorCockpitListReprocessIds = ref([]);

	const key_account_id = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getSponsorCockpitListReprocessIdsService(params, key_account_id),
		onSuccess: data => {
			sponsorCockpitListReprocessIds.value = data.data.data;
		},
		onError: err => {
			sponsorCockpitListReprocessIds.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os dados da lista: ${error?.message || ''}`
			);
		},
	});

	const isLoadingSponsorCockpitListReprocessIds = computed(() => {
		return isLoading.value;
	});

	const isErrorSponsorCockpitListReprocessIds = computed(() => {
		return isError.value;
	});

	function getSponsorCockpitListReprocessIds(filters): void {
		const paramsObject = { ...filters, operation_type: operationId };
		if (Array.isArray(paramsObject.status)) {
			paramsObject.status = paramsObject.status.join(',');
		}
		if (Array.isArray(paramsObject.source)) {
			paramsObject.source = paramsObject.source.join(',');
		}
		const params = queryString(paramsObject, {});
		mutate(params);
	}

	return {
		isLoadingSponsorCockpitListReprocessIds,
		isErrorSponsorCockpitListReprocessIds,
		sponsorCockpitListReprocessIds,
		getSponsorCockpitListReprocessIds,
	};
}
