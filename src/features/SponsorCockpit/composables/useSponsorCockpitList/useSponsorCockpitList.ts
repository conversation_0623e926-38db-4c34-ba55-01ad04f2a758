import { ref, computed } from 'vue';

import {
	errorBuilder,
	notification,
	queryString,
	RequestStatusEnum,
} from '@farm-investimentos/front-mfe-libs-ts';
import { useMutation } from '@tanstack/vue-query';

import { useSelectedProductId } from '@/composible';
import {
	builderSponsorCockpitList
} from '@/features/SponsorCockpit/helpers/builderSponsorCockpitList';
import { getSponsorCockpitList  as getSponsorCockpitListService } from '@/features/SponsorCockpit/services';

type UseSponsorCockpitList = {
	isLoadingSponsorCockpitList: {
		value: boolean;
	};
	isErrorSponsorCockpitList: {
		value: boolean;
	};
	sponsorCockpitList: Array<any>;
	sponsorCockpitPagination: any;
	getSponsorCockpitList: Function;
};

export function useSponsorCockpitList(operationId: number): UseSponsorCockpitList {
	const sponsorCockpitList = ref([]);
	const sponsorCockpitPagination = ref(null);

	const key_account_id = useSelectedProductId().value;

	const { isLoading, isError, mutate } = useMutation({
		mutationFn: params => getSponsorCockpitListService(params, key_account_id),
		onSuccess: data => {
			const { content, pagination } = builderSponsorCockpitList(data.data.data);
			sponsorCockpitList.value = content;
			sponsorCockpitPagination.value = pagination;
		},
		onError: err => {
			sponsorCockpitList.value = [];
			const error = errorBuilder(err);
			notification(
				RequestStatusEnum.ERROR,
				`Error ao buscar os dados da lista: ${error?.message || ''}`,
			);
		},
	});

	const isLoadingSponsorCockpitList = computed(() => {
		return isLoading.value;
	});

	const isErrorSponsorCockpitList = computed(() => {
		return isError.value;
	});

	function getSponsorCockpitList(filters): void {
		const paramsObject = { ...filters, operation_type: operationId };
		if (Array.isArray(paramsObject.status)) {
			paramsObject.status = paramsObject.status.join(",");
		}
		if (Array.isArray(paramsObject.source)) {
			paramsObject.source = paramsObject.source.join(",");
		}
		const params = queryString(paramsObject, {});
		mutate(params);
	}

	return {
		isLoadingSponsorCockpitList,
		isErrorSponsorCockpitList,
		sponsorCockpitPagination,
		sponsorCockpitList,
		getSponsorCockpitList,
	};
}
